#%%
# 胡耀天 202455053
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
#%%
# 胡耀天 202455053
df1 = pd.read_excel(r"2024审计学、财务管理学生名单.xlsx")
df2 = pd.read_excel(r"2024审计学、财务管理平时作业成绩统计.xlsx")
df = pd.merge(df1, df2, on=["学号/工号", "班级"], how="left") 
df
#%%
# 胡耀天 202455053
duplicate_mask = df.duplicated(subset=['学号/工号', '班级'], keep=False)#重复值处置
duplicate_count = duplicate_mask.sum()
if duplicate_count > 0:
    print(f" 发现 {duplicate_count} 条重复记录")
    df = df[~duplicate_mask]  
    print(f"已删除重复记录，剩余 {len(df)} 条数据")
else:
    print()
null_report = df.isnull().sum().sort_values(ascending=False)#空值
print()
for col in df.columns:
    if df[col].dtype == 'object':  # 字符串列
        df[col] = df[col].fillna('缺失')
    elif np.issubdtype(df[col].dtype, np.number):
        df[col] = df[col].fillna(0)

score_cols = [col for col in df.columns if '作业' in col]#异常值
for col in score_cols:
    outliers = df[(df[col] < 0) | (df[col] > 100)][col]  
    if not outliers.empty:
        print(f" 作业 {col} 发现 {len(outliers)} 条异常成绩（应0-100）")
        class_avg = df[col].mean()
        df[col] = np.where((df[col] < 0) | (df[col] > 100), class_avg, df[col])
        print(f" 已替换为班级平均值 {class_avg:.2f}")
df['姓名'] = df['姓名'].str.strip().str.upper()  
df['学号/工号'] = df['学号/工号'].astype(str)  
df
#%%
# 胡耀天 202455053
your_id = '202455053'
score_cols = [col for col in df.columns if '作业' in col] # 筛选所有作业成绩列
your_scores = df[df['学号/工号'] == your_id][score_cols].values[0] # 获取个人成绩数据
print(f"\n【个人成绩分析 - {your_id}】")
print(f"平均成绩: {np.mean(your_scores):.2f}")
print(f"最高成绩: {np.max(your_scores)}")
print(f"最低成绩: {np.min(your_scores)}")
#%%
# 胡耀天 202455053
your_class = df[df['学号/工号'] == your_id]['班级'].values[0]# 班级成绩
class_data = df[df['班级'] == your_class]
class_avg = class_data[score_cols].mean().mean()
class_max = class_data[score_cols].max().max()
class_min = class_data[score_cols].min().min()
print(f"\n【班级成绩分析 - {your_class}】")
print(f"班级平均成绩: {class_avg:.2f}")
print(f"班级最高成绩: {class_max}")
print(f"班级最低成绩: {class_min}")
#%%
# 胡耀天 202455053
df['总平均分'] = df[score_cols].mean(axis=1)  # 计算每个学生的总平均分
top_threshold = df['总平均分'].quantile(0.9) # 前10%的分数阈值
bottom_threshold = df['总平均分'].quantile(0.1) # 后10%的分数阈值
top_students = df[df['总平均分'] >= top_threshold][['学号/工号', '姓名', '总平均分']]
bottom_students = df[df['总平均分'] <= bottom_threshold][['学号/工号', '姓名', '总平均分']]
print(f"\n【成绩优秀学生 - 前10%】（阈值: {top_threshold:.2f}）")
print(top_students)
print(f"\n【需要辅导学生 - 后10%】（阈值: {bottom_threshold:.2f}）")
print(bottom_students)
#%%
# 胡耀天 202455053
df['分数段'] = pd.cut(df['总平均分'], bins=[0, 60, 70, 80, 90, 100], 
                     labels=['60分以下',
                             '60-69分',
                             '70-79分', 
                             '80-89分',
                             '90分以上'])
score_bins = df['分数段'].value_counts().sort_index()
print(f"\n【分数段分布】")
print(score_bins)
#%%
# 胡耀天 202455053
difficulty_analysis = pd.DataFrame({
    '作业名称': score_cols,
    '平均分': df[score_cols].mean(),
    '最高分': df[score_cols].max(),
    '最低分': df[score_cols].min(),
    '标准差': df[score_cols].std()
}).sort_values('平均分')  # 按平均分升序排列
print(f"\n【作业难易程度分析】（按平均分升序排列）")
print(difficulty_analysis)
print(f"\n【最难的3个作业】")
print(difficulty_analysis.head())    
#%%
# 胡耀天 202455053
df['总平均分'] = df[[col for col in df.columns if '作业' in col]].mean(axis=1)#每人的平均分
top10_students = df.nlargest(10, '总平均分')  # 选择Top10学生
plt.figure(figsize=(8, 5))  # 创建柱状图
bars = plt.bar(top10_students['姓名'], top10_students['总平均分'], color='purple')
plt.title('班级成绩Top10')
plt.xlabel('学生姓名')
plt.ylabel('总平均分')
plt.xticks(rotation=45)
for bar in bars:       # 添加数值标签
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height,
             f'{height:.1f}', ha='center', va='bottom')
plt.rcParams["font.sans-serif"] = ["KaiTi"]
plt.tight_layout() #调整布局
plt.show()
#%%
# 胡耀天 202455053
score_cols = [col for col in df.columns if '作业' in col]  # 获取作业列
your_id = '202455053'
your_data = df[df['学号/工号'] == your_id]
plt.rcParams["font.sans-serif"] = ["KaiTi"]
if your_data.empty:
    print(f"未找到学号/工号为 {your_id} 的数据，请检查数据或学号是否正确。")
else:
    your_scores = your_data[score_cols].values[0]
    plt.figure(figsize=(8,5))  # 创建图幅
    # 绘制带点的折线图
    plt.plot(score_cols, your_scores, 'o-', color='blue', linewidth=2)
    plt.title(f'{your_id} 个人成绩趋势')
    plt.xlabel('作业')
    plt.ylabel('分数')
    plt.grid(True, linestyle='--', alpha=0.7)  # 添加网格线
    plt.xticks(rotation=45)
    avg_score = np.mean(your_scores)  # 添加平均值参考线
    plt.axhline(y=avg_score, color='r', linestyle='--',
                label=f'平均分: {avg_score:.1f}')  # 添加水平线
    plt.legend()  # 显示图例
    plt.tight_layout()
    plt.show()
#%%
#胡耀天 202455053
df['分数段'] = pd.cut(df['总平均分'], bins=[0, 60, 70, 80, 90, 100], 
                     labels=['60分以下', '60-69分', '70-79分', '80-89分', '90分以上'])# 创建分数段数列
segment_counts = df['分数段'].value_counts()   # 统计各分数段人数
plt.figure(figsize=(8, 5)) # 创建画幅
plt.pie(segment_counts, labels=segment_counts.index, autopct='%1.1f%%',  # 显示百分比
        startangle=90,  # 设置起始角度
        colors=['gray', 'orange', 'yellow', 'green', 'blue'])  # 自定义颜色
plt.title('班级分数段分布')
plt.axis('equal')  # 饼图是很圆的圆形
plt.tight_layout()
plt.show()
#%%
# 胡耀天 202455053
score_cols = [col for col in df.columns if '作业' in col]
class_avg = df.groupby('班级')[score_cols].mean().T 
# 按班级分组计算各作业平均分               
plt.figure(figsize=(14, 6))# 创建画幅
class_avg.plot(kind='bar', ax=plt.gca(), width=0.5) #一些小细节
plt.title('不同班级各作业平均成绩对比')
plt.xlabel('作业')
plt.ylabel('平均分')
plt.legend(title='班级')
plt.tight_layout()
plt.show()
#%%
# 胡耀天 202455053
import matplotlib.pyplot as plt
from matplotlib import gridspec
your_id = '202455053'

df['总平均分'] = df[score_cols].mean(axis=1)# 计算统计数据
your_scores = df[df['学号/工号'] == your_id][score_cols].values[0]
top10_students = df.nlargest(10, '总平均分')
df['分数段'] = pd.cut(df['总平均分'], bins=[0,60,70,80,90,100], 
                     labels=['60分以下', '60-69分', '70-79分', '80-89分', '90分以上'])
segment_counts = df['分数段'].value_counts()
class_avg = df.groupby('班级')[score_cols].mean().T


fig = plt.figure(figsize=(20,26))# 创建画布和网格布局
gs = gridspec.GridSpec(2, 2, wspace=0.25, hspace=0.3)
ax1 = fig.add_subplot(gs[0, 0]) # 个人成绩趋势图         
ax1.plot(score_cols, your_scores, 'o-', color='blue', linewidth=2)
ax1.set(title=f'{your_id} 个人成绩趋势', xlabel='作业', ylabel='分数')
ax1.grid(True, linestyle='--', alpha=0.7)
ax1.tick_params(axis='x', rotation=45)
ax1.axhline(y=np.mean(your_scores), color='r', linestyle='--', 
            label=f'平均分: {np.mean(your_scores):.1f}')
ax1.legend(loc='upper right')
ax2 = fig.add_subplot(gs[0, 1])#班级成绩Top10柱状图
bars = ax2.bar(top10_students['姓名'], top10_students['总平均分'], color='purple')
ax2.set(title='班级成绩Top10', xlabel='学生姓名', ylabel='总平均分')
ax2.tick_params(axis='x', rotation=45)
for bar in bars:
    ax2.text(bar.get_x()+bar.get_width()/2, bar.get_height(), 
             f'{bar.get_height():.1f}', ha='center', va='bottom', fontsize=9)
ax3 = fig.add_subplot(gs[1, 0])#班级分数段饼图
ax3.pie(segment_counts, labels=segment_counts.index, autopct='%1.1f%%',
        startangle=90, colors=['gray', 'orange', 'yellow', 'green', 'blue'])
ax3.set_title('班级分数段分布')
box = ax3.get_position()
ax3.set_position([box.x0, box.y0 - 0.5, box.width, box.height])  # 向下移动0.05
ax3.axis('equal')
ax4 = fig.add_subplot(gs[1, 1])#班级平均成绩对比图
class_avg.plot(kind='bar', ax=ax4, width=0.8)
ax4.set(title='不同班级各作业平均成绩对比', xlabel='作业', ylabel='平均分')
ax4.legend(title='班级', loc='upper right')
ax4.tick_params(axis='x', rotation=45)
plt.suptitle('班级成绩综合分析报告', fontsize=18, y=0.96)#标题
plt.subplots_adjust(top=0.9, bottom=0.07, left=0.07, right=0.9, hspace=0.3, wspace=0.2)
plt.show()
#%%
