import math

class UltrasonicFlowmeter:
    """
    一个用于模拟和计算超声波流量计的类。
    """
    def __init__(self, diameter: float, path_length: float, angle_deg: float):
        """
        初始化流量计的几何参数。

        :param diameter: 管道直径 D (米)
        :param path_length: 超声波传播距离 L (米)
        :param angle_deg: 超声波路径与管道直径的夹角 θ (度)
        """
        if not (diameter > 0 and path_length > 0):
            raise ValueError("直径和传播距离必须为正数。")
        if not (0 < angle_deg < 90):
            raise ValueError("角度必须在 0 到 90 度之间。")
            
        self.D = diameter
        self.L = path_length
        self.theta = math.radians(angle_deg)  # 将角度转换为弧度
        self.A = math.pi * (self.D / 2)**2

    def calculate_velocity_problem1(self, time_diff: float, sound_speed: float) -> float:
        """
        问题1：在已知声速的情况下，通过时差计算流速。

        :param time_diff: 顺流和逆流的时间差 Δt (秒)
        :param sound_speed: 流体中的声速 c (米/秒)
        :return: 流体速度 v (米/秒)
        """
        if sound_speed <= 0:
            raise ValueError("声速必须为正数。")
        
        # v ≈ (c^2 * Δt) / (2 * L * sin(θ))
        numerator = sound_speed**2 * time_diff
        denominator = 2 * self.L * math.sin(self.theta)
        
        if denominator == 0:
            return 0.0

        return numerator / denominator

    def calculate_flow_volume(self, velocity: float, time_period: float) -> float:
        """
        计算在给定时间段内流过的流体总体积。

        :param velocity: 流体速度 v (米/秒)
        :param time_period: 时间段 T (秒)
        :return: 流体总体积 (立方米)
        """
        if time_period < 0:
            raise ValueError("时间段不能为负。")
        
        # 体积 = 速度 * 截面积 * 时间
        return velocity * self.A * time_period

    def estimate_sound_speed(self, t_ab: float, t_ba: float) -> float:
        """
        通过顺流和逆流时间估算流体中的声速。

        :param t_ab: 从 A 到 B 的顺流时间 (秒)
        :param t_ba: 从 B 到 A 的逆流时间 (秒)
        :return: 估算的声速 c (米/秒)
        """
        if t_ab <= 0 or t_ba <= 0:
            raise ValueError("传播时间必须为正数。")
        
        # c = (L / 2) * (t_ab + t_ba) / (t_ab * t_ba)
        return (self.L / 2.0) * (t_ab + t_ba) / (t_ab * t_ba)

    def calculate_velocity_problem2(self, t_ab: float, t_ba: float) -> float:
        """
        问题2：在未知声速的情况下，通过 t_ab 和 t_ba 计算流速（修正模型）。

        :param t_ab: 从 A 到 B 的顺流时间 (秒)
        :param t_ba: 从 B 到 A 的逆流时间 (秒)
        :return: 流体速度 v (米/秒)
        """
        if t_ab <= 0 or t_ba <= 0:
            raise ValueError("传播时间必须为正数。")
        
        time_diff = t_ba - t_ab
        
        # v = (L / (2 * sin(θ))) * Δt / (t_ab * t_ba)
        numerator = self.L * time_diff
        denominator = 2 * math.sin(self.theta) * t_ab * t_ba

        if denominator == 0:
            return 0.0
        
        return numerator / denominator
    
    @staticmethod
    def get_sound_speed_in_water(temperature: float) -> float:
        """
        根据温度计算在水中的声速 (基于标准经验公式)。

        :param temperature: 水温 T (摄氏度)
        :return: 水中的声速 c (米/秒)
        """
        # 一个简化的多项式模型，适用于 0-100°C
        T = temperature
        c = (1402.388 + 5.03711 * T - 0.0580852 * T**2 + 
             0.00033163 * T**3 - 1.47800e-6 * T**4 + 3.1464e-9 * T**5)
        return c

    def estimate_temperature(self, sound_speed: float, temp_range=(0, 100)) -> float:
        """
        根据声速估算水的温度。

        :param sound_speed: 估算出的声速 c (米/秒)
        :param temp_range: 可能的温度范围 (摄氏度)
        :return: 估算出的水温 T (摄氏度)
        """
        best_temp = -1
        min_diff = float('inf')

        # 在温度范围内以 0.01 度为步长进行搜索 (原为0.1度)
        for temp_check in range(temp_range[0] * 100, temp_range[1] * 100):
            temp = temp_check / 100.0  # 将整数转换为浮点数温度值 (原为/10.0)
            c_theoretical = self.get_sound_speed_in_water(temp)
            diff = abs(sound_speed - c_theoretical)
            
            if diff < min_diff:
                min_diff = diff
                best_temp = temp
                
        return best_temp 