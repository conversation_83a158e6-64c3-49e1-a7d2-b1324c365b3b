import numpy as np
import matplotlib
matplotlib.use('Agg')  # 设置后端为Agg
import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = <PERSON>als<PERSON>

def calculate_eci(wlr, gp):
    """
    计算生态补偿综合指标 (ECI)
    参数:
    wlr: 兽畜比例 (Wild-to-Livestock Ratio)
    gp: 放牧压力 (Grazing Pressure)
    """
    # 根据补偿逻辑设计权重
    # WLR高&GP高 → 最高补偿 (权重最大)
    # WLR低&GP高 → 适中补偿
    # WLR高&GP低 → 极低补偿
    # WLR低&GP低 → 无需补偿
    
    # 将WLR和GP标准化到[0,1]区间
    wlr_norm = (wlr - np.min(wlr)) / (np.max(wlr) - np.min(wlr))
    gp_norm = (gp - np.min(gp)) / (np.max(gp) - np.min(gp))
    
    # ECI计算公式：结合WLR和GP，并根据补偿逻辑赋予权重
    eci = 0.6 * wlr_norm + 0.4 * gp_norm
    
    return eci

# 生成示例数据
np.random.seed(42)
n_points = 100
wlr = np.random.normal(0.5, 0.2, n_points)
gp = np.random.normal(0.5, 0.2, n_points)

# 计算ECI
eci = calculate_eci(wlr, gp)

# 创建四象限图
plt.figure(figsize=(12, 10))

# 绘制散点图
scatter = plt.scatter(wlr, gp, c=eci, cmap='RdYlGn_r', s=100, alpha=0.6)
plt.colorbar(scatter, label='生态补偿综合指标 (ECI)')

# 添加中心线
plt.axhline(y=np.mean(gp), color='gray', linestyle='--', alpha=0.5)
plt.axvline(x=np.mean(wlr), color='gray', linestyle='--', alpha=0.5)

# 设置坐标轴标签
plt.xlabel('兽畜比例 (WLR)')
plt.ylabel('放牧压力 (GP)')

# 添加象限标注
wlr_mean = np.mean(wlr)
gp_mean = np.mean(gp)

# 第一象限：右上 (WLR高 & GP高)
plt.text(wlr_mean + (np.max(wlr) - wlr_mean)/2, 
         gp_mean + (np.max(gp) - gp_mean)/2,
         '亟需生态补偿\n(高补偿区)',
         ha='center', va='center', bbox=dict(facecolor='white', alpha=0.8))

# 第二象限：左上 (WLR低 & GP高)
plt.text(wlr_mean - (wlr_mean - np.min(wlr))/2,
         gp_mean + (np.max(gp) - gp_mean)/2,
         '适当补偿\n(中补偿区)',
         ha='center', va='center', bbox=dict(facecolor='white', alpha=0.8))

# 第三象限：左下 (WLR低 & GP低)
plt.text(wlr_mean - (wlr_mean - np.min(wlr))/2,
         gp_mean - (gp_mean - np.min(gp))/2,
         '无需补偿\n(零补偿区)',
         ha='center', va='center', bbox=dict(facecolor='white', alpha=0.8))

# 第四象限：右下 (WLR高 & GP低)
plt.text(wlr_mean + (np.max(wlr) - wlr_mean)/2,
         gp_mean - (gp_mean - np.min(gp))/2,
         '极低或无需补偿\n(低补偿区)',
         ha='center', va='center', bbox=dict(facecolor='white', alpha=0.8))

# 添加标题
plt.title('生态补偿综合指标(ECI)四象限分析', pad=20, size=14)

# 添加图例说明
legend_text = '''补偿逻辑说明：
1. WLR高 & GP高 → 亟需生态补偿
2. WLR低 & GP高 → 适当补偿
3. WLR低 & GP低 → 无需补偿
4. WLR高 & GP低 → 极低或无需补偿'''

plt.text(1.2, 0.98, legend_text,
         transform=plt.gca().transAxes,
         bbox=dict(facecolor='white', edgecolor='gray', alpha=0.8),
         verticalalignment='top')

# 调整布局
plt.tight_layout()

try:
    # 保存图片
    plt.savefig('ecological_compensation_analysis.png', dpi=300, bbox_inches='tight', format='png')
except Exception as e:
    print(f"保存图片时出错: {e}")
finally:
    plt.close()  # 确保图形被关闭

# 输出统计信息
print("\n=== 生态补偿分析统计信息 ===")
print(f"数据点总数: {n_points}")
print(f"平均ECI: {np.mean(eci):.3f}")
print(f"最大ECI: {np.max(eci):.3f}")
print(f"最小ECI: {np.min(eci):.3f}")
print(f"ECI标准差: {np.std(eci):.3f}")

# 计算各象限的点数
q1 = np.sum((wlr > wlr_mean) & (gp > gp_mean))  # 第一象限
q2 = np.sum((wlr <= wlr_mean) & (gp > gp_mean))  # 第二象限
q3 = np.sum((wlr <= wlr_mean) & (gp <= gp_mean))  # 第三象限
q4 = np.sum((wlr > wlr_mean) & (gp <= gp_mean))  # 第四象限

print("\n各补偿区域数据点分布：")
print(f"亟需生态补偿区 (高补偿区): {q1}点 ({q1/n_points*100:.1f}%)")
print(f"适当补偿区 (中补偿区): {q2}点 ({q2/n_points*100:.1f}%)")
print(f"无需补偿区 (零补偿区): {q3}点 ({q3/n_points*100:.1f}%)")
print(f"极低或无需补偿区 (低补偿区): {q4}点 ({q4/n_points*100:.1f}%)") 