import pygame
from pygame.sprite import Sprite

class Alien(Sprite):
    """表示单个外星人的类"""
    
    def __init__(self, ai_game):
        """初始化外星人并设置其起始位置"""
        super().__init__()
        self.screen = ai_game.screen
        self.settings = ai_game.settings
        
        # 加载外星人图像并设置其rect属性
        self.image = pygame.image.load('images/alien.bmp')
        # 翻转图像，让敌人飞船朝下
        self.image = pygame.transform.flip(self.image, False, True)
        self.rect = self.image.get_rect()
        
        # 每个外星人最初都在屏幕顶部随机位置
        self.rect.x = self.rect.width
        self.rect.y = self.rect.height
        
        # 存储外星人的精确位置
        self.x = float(self.rect.x)
        self.y = float(self.rect.y)
            
    def update(self):
        """向下移动外星人"""
        # 更新表示外星人位置的小数值
        self.y += self.settings.alien_speed
        # 更新rect位置
        self.rect.y = self.y 