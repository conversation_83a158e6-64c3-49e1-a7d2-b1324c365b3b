#%%
# 胡灏迪202456242
import pandas as pd
import warnings
import matplotlib.pyplot as plt
# 忽略所有警告信息
warnings.filterwarnings('ignore')
# 设置中文字体，避免中文显示乱码
plt.rcParams['font.sans-serif'] = ['SimHei']  
plt.rcParams['axes.unicode_minus'] = False  

#%%
# 胡灏迪202456242
# 读取学生信息表
student_info = pd.read_excel('2024审计学、财务管理学生名单.xlsx')
print("学生信息表的形状：", student_info.shape)
print("学生信息表的前5行：")
print(student_info.head())

#%%
# 胡灏迪202456242
# 读取作业完成情况表
homework_scores = pd.read_excel('2024审计学、财务管理平时作业成绩统计.xlsx')
print("作业完成情况表的形状：", homework_scores.shape)
print("作业完成情况表的前5行：")
print(homework_scores.head())

#%%
# 胡灏迪202456242
# "学号/工号"列作为合并键
merged_data = pd.merge(student_info, homework_scores, on=['学号/工号'], how='inner')

#%%
# 胡灏迪202456242
# 查看合并后的数据
print("合并后的数据表的形状：", merged_data.shape)
print("合并后的数据表的前5行：")
print(merged_data.head())

#%% md
# # ## 数据清洗
#%%
# 胡灏迪202456242
# 检查重复行
duplicate_rows = merged_data.duplicated()
print("是否存在重复行：", duplicate_rows.any())
if duplicate_rows.any():
    print("重复行数量：", duplicate_rows.sum())
    print("重复行索引：", merged_data[duplicate_rows].index.tolist())

#%%
# 胡灏迪202456242
# 检查空值
null_values = merged_data.isnull()
print("是否存在空值：", null_values.any().any())
if null_values.any().any():
    print("每列的空值数量：")
    print(null_values.sum())
    
    # 查看哪些行包含空值
    rows_with_nulls = merged_data[merged_data.isnull().any(axis=1)]
    print("包含空值的行数：", len(rows_with_nulls))
    if len(rows_with_nulls) > 0:
        print("包含空值的行：")
        print(rows_with_nulls)

#%%
# 胡灏迪202456242
#空值填充为0分
merged_data = merged_data.fillna(0)

#%%
# 胡灏迪202456242
# 验证是否还有空值
print("填充后是否还有空值：", merged_data.isnull().any().any())

#%%
# 胡灏迪202456242
# 分数列应该在0-100之间
numeric_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns
for col in numeric_columns:
    if col not in ['学号/工号','学校代码']: 
        min_val = merged_data[col].min()
        max_val = merged_data[col].max()
        print(f"列 '{col}' 的值范围: {min_val} - {max_val}")
        
        # 检查是否有超出合理范围的值
        if min_val < 0 or max_val > 100:
            print(f"警告: 列 '{col}' 中存在可能的异常值!")
            # 显示异常值
            abnormal_values = merged_data[(merged_data[col] < 0) | (merged_data[col] > 100)]
            if len(abnormal_values) > 0:
                print("异常值所在行：")
                print(abnormal_values)

#%%
# 胡灏迪202456242 
# 保存清洗后的数据
merged_data.to_excel('清洗后的综合数据表.xlsx', index=False)
print("数据清洗完成，已保存到'清洗后的综合数据表.xlsx'") 

#%% md
# # # （二）数据分析
# # 利用Pandas库对整合后的数据进行统计分析
# 
#%% md
# # ## 1. 个人学习成绩分析
# # ### （1）计算本人的作业平均成绩、最高成绩、最低成绩
#%%
# 胡灏迪202456242
my_student_id = 202456242
my_data = merged_data[merged_data['学号/工号'] == my_student_id]

if len(my_data) == 0:
    print(f"未找到学号为 {my_student_id} 的学生数据")
else:
    print(f"学号为 {my_student_id} 的学生信息：")
    print(my_data[['学号/工号', '姓名']].iloc[0])
    
    print("\n所有列名：")
    print(merged_data.columns.tolist())
    
    score_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns.tolist()
    score_columns = [col for col in score_columns if col not in ['学号/工号', '学校代码']]
    
    print("\n作业成绩列：")
    print(score_columns)
    
    my_scores = my_data[score_columns]
    print("\n本人各次作业成绩：")
    print(my_scores)
    
    # 计算平均成绩、最高成绩、最低成绩
    avg_score = my_scores.mean(axis=1).iloc[0]
    max_score = my_scores.max(axis=1).iloc[0]
    min_score = my_scores.min(axis=1).iloc[0]
    
    print(f"\n本人成绩统计：")
    print(f"平均成绩: {avg_score:.2f}")
    print(f"最高成绩: {max_score:.2f}")
    print(f"最低成绩: {min_score:.2f}")

#%% md
# # ## 2. 班级学习数据分析
# # ### （1）分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少
#%%
# 胡灏迪202456242
my_class = my_data['班级_x'].iloc[0]
print(f"学生 {my_data['姓名'].iloc[0]} 所在的班级是: {my_class}")

# 筛选出同一班级的学生
class_students = merged_data[merged_data['班级_x'] == my_class]
print(f"班级 {my_class} 共有 {len(class_students)} 名学生")
# 计算每个学生的平均分
student_avg_scores = class_students[score_columns].mean(axis=1)
class_students['平均分'] = student_avg_scores
merged_data.loc[class_students.index, '平均分'] = student_avg_scores

# 查看班级平均分的统计信息
class_avg = student_avg_scores.mean()
class_max = student_avg_scores.max()
class_min = student_avg_scores.min()

print("班级成绩统计：")
print(f"班级平均分: {class_avg:.2f}")
print(f"班级最高成绩: {class_max:.2f}")
print(f"班级最低成绩: {class_min:.2f}")

#%% md
# # ### （2）查找成绩优秀或需要辅导的学生（top10和tail10）
#%%
# 胡灏迪202456242
# 按平均分排序，找出班级内成绩最好的前10名学生
top_n = min(10, len(class_students))
top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)
print(f"班级 {my_class} 成绩优秀的前{top_n}名学生：")
print(top_students[['学号/工号', '姓名', '平均分']])

# 找出班级内成绩最差的后10名学生
bottom_students = class_students.sort_values(by='平均分').head(top_n)
print(f"\n班级 {my_class} 需要辅导的后{top_n}名学生：")
print(bottom_students[['学号/工号', '姓名', '平均分']])

#%% md
# # ### （3）分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布
#%%
# 胡灏迪202456242
# 定义分数段
def get_score_range(score):
    if score < 60:
        return '60分以下'
    elif 60 <= score < 70:
        return '60-69分'
    elif 70 <= score < 80:
        return '70-79分'
    elif 80 <= score < 90:
        return '80-89分'
    else:
        return '90分以上'

# 应用分数段函数到班级学生
class_students['分数段'] = class_students['平均分'].apply(get_score_range)
merged_data.loc[class_students.index, '分数段'] = class_students['分数段']

# 统计班级内各分数段的学生人数
score_range_counts = class_students['分数段'].value_counts().sort_index()
print(f"班级 {my_class} 各分数段学生人数分布：")
print(score_range_counts)

# 计算班级内各分数段的百分比
score_range_percentage = score_range_counts / len(class_students) * 100
print(f"\n班级 {my_class} 各分数段学生人数百分比：")
for range_name, percentage in score_range_percentage.items():
    print(f"{range_name}: {percentage:.2f}%")

#%% md
# # ## 3. 数据可视化
# # 使用matplotlib库对分析结果进行可视化展示
#%%
# 胡灏迪202456242
from matplotlib.gridspec import GridSpec
#%%
# 胡灏迪202456242
# 1. 绘制班级成绩Top10的柱状图
plt.figure(figsize=(12, 6))
top_n = min(10, len(class_students))
top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)
bars = plt.bar(top_students['姓名'], top_students['平均分'], color='skyblue')
plt.title(f'班级 {my_class} 成绩Top{top_n}学生')
plt.xlabel('学生姓名')
plt.ylabel('平均分')
plt.ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.xticks(rotation=45, ha='right')

# 在柱状图上添加具体分数
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
            f'{height:.1f}', ha='center', va='bottom')
            
plt.tight_layout()
plt.savefig('班级成绩Top10柱状图.png')
plt.show()
#%%
# 胡灏迪202456242
# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）
plt.figure(figsize=(12, 6))
my_scores_values = my_scores.iloc[0].values
plt.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)
plt.title(f'学号 {my_student_id} ({my_data["姓名"].iloc[0]}) 的成绩趋势图')
plt.xlabel('作业序号')
plt.ylabel('分数')
plt.xticks(range(len(score_columns)), [f'作业{i+1}' for i in range(len(score_columns))], rotation=45)
plt.grid(True)
plt.ylim(0, 100)  # 设置y轴范围为0-100

# 在趋势图上添加具体分数
for i, score in enumerate(my_scores_values):
    plt.text(i, score + 2, f'{score:.1f}', ha='center')
    
plt.tight_layout()
plt.savefig('个人成绩趋势图.png')
plt.show()

#%%
# 胡灏迪202456242
# 3. 绘制本班级不同分数段比例图（饼图）
plt.figure(figsize=(10, 8))
# 确保分数段是按照正确顺序排列的
ordered_ranges = ['60分以下', '60-69分', '70-79分', '80-89分', '90分以上']
ordered_counts = [score_range_counts.get(range_name, 0) for range_name in ordered_ranges]

# 定义饼图颜色
colors = ['#FF9999', '#FFCC99', '#FFFF99', '#99FF99', '#99CCFF']
explode = (0.1, 0, 0, 0, 0)  # 突出显示60分以下的部分

wedges, texts, autotexts = plt.pie(
    ordered_counts, 
    labels=ordered_ranges, 
    autopct='%1.1f%%', 
    startangle=90, 
    colors=colors,
    explode=explode,
    shadow=True
)
plt.title(f'班级 {my_class} 不同分数段学生比例')
plt.axis('equal')  # 保证饼图是圆形的

# 设置饼图文本样式
plt.setp(autotexts, size=10, weight="bold")

plt.tight_layout()
plt.savefig('班级分数段比例饼图.png')
plt.show()

#%%
# 胡灏迪202456242
# 4. 按班级分组，绘制两个班的平均成绩对比图
plt.figure(figsize=(10, 6))

# 获取所有班级
all_classes = merged_data['班级_x'].unique()
class_avg_scores = []

# 计算每个班级的平均分
for cls in all_classes:
    cls_students = merged_data[merged_data['班级_x'] == cls]
    cls_avg = cls_students[score_columns].mean().mean()
    class_avg_scores.append({'班级': cls, '平均分': cls_avg})

# 创建班级平均分数据框
class_avg_df = pd.DataFrame(class_avg_scores)

# 绘制班级平均分对比条形图
colors = ['#66B2FF' if cls != my_class else '#FF9900' for cls in class_avg_df['班级']]
bars = plt.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)
plt.title('不同班级平均成绩对比')
plt.xlabel('班级')
plt.ylabel('平均分')
plt.ylim(0, 100)
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 在条形图上添加具体分数
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
            f'{height:.2f}', ha='center', va='bottom')

plt.tight_layout()
plt.savefig('班级平均成绩对比图.png')
plt.show()

#%%
# 胡灏迪202456242
# 5. 将所有图表整合到一张完整的图纸中
plt.figure(figsize=(20, 15))
gs = GridSpec(3, 2, figure=plt.gcf())

# 1. 绘制班级成绩Top10的柱状图
ax1 = plt.subplot(gs[0, 0])
bars = ax1.bar(top_students['姓名'], top_students['平均分'], color='skyblue')
ax1.set_title(f'班级 {my_class} 成绩Top{top_n}学生')
ax1.set_xlabel('学生姓名')
ax1.set_ylabel('平均分')
ax1.set_ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围
ax1.grid(axis='y', linestyle='--', alpha=0.7)
plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')

# 在柱状图上添加具体分数
for bar in bars:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
            f'{height:.1f}', ha='center', va='bottom')

# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）
ax2 = plt.subplot(gs[0, 1])
ax2.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)
ax2.set_title(f'学号 {my_student_id} ({my_data["姓名"].iloc[0]}) 的成绩趋势图')
ax2.set_xlabel('作业序号')
ax2.set_ylabel('分数')
ax2.set_xticks(range(len(score_columns)))
ax2.set_xticklabels([f'作业{i+1}' for i in range(len(score_columns))], rotation=45)
ax2.grid(True)
ax2.set_ylim(0, 100)  # 设置y轴范围为0-100

# 在趋势图上添加具体分数
for i, score in enumerate(my_scores_values):
    ax2.text(i, score + 2, f'{score:.1f}', ha='center')

# 3. 绘制本班级不同分数段比例图（饼图）
ax3 = plt.subplot(gs[1, 0])
wedges, texts, autotexts = ax3.pie(
    ordered_counts, 
    labels=ordered_ranges, 
    autopct='%1.1f%%', 
    startangle=90, 
    colors=colors,
    explode=explode,
    shadow=True
)
ax3.set_title(f'班级 {my_class} 不同分数段学生比例')
ax3.axis('equal')  # 保证饼图是圆形的

# 设置饼图文本样式
plt.setp(autotexts, size=10, weight="bold")

# 4. 按班级分组，绘制两个班的平均成绩对比图
ax4 = plt.subplot(gs[1, 1])
bars = ax4.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)
ax4.set_title('不同班级平均成绩对比')
ax4.set_xlabel('班级')
ax4.set_ylabel('平均分')
ax4.set_ylim(0, 100)
ax4.grid(axis='y', linestyle='--', alpha=0.7)

# 在条形图上添加具体分数
for bar in bars:
    height = bar.get_height()
    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
            f'{height:.2f}', ha='center', va='bottom')


# 添加总标题
plt.suptitle('学生成绩分析报告', fontsize=20, fontweight='bold')

# 调整子图布局
plt.tight_layout(rect=[0, 0, 1, 0.96])  # 为顶部标题留出空间

# 保存整合后的图表
plt.savefig('学生成绩分析报告.png', dpi=300, bbox_inches='tight')
plt.show() 
#%%
