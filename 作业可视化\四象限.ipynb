{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-21T13:48:47.041229Z", "start_time": "2025-06-21T13:48:46.800135Z"}}, "source": ["import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "\n", "# Step 1: 模拟一些 WLR 和 GP 数据（也可以替换为真实数据）\n", "np.random.seed(42)\n", "n_samples = 200\n", "data = {\n", "    'WLR': np.random.uniform(0, 10, n_samples),\n", "    'GP': np.random.uniform(0, 10, n_samples)\n", "}\n", "df = pd.DataFrame(data)\n", "\n", "# Step 2: 设置高低阈值（可根据实际业务需求调整）\n", "wlr_threshold = df['WLR'].median()\n", "gp_threshold = df['GP'].median()\n", "\n", "# Step 3: 划分象限并定义补偿建议\n", "def determine_compensation(row):\n", "    if row['WLR'] > wlr_threshold and row['GP'] > gp_threshold:\n", "        return '亟需补偿'\n", "    elif row['WLR'] > wlr_threshold and row['GP'] <= gp_threshold:\n", "        return '极低或无需补偿'\n", "    elif row['WLR'] <= wlr_threshold and row['GP'] <= gp_threshold:\n", "        return '无需补偿'\n", "    else:\n", "        return '适当补偿'\n", "\n", "df['Compensation'] = df.apply(determine_compensation, axis=1)\n", "\n", "# Step 4: 绘制四象限图\n", "plt.figure(figsize=(8, 6))\n", "\n", "# 设置颜色映射\n", "color_map = {\n", "    '亟需补偿': 'red',\n", "    '适当补偿': 'orange',\n", "    '极低或无需补偿': 'green',\n", "    '无需补偿': 'blue'\n", "}\n", "\n", "for label, color in color_map.items():\n", "    subset = df[df['Compensation'] == label]\n", "    plt.scatter(subset['WLR'], subset['GP'], c=color, label=label, alpha=0.7)\n", "\n", "# 添加阈值线\n", "plt.axvline(wlr_threshold, color='gray', linestyle='--')\n", "plt.axhline(gp_threshold, color='gray', linestyle='--')\n", "\n", "# 添加标签和标题\n", "plt.xlabel('Wild-to-Livestock Ratio (WLR)')\n", "plt.ylabel('Grazing Pressure (GP)')\n", "plt.title('四象限图：生态补偿决策依据')\n", "plt.legend(title='补偿建议')\n", "plt.grid(True)\n", "plt.tight_layout()\n", "plt.show()"], "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 22235 (\\N{CJK UNIFIED IDEOGRAPH-56DB}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 35937 (\\N{CJK UNIFIED IDEOGRAPH-8C61}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 38480 (\\N{CJK UNIFIED IDEOGRAPH-9650}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 65306 (\\N{FULLWIDTH COLON}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 29983 (\\N{CJK UNIFIED IDEOGRAPH-751F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 24577 (\\N{CJK UNIFIED IDEOGRAPH-6001}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 34917 (\\N{CJK UNIFIED IDEOGRAPH-8865}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 20607 (\\N{CJK UNIFIED IDEOGRAPH-507F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 20915 (\\N{CJK UNIFIED IDEOGRAPH-51B3}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 31574 (\\N{CJK UNIFIED IDEOGRAPH-7B56}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 20381 (\\N{CJK UNIFIED IDEOGRAPH-4F9D}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 25454 (\\N{CJK UNIFIED IDEOGRAPH-636E}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 24314 (\\N{CJK UNIFIED IDEOGRAPH-5EFA}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 35758 (\\N{CJK UNIFIED IDEOGRAPH-8BAE}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 20127 (\\N{CJK UNIFIED IDEOGRAPH-4E9F}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 38656 (\\N{CJK UNIFIED IDEOGRAPH-9700}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 36866 (\\N{CJK UNIFIED IDEOGRAPH-9002}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 24403 (\\N{CJK UNIFIED IDEOGRAPH-5F53}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 26497 (\\N{CJK UNIFIED IDEOGRAPH-6781}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 20302 (\\N{CJK UNIFIED IDEOGRAPH-4F4E}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 25110 (\\N{CJK UNIFIED IDEOGRAPH-6216}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_60080\\3980725281.py:56: UserWarning: Glyph 26080 (\\N{CJK UNIFIED IDEOGRAPH-65E0}) missing from font(s) Arial.\n", "  plt.tight_layout()\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 22235 (\\N{CJK UNIFIED IDEOGRAPH-56DB}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 35937 (\\N{CJK UNIFIED IDEOGRAPH-8C61}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 38480 (\\N{CJK UNIFIED IDEOGRAPH-9650}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 22270 (\\N{CJK UNIFIED IDEOGRAPH-56FE}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 65306 (\\N{FULLWIDTH COLON}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 29983 (\\N{CJK UNIFIED IDEOGRAPH-751F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 24577 (\\N{CJK UNIFIED IDEOGRAPH-6001}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 34917 (\\N{CJK UNIFIED IDEOGRAPH-8865}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20607 (\\N{CJK UNIFIED IDEOGRAPH-507F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20915 (\\N{CJK UNIFIED IDEOGRAPH-51B3}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 31574 (\\N{CJK UNIFIED IDEOGRAPH-7B56}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20381 (\\N{CJK UNIFIED IDEOGRAPH-4F9D}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 25454 (\\N{CJK UNIFIED IDEOGRAPH-636E}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 24314 (\\N{CJK UNIFIED IDEOGRAPH-5EFA}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 35758 (\\N{CJK UNIFIED IDEOGRAPH-8BAE}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20127 (\\N{CJK UNIFIED IDEOGRAPH-4E9F}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 38656 (\\N{CJK UNIFIED IDEOGRAPH-9700}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 36866 (\\N{CJK UNIFIED IDEOGRAPH-9002}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 24403 (\\N{CJK UNIFIED IDEOGRAPH-5F53}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 26497 (\\N{CJK UNIFIED IDEOGRAPH-6781}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 20302 (\\N{CJK UNIFIED IDEOGRAPH-4F4E}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 25110 (\\N{CJK UNIFIED IDEOGRAPH-6216}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\IPython\\core\\pylabtools.py:170: UserWarning: Glyph 26080 (\\N{CJK UNIFIED IDEOGRAPH-65E0}) missing from font(s) Arial.\n", "  fig.canvas.print_figure(bytes_io, **kw)\n"]}, {"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 1}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "42fb85ee7866c60b"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}