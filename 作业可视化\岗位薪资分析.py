#%%
#岗位薪资数据分析与建模 
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
import re
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.pipeline import Pipeline
from sklearn.linear_model import LinearRegression, Ridge, Lasso, ElasticNet
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
import joblib

warnings.filterwarnings('ignore')
# 设置中文字体，避免中文显示乱码
plt.rcParams['font.sans-serif'] = ['SimHei']  
plt.rcParams['axes.unicode_minus'] = False  

#%% md
# # # 1. 数据加载与预处理
#%%
# 读取岗位数据
job_data = pd.read_excel('job2020.xlsx')
print("岗位数据表的形状：", job_data.shape)
print(job_data.head())

#%%
# 检查数据的基本信息
print("数据基本信息：")
print(job_data.info())
print("\n数据统计描述：")
print(job_data.describe(include='all'))

#%%
# 检查空值
null_values = job_data.isnull()
print("是否存在空值：", null_values.any().any())
if null_values.any().any():
    print("各列空值数量：")
    print(null_values.sum())
    # 直接删除包含空值的行
    job_data = job_data.dropna()
    print("删除空值后的数据形状：", job_data.shape)

#%%
# 检查重复行
duplicate_rows = job_data.duplicated()
print("是否存在重复行：", duplicate_rows.any())
if duplicate_rows.any():
    print("重复行数量：", duplicate_rows.sum())
    # 删除重复行
    job_data = job_data.drop_duplicates()
    print("删除重复行后的数据形状：", job_data.shape)

#%% md
# # # 2. 工资数据格式转换
#%%
# 定义函数将工资格式转换为最低工资和最高工资
def extract_salary_range(salary_str):
    """
    将工资字符串转换为最低工资和最高工资（月薪，单位K）以及薪资倍数
    示例：'25-40K·15薪' -> (25, 40, 15) -> 月薪范围为25K-40K，年发15个月
    """
    try:
        # 检查是否为空值
        if pd.isna(salary_str):
            return np.nan, np.nan, np.nan
            
        # 提取薪资范围和薪资倍数
        match = re.search(r'(\d+)-(\d+)K(?:·(\d+)薪)?', salary_str)
        
        if match:
            min_salary = float(match.group(1))
            max_salary = float(match.group(2))
            
            # 记录薪资倍数
            multiplier = float(match.group(3)) if match.group(3) else 12.0  # 默认按12薪计算
            
            return min_salary, max_salary, multiplier
        else:
            # 处理其他可能的格式，如固定值
            fixed_match = re.search(r'(\d+)K(?:·(\d+)薪)?', salary_str)
            if fixed_match:
                fixed_salary = float(fixed_match.group(1))
                
                # 记录薪资倍数
                multiplier = float(fixed_match.group(2)) if fixed_match.group(2) else 12.0  # 默认按12薪计算
                
                return fixed_salary, fixed_salary, multiplier
            
            return np.nan, np.nan, np.nan
    except:
        return np.nan, np.nan, np.nan

#%%
# 应用工资转换函数
job_data['最低工资'] = np.nan
job_data['最高工资'] = np.nan
job_data['薪资倍数'] = np.nan
job_data['平均工资'] = np.nan

for idx, row in job_data.iterrows():
    min_salary, max_salary, multiplier = extract_salary_range(row['工资'])
    job_data.at[idx, '最低工资'] = min_salary
    job_data.at[idx, '最高工资'] = max_salary
    job_data.at[idx, '薪资倍数'] = multiplier
    
    if not pd.isna(min_salary) and not pd.isna(max_salary):
        job_data.at[idx, '平均工资'] = (min_salary + max_salary) / 2

#%%
# 检查转换结果
print("工资转换后的数据：")
print(job_data[['工资', '最低工资', '最高工资', '薪资倍数', '平均工资']].head(10))

#%%
# 统计转换是否成功
conversion_success = ~job_data['平均工资'].isna()
print(f"成功转换工资格式的记录数: {conversion_success.sum()}, 比例: {conversion_success.sum()/len(job_data):.2%}")

#%%
# 处理缺失值 - 删除没有成功转换工资的记录
job_data = job_data[conversion_success].copy()
print("清理后的数据形状：", job_data.shape)

#%%
# 保存清洗后的数据
job_data.to_excel('数据表.xlsx', index=False)
#%% md
# # # 3. 工资水平划分
#%%
# 定义工资水平函数
def get_salary_level(salary):
    """根据平均月薪划分工资等级"""
    if salary < 10:  # 10K
        return '低薪(10K以下/月)'
    elif 10 <= salary < 20:
        return '较低薪资(10-20K/月)'
    elif 20 <= salary < 30:
        return '中等薪资(20-30K/月)'
    elif 30 <= salary < 50:
        return '较高薪资(30-50K/月)'
    else:
        return '高薪(50K以上/月)'

# 应用工资水平函数
job_data['工资水平'] = job_data['平均工资'].apply(get_salary_level)

# 定义工资水平的顺序，用于后续排序
salary_level_order = ['低薪(10K以下/月)', '较低薪资(10-20K/月)', '中等薪资(20-30K/月)', '较高薪资(30-50K/月)', '高薪(50K以上/月)']

# 统计各工资水平的数量
salary_level_counts = job_data['工资水平'].value_counts().reindex(salary_level_order)
print("各工资水平的数量：")
print(salary_level_counts)

# 计算各工资水平的百分比
salary_level_percentage = salary_level_counts / len(job_data) * 100
print("\n各工资水平的百分比：")
for level, percentage in salary_level_percentage.items():
    print(f"{level}: {percentage:.2f}%")

#%% md
# # # 4. 数据探索性分析
#%%
# 统计不同学历要求的数量
education_counts = job_data['学历要求'].value_counts()
print("不同学历要求的数量：")
print(education_counts)

# 统计不同经验要求的数量
experience_counts = job_data['经验要求'].value_counts()
print("\n不同经验要求的数量：")
print(experience_counts)

# 统计不同行业的数量
industry_counts = job_data['所属行业'].value_counts().head(10)
print("\n前10个行业的数量：")
print(industry_counts)

# 处理公司地址信息，提取城市
def extract_city(address):
    if pd.isna(address):
        return "未知"
    # 按照常见格式提取城市名称（假设格式为"城市·区域·..."）
    parts = address.split('·')
    if parts:
        return parts[0]
    return "未知"

job_data['城市'] = job_data['公司地址'].apply(extract_city)

# 统计不同城市的数量
city_counts = job_data['城市'].value_counts().head(10)
print("\n前10个城市的数量：")
print(city_counts)

# 处理岗位名称，提取关键词
def extract_job_keyword(job_name):
    if pd.isna(job_name):
        return "其他"
    
    # 常见岗位关键词列表
    keywords = ['数据', '分析', '开发', '工程', '设计', '销售', '市场', '运营', '产品', '人力', 
               '财务', '会计', '客服', '行政', '管理', '测试', '运维', '算法', '研究', '教育']
    
    for keyword in keywords:
        if keyword in job_name:
            return keyword
    
    # 如果没有匹配到关键词，返回"其他"
    return "其他"

job_data['岗位关键词'] = job_data['岗位名'].apply(extract_job_keyword)

# 统计不同岗位关键词的数量
job_keyword_counts = job_data['岗位关键词'].value_counts()
print("\n不同岗位关键词的数量：")
print(job_keyword_counts)

# 分析不同岗位关键词的平均薪资
job_keyword_salary = job_data.groupby('岗位关键词')['平均工资'].mean().sort_values(ascending=False)
print("\n不同岗位关键词的平均薪资：")
print(job_keyword_salary)

#%% md
# # # 5. 平均工资与各因素的关系分析
#%%
# 5.1 学历要求与平均工资的关系
education_salary = job_data.groupby('学历要求')['平均工资'].mean().sort_values()
print("不同学历要求的平均工资：")
print(education_salary)

# 5.2 经验要求与平均工资的关系
experience_salary = job_data.groupby('经验要求')['平均工资'].mean().sort_values()
print("\n不同经验要求的平均工资：")
print(experience_salary)

# 5.3 行业与平均工资的关系（前10个行业）
industry_salary = job_data.groupby('所属行业')['平均工资'].mean().sort_values(ascending=False).head(10)
print("\n前10个行业的平均工资：")
print(industry_salary)

# 5.4 城市与平均工资的关系（前10个城市）
city_salary = job_data.groupby('城市')['平均工资'].mean().sort_values(ascending=False).head(10)
print("\n前10个城市的平均工资：")
print(city_salary)

#%% md
# # # 6. 数据可视化
#%%
# 6.1 各工资水平占比饼图
plt.figure(figsize=(10, 8))
colors = ['#FF9999', '#FFCC99', '#FFFF99', '#99FF99', '#99CCFF']
explode = (0, 0, 0.1, 0, 0)  # 突出显示中等薪资

wedges, texts, autotexts = plt.pie(
    salary_level_counts, 
    labels=salary_level_counts.index, 
    autopct='%1.1f%%', 
    startangle=90, 
    colors=colors,
    explode=explode,
    shadow=True
)
plt.title('岗位月薪水平分布')
plt.axis('equal')  # 保证饼图是圆形的

# 设置饼图文本样式
plt.setp(autotexts, size=10, weight="bold")

plt.tight_layout()
plt.savefig('岗位月薪水平分布饼图.png')
plt.show()

#%%
# 6.2 学历要求与平均工资柱状图
plt.figure(figsize=(12, 6))
# 按照学历级别重新排序
education_order = ['初中及以下', '中专/中技', '高中', '大专', '本科', '硕士', '博士']
education_salary_ordered = education_salary.reindex(education_order)

bars = plt.bar(education_salary_ordered.index, education_salary_ordered.values, color='skyblue')
plt.title('不同学历要求的平均月薪')
plt.xlabel('学历要求')
plt.ylabel('平均月薪(K)')
plt.xticks(rotation=45)
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 在柱状图上添加具体数值
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 1,
            f'{height:.1f}K', ha='center', va='bottom')

plt.tight_layout()
plt.savefig('学历要求与平均月薪关系图.png')
plt.show()

#%%
# 6.3 经验要求与平均工资柱状图
plt.figure(figsize=(12, 6))
# 按照经验年限重新排序
experience_order = ['在校/应届', '经验不限', '1年以内', '1-3年', '3-5年', '5-10年', '10年以上']
experience_salary_ordered = experience_salary.reindex(experience_order)

bars = plt.bar(experience_salary_ordered.index, experience_salary_ordered.values, color='lightgreen')
plt.title('不同经验要求的平均月薪')
plt.xlabel('经验要求')
plt.ylabel('平均月薪(K)')
plt.xticks(rotation=45)
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 在柱状图上添加具体数值
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 1,
            f'{height:.1f}K', ha='center', va='bottom')

plt.tight_layout()
plt.savefig('经验要求与平均月薪关系图.png')
plt.show()

#%%
# 6.4 前10个行业的平均工资柱状图
plt.figure(figsize=(14, 8))
bars = plt.barh(industry_salary.index[::-1], industry_salary.values[::-1], color='coral')
plt.title('不同行业的平均月薪（前10名）')
plt.xlabel('平均月薪(K)')
plt.ylabel('行业')
plt.grid(axis='x', linestyle='--', alpha=0.7)

# 在柱状图上添加具体数值
for bar in bars:
    width = bar.get_width()
    plt.text(width + 1, bar.get_y() + bar.get_height()/2, 
            f'{width:.1f}K', ha='left', va='center')

plt.tight_layout()
plt.savefig('行业与平均月薪关系图.png')
plt.show()

#%%
# 6.5 前10个城市的平均工资柱状图
plt.figure(figsize=(12, 6))
bars = plt.bar(city_salary.index, city_salary.values, color='mediumpurple')
plt.title('不同城市的平均月薪（前10名）')
plt.xlabel('城市')
plt.ylabel('平均月薪(K)')
plt.xticks(rotation=45)
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 在柱状图上添加具体数值
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 1,
            f'{height:.1f}K', ha='center', va='bottom')

plt.tight_layout()
plt.savefig('城市与平均月薪关系图.png')
plt.show()

#%%
# 6.6 不同岗位关键词的平均薪资柱状图
plt.figure(figsize=(14, 8))
# 按照平均薪资排序
job_keyword_salary_sorted = job_keyword_salary.sort_values(ascending=False)

bars = plt.barh(job_keyword_salary_sorted.index, job_keyword_salary_sorted.values, color='lightcoral')
plt.title('不同岗位关键词的平均月薪')
plt.xlabel('平均月薪(K)')
plt.ylabel('岗位关键词')
plt.grid(axis='x', linestyle='--', alpha=0.7)

# 在柱状图上添加具体数值
for bar in bars:
    width = bar.get_width()
    plt.text(width + 1, bar.get_y() + bar.get_height()/2, 
            f'{width:.1f}K', ha='left', va='center')

plt.tight_layout()
plt.savefig('岗位关键词与平均月薪关系图.png')
plt.show()

#%% md
# # # 7. 薪资预测模型构建
#%%
# 准备建模数据
# 选择特征和目标变量
X = job_data[['岗位关键词', '学历要求', '经验要求', '所属行业', '城市']]
y_monthly = job_data['平均工资']  # 月薪

# 分割训练集和测试集 - 使用月薪作为目标变量
X_train, X_test, y_train, y_test = train_test_split(X, y_monthly, test_size=0.2, random_state=42)

# 特征处理
# 对类别特征进行独热编码
categorical_features = ['岗位关键词', '学历要求', '经验要求', '所属行业', '城市']
preprocessor = ColumnTransformer(
    transformers=[
        ('cat', OneHotEncoder(handle_unknown='ignore'), categorical_features)
    ])

# 7.1 线性回归模型
linear_pipeline = Pipeline([
    ('preprocessor', preprocessor),
    ('regressor', LinearRegression())
])

linear_pipeline.fit(X_train, y_train)
y_pred_linear = linear_pipeline.predict(X_test)

# 模型评估
linear_mse = mean_squared_error(y_test, y_pred_linear)
linear_rmse = np.sqrt(linear_mse)
linear_r2 = r2_score(y_test, y_pred_linear)

print("线性回归模型评估 (月薪预测):")
print(f"均方误差(MSE): {linear_mse:.2f}")
print(f"均方根误差(RMSE): {linear_rmse:.2f}")
print(f"决定系数(R^2): {linear_r2:.2f}")

# 7.2 岭回归模型
ridge_pipeline = Pipeline([
    ('preprocessor', preprocessor),
    ('scaler', StandardScaler(with_mean=False)),  # 添加标准化步骤
    ('regressor', Ridge(alpha=1.0))  # alpha是正则化强度参数
])

ridge_pipeline.fit(X_train, y_train)
y_pred_ridge = ridge_pipeline.predict(X_test)

# 模型评估
ridge_mse = mean_squared_error(y_test, y_pred_ridge)
ridge_rmse = np.sqrt(ridge_mse)
ridge_r2 = r2_score(y_test, y_pred_ridge)

print("\n岭回归模型评估 (月薪预测):")
print(f"均方误差(MSE): {ridge_mse:.2f}")
print(f"均方根误差(RMSE): {ridge_rmse:.2f}")
print(f"决定系数(R^2): {ridge_r2:.2f}")

# 7.3 Lasso回归模型
lasso_pipeline = Pipeline([
    ('preprocessor', preprocessor),
    ('scaler', StandardScaler(with_mean=False)),
    ('regressor', Lasso(alpha=0.1))
])

lasso_pipeline.fit(X_train, y_train)
y_pred_lasso = lasso_pipeline.predict(X_test)

# 模型评估
lasso_mse = mean_squared_error(y_test, y_pred_lasso)
lasso_rmse = np.sqrt(lasso_mse)
lasso_r2 = r2_score(y_test, y_pred_lasso)

print("\nLasso回归模型评估 (月薪预测):")
print(f"均方误差(MSE): {lasso_mse:.2f}")
print(f"均方根误差(RMSE): {lasso_rmse:.2f}")
print(f"决定系数(R^2): {lasso_r2:.2f}")

# 7.4 弹性网络回归模型
elastic_pipeline = Pipeline([
    ('preprocessor', preprocessor),
    ('scaler', StandardScaler(with_mean=False)),
    ('regressor', ElasticNet(alpha=0.1, l1_ratio=0.5))  # l1_ratio控制L1和L2正则化的比例
])

elastic_pipeline.fit(X_train, y_train)
y_pred_elastic = elastic_pipeline.predict(X_test)

# 模型评估
elastic_mse = mean_squared_error(y_test, y_pred_elastic)
elastic_rmse = np.sqrt(elastic_mse)
elastic_r2 = r2_score(y_test, y_pred_elastic)

print("\n弹性网络回归模型评估 (月薪预测):")
print(f"均方误差(MSE): {elastic_mse:.2f}")
print(f"均方根误差(RMSE): {elastic_rmse:.2f}")
print(f"决定系数(R^2): {elastic_r2:.2f}")

# 比较所有模型并选择最佳模型
models = {
    "线性回归": (linear_pipeline, linear_r2, y_pred_linear),
    "岭回归": (ridge_pipeline, ridge_r2, y_pred_ridge),
    "Lasso回归": (lasso_pipeline, lasso_r2, y_pred_lasso),
    "弹性网络回归": (elastic_pipeline, elastic_r2, y_pred_elastic)
}

# 找出R^2最高的模型
best_model_name = max(models.keys(), key=lambda k: models[k][1])
best_model, best_r2, y_pred = models[best_model_name]

print(f"\n最佳模型: {best_model_name}, R^2 = {best_r2:.2f}")

# 保存最佳模型
joblib.dump(best_model, 'salary_prediction_model.pkl')
print(f"\n已保存{best_model_name}模型")
model_name = best_model_name

#%%
# 绘制预测值与实际值对比图（使用更好的模型）
plt.figure(figsize=(10, 6))

plt.scatter(y_test, y_pred, alpha=0.5)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--')
plt.xlabel('实际月薪(K)')
plt.ylabel('预测月薪(K)')
plt.title(f'{model_name}模型预测月薪与实际月薪对比')
plt.grid(True)
plt.tight_layout()
plt.savefig('薪资预测模型效果图.png')
plt.show()

#%%
print("\n进行模型预测相关性分析...")

# 计算预测值与实际值的相关系数
prediction_corr = np.corrcoef(y_test, y_pred)[0, 1]
print(f"预测值与实际值的相关系数: {prediction_corr:.4f}")

# 创建预测值与实际值的热力图（混淆矩阵）
# 将实际值和预测值分箱
bins = [0, 10, 20, 30, 40, 50, 100]
labels = ['0-10K', '10-20K', '20-30K', '30-40K', '40-50K', '50K+']

actual_binned = pd.cut(y_test, bins=bins, labels=labels)
predicted_binned = pd.cut(y_pred, bins=bins, labels=labels)

# 创建混淆矩阵
conf_matrix = pd.crosstab(actual_binned, predicted_binned, 
                          rownames=['实际值'], colnames=['预测值'], 
                          normalize='index')

# 绘制热力图
plt.figure(figsize=(10, 8))
sns.heatmap(conf_matrix, annot=True, fmt='.2%', cmap='Blues')
plt.title('薪资预测混淆矩阵 (行归一化)')
plt.tight_layout()
plt.savefig('薪资预测混淆矩阵.png')
plt.show()

#%% md
# # 8. 薪资与主要因素相关性热力图分析
#%%
print("\n进行薪资与主要因素相关性热力图分析...")

# 8.1 准备数据
# 提取岗位名中的主要职位类型
def extract_job_type(job_name):
    if pd.isna(job_name):
        return "其他"
    
    job_types = {
        '工程师': ['工程师', '开发', '研发'],
        '管理': ['经理', '主管', '总监', '负责人'],
        '分析师': ['分析师', '数据', '算法'],
        '设计师': ['设计师', 'UI', 'UX'],
        '销售': ['销售', '客户', '营销'],
        '运营': ['运营', '推广'],
        '产品': ['产品'],
        '人力资源': ['人力', 'HR', '招聘'],
        '财务': ['财务', '会计', '审计'],
        '行政': ['行政', '助理']
    }
    
    for job_type, keywords in job_types.items():
        for keyword in keywords:
            if keyword in job_name:
                return job_type
    
    return "其他"

# 应用函数提取岗位类型
job_data['岗位类型'] = job_data['岗位名'].apply(extract_job_type)

# 8.2 创建相关性分析数据框
corr_data = pd.DataFrame()

# 对分类变量进行独热编码
# 学历要求
education_dummies = pd.get_dummies(job_data['学历要求'], prefix='学历')
corr_data = pd.concat([corr_data, education_dummies], axis=1)

# 经验要求
experience_dummies = pd.get_dummies(job_data['经验要求'], prefix='经验')
corr_data = pd.concat([corr_data, experience_dummies], axis=1)

# 行业 (选择前15个最常见的行业)
top_industries = job_data['所属行业'].value_counts().nlargest(15).index
job_data['主要行业'] = job_data['所属行业'].apply(lambda x: x if x in top_industries else '其他')
industry_dummies = pd.get_dummies(job_data['主要行业'], prefix='行业')
corr_data = pd.concat([corr_data, industry_dummies], axis=1)

# 岗位类型
job_type_dummies = pd.get_dummies(job_data['岗位类型'], prefix='岗位')
corr_data = pd.concat([corr_data, job_type_dummies], axis=1)

# 添加薪资信息
corr_data['平均工资'] = job_data['平均工资']
corr_data['最低工资'] = job_data['最低工资']
corr_data['最高工资'] = job_data['最高工资']

# 8.3 计算相关性矩阵
correlation_matrix = corr_data.corr()

# 8.4 绘制完整相关性热力图
plt.figure(figsize=(20, 16))
mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
sns.heatmap(correlation_matrix, mask=mask, annot=False, cmap='coolwarm', 
            linewidths=0.5, center=0, vmin=-1, vmax=1)
plt.title('薪资与主要因素相关性热力图 (完整)', fontsize=16)
plt.tight_layout()
plt.savefig('薪资因素相关性热力图_完整.png', dpi=300)
plt.show()

# 8.5 提取与薪资相关的主要因素
salary_correlations = correlation_matrix[['平均工资']].sort_values(by='平均工资', ascending=False)

# 选择相关系数绝对值大于0.1的特征
important_features = salary_correlations[abs(salary_correlations['平均工资']) > 0.1].index.tolist()
important_features.extend(['最低工资', '最高工资'])  # 添加其他薪资指标

# 8.6 绘制薪资与重要因素的相关性热力图
important_corr = correlation_matrix.loc[important_features, important_features]

plt.figure(figsize=(14, 12))
sns.heatmap(important_corr, annot=True, fmt='.2f', cmap='coolwarm', 
            linewidths=0.5, center=0, vmin=-1, vmax=1)
plt.title('薪资与主要影响因素相关性热力图', fontsize=16)
plt.tight_layout()
plt.savefig('薪资主要因素相关性热力图.png', dpi=300)
plt.show()

# 8.7 绘制学历、经验与薪资的交叉热力图
# 计算不同学历和经验组合的平均薪资
edu_exp_salary = job_data.pivot_table(
    values='平均工资', 
    index='学历要求', 
    columns='经验要求', 
    aggfunc='mean'
)

# 按照学历和经验的逻辑顺序重新排序
education_order = ['初中及以下', '中专/中技', '高中', '大专', '本科', '硕士', '博士']
experience_order = ['在校/应届', '经验不限', '1年以内', '1-3年', '3-5年', '5-10年', '10年以上']

edu_exp_salary = edu_exp_salary.reindex(index=education_order, columns=experience_order)

plt.figure(figsize=(14, 10))
sns.heatmap(edu_exp_salary, annot=True, fmt='.1f', cmap='YlGnBu', linewidths=0.5)
plt.title('不同学历和经验组合的平均月薪(K)', fontsize=16)
plt.tight_layout()
plt.savefig('学历经验薪资热力图.png', dpi=300)
plt.show()

# 8.8 绘制行业与岗位类型的薪资热力图
industry_job_salary = job_data.pivot_table(
    values='平均工资', 
    index='主要行业', 
    columns='岗位类型', 
    aggfunc='mean'
)

# 按照平均薪资排序行业和岗位类型
industry_avg = job_data.groupby('主要行业')['平均工资'].mean().sort_values(ascending=False)
job_type_avg = job_data.groupby('岗位类型')['平均工资'].mean().sort_values(ascending=False)

industry_job_salary = industry_job_salary.reindex(index=industry_avg.index, columns=job_type_avg.index)

plt.figure(figsize=(16, 12))
sns.heatmap(industry_job_salary, annot=True, fmt='.1f', cmap='YlOrRd', linewidths=0.5)
plt.title('不同行业和岗位类型的平均月薪(K)', fontsize=16)
plt.tight_layout()
plt.savefig('行业岗位薪资热力图.png', dpi=300)
plt.show()

#%%
# 创建结论性分析表格
print("="*80)
print("                    岗位薪资数据分析结论                        ")
print("="*80)
print(f"1. 数据集共包含 {job_data.shape[0]} 条有效岗位记录")
print(f"2. 月薪分布: ")
for level, percentage in salary_level_percentage.items():
    print(f"   - {level}: {percentage:.2f}%")
print(f"3. 学历与薪资关系: 学历越高，平均月薪越高，博士平均月薪约为 {education_salary.get('博士', 0):.1f}K")
print(f"4. 工作经验与薪资关系: 经验越丰富，月薪越高，5-10年经验平均月薪约为 {experience_salary.get('5-10年', 0):.1f}K")
print(f"5. 月薪最高的行业: {industry_salary.index[0]} ({industry_salary.values[0]:.1f}K/月)")
print(f"6. 岗位关键词分析: 薪资最高的关键词是 '{job_keyword_salary.index[0]}' ({job_keyword_salary.values[0]:.1f}K/月)")
print(f"7. 薪资倍数分析: 平均薪资倍数为 {job_data['薪资倍数'].mean():.2f}，最高薪资倍数为 {job_data['薪资倍数'].max()}")
print(f"8. 月薪预测模型: {model_name}模型表现更好，R^2 = {best_r2:.2f}")
print("="*80)

#%%
