def generate_password(student_id, length):
    """
    基于学号生成固定长度的密码
    
    规则:
    - 奇数位：取学号对应数字 (如果学号长度不够则循环使用)
    - 偶数位：取前一数字+1，如果超过9则取余10
    - 校验规则：密码中不能出现连续3个相同数字，若出现则重新生成该位置数字
    
    参数:
    student_id (str): 学号
    length (int): 密码长度
    
    返回:
    str: 生成的密码
    """
    # 确保学号只包含数字
    if not student_id.isdigit():
        raise ValueError("学号必须只包含数字")
    
    # 将学号转换为数字列表
    student_id_digits = [int(digit) for digit in student_id]
    print(f"学号初始数字列表：{student_id_digits}")
    
    # 初始化密码列表
    password = []
    
    for i in range(length):
        # 当前生成的位置
        position = i + 1
        
        # 判断是奇数位还是偶数位
        if position % 2 == 1:  # 奇数位
            # 从学号中取数字，如果学号长度不够则循环使用
            digit = student_id_digits[i % len(student_id_digits)]
        else:  # 偶数位
            # 取前一数字+1，如果超过9则取余10
            digit = (password[-1] + 1) % 10
        
        # 校验是否会出现连续3个相同数字
        while len(password) >= 2 and password[-1] == password[-2] == digit:
            print(f"检测到连续相同数字 {digit}，重新生成第 {position} 位")
            # 如果出现连续三个相同数字，尝试使用不同的数字
            digit = (digit + 1) % 10
        
        # 添加当前数字到密码列表
        password.append(digit)
        print(f"当前生成第 {position} 位: {digit}, 序列: {password}")
    
    # 将密码列表转换为字符串
    password_str = ''.join(map(str, password))
    return password_str
        
if __name__ == "__main__":
    """主函数"""
    print("===== 学号数字密码生成器 =====")

    while True:
        student_id = input("请输入学号(只含数字，输入0退出): ")

        # 退出条件
        if student_id == "0":
            print("感谢使用密码生成器，再见！")
            break

        # 校验学号
        if not student_id.isdigit():
            print("错误：学号必须只包含数字！")
            continue

        # 获取密码长度
        try:
            length = int(input("请输入密码长度: "))
            if length <= 0:
                print("错误：密码长度必须大于0！")
                continue
        except ValueError:
            print("错误：请输入有效的数字！")
            continue

        try:
            # 生成密码
            password = generate_password(student_id, length)
            print("\n生成过程完成！")
            print(f"最终密码: {password}")
            print("-" * 30)
        except Exception as e:
            print(f"错误：{e}")