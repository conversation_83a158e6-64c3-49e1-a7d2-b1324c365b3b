import matplotlib
# 在导入pyplot之前设置matplotlib参数
matplotlib.use('Agg')  # 使用非交互式后端

matplotlib.rc('font', family='Microsoft YaHei')
# 确保使用ASCII连字符而非Unicode减号
matplotlib.rcParams['axes.unicode_minus'] = False

import matplotlib.pyplot as plt
import numpy as np
import math
import random
from flowmeter_model import UltrasonicFlowmeter

def plot_temperature_vs_sound_speed():
    """
    绘制温度与声速的关系图
    """
    # 创建流量计实例（几何参数对此图无影响）
    flowmeter = UltrasonicFlowmeter(diameter=0.1, path_length=0.12, angle_deg=60)
    
    # 生成温度范围
    temperatures = np.linspace(0, 100, 101)
    sound_speeds = [flowmeter.get_sound_speed_in_water(t) for t in temperatures]
    
    plt.figure(figsize=(10, 6))
    plt.plot(temperatures, sound_speeds, 'b-', linewidth=2)
    plt.title('水中声速与温度的关系', fontsize=14)
    plt.xlabel('温度 (°C)', fontsize=12)
    plt.ylabel('声速 (m/s)', fontsize=12)
    plt.grid(True)
    plt.savefig('temperature_vs_sound_speed.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("已生成：温度与声速关系图 (temperature_vs_sound_speed.png)")

def plot_temperature_error_impact():
    """
    绘制温度假设误差对流速计算的影响
    """
    # 创建流量计实例
    flowmeter = UltrasonicFlowmeter(diameter=0.1, path_length=0.12, angle_deg=60)
    
    # 真实温度和流速
    true_temp = 25.0
    true_velocity = 1.2
    
    # 获取真实声速
    true_sound_speed = flowmeter.get_sound_speed_in_water(true_temp)
    
    # 计算理想情况下的时间差
    sin_theta = math.sin(flowmeter.theta)
    t_ab = flowmeter.L / (true_sound_speed + true_velocity * sin_theta)
    t_ba = flowmeter.L / (true_sound_speed - true_velocity * sin_theta)
    time_diff = t_ba - t_ab
    
    # 不同温度假设下的计算结果
    assumed_temps = np.linspace(0, 50, 51)
    calculated_velocities = []
    
    for temp in assumed_temps:
        assumed_sound_speed = flowmeter.get_sound_speed_in_water(temp)
        v = flowmeter.calculate_velocity_problem1(time_diff, assumed_sound_speed)
        calculated_velocities.append(v)
    
    plt.figure(figsize=(10, 6))
    plt.plot(assumed_temps, calculated_velocities, 'r-', linewidth=2)
    plt.axhline(y=true_velocity, color='g', linestyle='--', label=f'真实流速 ({true_velocity} m/s)')
    plt.axvline(x=true_temp, color='b', linestyle='--', label=f'真实温度 ({true_temp}°C)')
    plt.title('温度假设误差对流速计算的影响', fontsize=14)
    plt.xlabel('假设温度 (°C)', fontsize=12)
    plt.ylabel('计算得到的流速 (m/s)', fontsize=12)
    plt.grid(True)
    plt.legend()
    plt.savefig('temperature_error_impact.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("已生成：温度误差影响图 (temperature_error_impact.png)")

def plot_noise_impact_comparison():
    """
    绘制不同噪声水平下两种模型的误差比较
    """
    # 创建流量计实例
    flowmeter = UltrasonicFlowmeter(diameter=0.1, path_length=0.12, angle_deg=60)
    
    # 真实参数
    true_temp = 25.0
    true_velocity = 1.2
    assumed_temp = 20.0  # 问题1中的假设温度
    
    # 获取真实声速和假设声速
    true_sound_speed = flowmeter.get_sound_speed_in_water(true_temp)
    assumed_sound_speed = flowmeter.get_sound_speed_in_water(assumed_temp)
    
    # 计算理想情况下的时间
    sin_theta = math.sin(flowmeter.theta)
    t_ab_perfect = flowmeter.L / (true_sound_speed + true_velocity * sin_theta)
    t_ba_perfect = flowmeter.L / (true_sound_speed - true_velocity * sin_theta)
    
    # 不同噪声水平
    noise_levels = np.logspace(-10, -7, 10)  # 从0.1ns到100ns
    
    # 存储结果
    errors_problem1 = []
    errors_problem2 = []
    
    # 每个噪声水平进行多次试验取平均
    trials = 50
    
    for noise_level in noise_levels:
        error_sum1 = 0
        error_sum2 = 0
        
        for _ in range(trials):
            # 添加噪声
            t_ab_noisy = t_ab_perfect + random.uniform(-noise_level, noise_level)
            t_ba_noisy = t_ba_perfect + random.uniform(-noise_level, noise_level)
            time_diff_noisy = t_ba_noisy - t_ab_noisy
            
            # 问题1计算
            v1 = flowmeter.calculate_velocity_problem1(time_diff_noisy, assumed_sound_speed)
            error1 = abs(v1 - true_velocity)
            error_sum1 += error1
            
            # 问题2计算
            v2 = flowmeter.calculate_velocity_problem2(t_ab_noisy, t_ba_noisy)
            error2 = abs(v2 - true_velocity)
            error_sum2 += error2
        
        # 计算平均误差
        errors_problem1.append(error_sum1 / trials)
        errors_problem2.append(error_sum2 / trials)
    
    plt.figure(figsize=(10, 6))
    plt.loglog(noise_levels, errors_problem1, 'r-', marker='o', label='问题1模型 (假设温度)')
    plt.loglog(noise_levels, errors_problem2, 'b-', marker='s', label='问题2模型 (修正模型)')
    plt.title('不同噪声水平下两种模型的误差比较', fontsize=14)
    plt.xlabel('噪声水平 (秒)', fontsize=12)
    plt.ylabel('流速平均误差 (m/s)', fontsize=12)
    plt.grid(True, which="both", ls="-")
    plt.legend()
    plt.savefig('noise_impact_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("已生成：噪声影响比较图 (noise_impact_comparison.png)")

def plot_flowmeter_geometry():
    """
    绘制流量计几何示意图
    """
    # 创建流量计实例
    flowmeter = UltrasonicFlowmeter(diameter=0.1, path_length=0.12, angle_deg=60)
    
    # 管道参数
    D = flowmeter.D
    L = flowmeter.L
    theta = flowmeter.theta
    
    # 计算关键点坐标
    pipe_length = 1.5 * D
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 5))
    
    # 绘制管道
    pipe_top = D/2
    pipe_bottom = -D/2
    ax.plot([0, pipe_length], [pipe_top, pipe_top], 'k-', linewidth=2)
    ax.plot([0, pipe_length], [pipe_bottom, pipe_bottom], 'k-', linewidth=2)
    
    # 计算传感器位置
    sensor_offset = D * 0.1  # 传感器嵌入管道的深度
    
    # 计算A和B点的坐标
    x_center = pipe_length / 2
    A_x = x_center - (D/2 - sensor_offset) * math.cos(theta)
    A_y = pipe_top - sensor_offset
    B_x = x_center + (D/2 - sensor_offset) * math.cos(theta)
    B_y = pipe_bottom + sensor_offset
    
    # 绘制传感器A和B
    sensor_size = D * 0.15
    ax.add_patch(plt.Rectangle((A_x - sensor_size/2, A_y), sensor_size, sensor_size, 
                              angle=0, color='blue', alpha=0.7))
    ax.add_patch(plt.Rectangle((B_x - sensor_size/2, B_y - sensor_size), sensor_size, sensor_size, 
                              angle=0, color='blue', alpha=0.7))
    
    # 绘制声波路径
    ax.plot([A_x, B_x], [A_y, B_y], 'r--', linewidth=1.5)
    
    # 绘制角度标记
    angle_radius = D * 0.2
    ax.plot([x_center, x_center], [0, angle_radius], 'g-', linewidth=1)
    ax.plot([x_center, x_center + angle_radius * math.cos(theta)], 
            [0, angle_radius * math.sin(theta)], 'g-', linewidth=1)
    
    # 添加角度弧线
    theta_deg = math.degrees(theta)
    arc = np.linspace(0, theta_deg, 50)
    arc_rad = np.radians(arc)
    ax.plot(x_center + angle_radius * 0.7 * np.cos(arc_rad), 
            angle_radius * 0.7 * np.sin(arc_rad), 'g-', linewidth=1)
    
    # 添加标签
    ax.text(A_x - sensor_size, A_y + sensor_size, 'A', fontsize=12)
    ax.text(B_x - sensor_size, B_y - sensor_size*1.5, 'B', fontsize=12)
    ax.text(x_center + angle_radius * 0.5 * math.cos(theta/2), 
            angle_radius * 0.5 * math.sin(theta/2), f'θ = {theta_deg:.1f}°', fontsize=10)
    ax.text(x_center + D * 0.1, D * 0.1, 'L', fontsize=10)
    ax.text(pipe_length/2, -D * 0.7, 'D', fontsize=10)
    
    # 绘制流向箭头
    arrow_length = pipe_length * 0.3
    ax.arrow(pipe_length/2 - arrow_length/2, 0, arrow_length, 0, 
             head_width=D*0.1, head_length=D*0.1, fc='blue', ec='blue', alpha=0.7)
    ax.text(pipe_length/2, D * 0.1, 'v', fontsize=12)
    
    # 绘制D标记
    ax.plot([pipe_length/2, pipe_length/2], [pipe_bottom, pipe_top], 'k:', linewidth=1)
    ax.plot([pipe_length/2 - D*0.05, pipe_length/2 + D*0.05], [pipe_bottom, pipe_bottom], 'k-', linewidth=1)
    ax.plot([pipe_length/2 - D*0.05, pipe_length/2 + D*0.05], [pipe_top, pipe_top], 'k-', linewidth=1)
    
    # 设置图形属性
    ax.set_xlim(-D*0.2, pipe_length + D*0.2)
    ax.set_ylim(-D*0.8, D*0.8)
    ax.set_aspect('equal')
    ax.set_title('超声波流量计几何示意图', fontsize=14)
    ax.set_xlabel('管道长度方向', fontsize=12)
    ax.set_ylabel('管道直径方向', fontsize=12)
    ax.grid(True, linestyle='--', alpha=0.7)
    
    # 去除坐标轴刻度
    ax.set_xticks([])
    ax.set_yticks([])
    
    plt.savefig('flowmeter_geometry.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("已生成：流量计几何示意图 (flowmeter_geometry.png)")

def main():
    """
    生成所有可视化图表
    """
    print("开始生成可视化图表...")
    
    plot_temperature_vs_sound_speed()
    plot_temperature_error_impact()
    plot_noise_impact_comparison()
    plot_flowmeter_geometry()
    
    print("\n所有图表生成完成！")

if __name__ == "__main__":
    main() 