import numpy as np

STATES = [1, 2]
ACTIONS = [1, 2]
GAMMA = 0.9  # 折扣因子

# 状态转移概率矩阵 P[s,a][s'] = 从状态s执行动作a转移到状态s'的概率
TRANSITION_PROB = {
    (1, 1): {1: 0.0, 2: 1.0},  # 状态1执行动作1，100%转移到状态2
    (1, 2): {1: 1.0, 2: 0.0},  # 状态1执行动作2，100%转移到状态1
    (2, 1): {1: 1.0, 2: 0.0},  # 状态2执行动作1，100%转移到状态1
    (2, 2): {1: 0.0, 2: 1.0}   # 状态2执行动作2，100%转移到状态2
}

# 奖励函数 R[s,a] = 在状态s执行动作a获得的即时奖励
REWARD = {
    (1, 1): 1.0,  # 状态1执行动作1获得奖励1.0
    (1, 2): 0.0,  # 状态1执行动作2获得奖励0.0
    (2, 1): 1.0,  # 状态2执行动作1获得奖励1.0
    (2, 2): 0.0   # 状态2执行动作2获得奖励0.0
}

# 初始策略
initial_policy = {1: 1, 2: 2}


def evaluate_policy(policy, value_function, transition_prob, reward, gamma, convergence_threshold=1e-6):
    """
    策略评估：计算给定策略下的状态价值函数
    
    Args:
        policy: 当前策略字典 {state: action}
        value_function: 状态价值函数字典 {state: value}
        transition_prob: 状态转移概率
        reward: 奖励函数
        gamma: 折扣因子
        convergence_threshold: 收敛阈值
    
    Returns:
        更新后的状态价值函数
    """
    while True:
        max_delta = 0
        
        for state in STATES:
            old_value = value_function[state]
            action = policy[state]
            
            # 计算新的状态价值：V(s) = Σ P(s'|s,a) * [R(s,a) + γ * V(s')]
            new_value = sum(
                transition_prob[state, action][next_state] * 
                (reward[state, action] + gamma * value_function[next_state])
                for next_state in STATES
            )
            
            value_function[state] = new_value
            max_delta = max(max_delta, abs(old_value - new_value))
        
        # 检查收敛条件
        if max_delta < convergence_threshold:
            break
    
    return value_function


def improve_policy(policy, value_function, transition_prob, reward, gamma):
    """
    Args:
        policy: 当前策略字典
        value_function: 当前状态价值函数
        transition_prob: 状态转移概率
        reward: 奖励函数
        gamma: 折扣因子
    
    Returns:
        tuple: (更新后的策略, 策略是否稳定)
    """
    is_policy_stable = True
    
    for state in STATES:
        old_action = policy[state]
        
        # 计算每个动作的期望价值
        action_values = np.zeros(len(ACTIONS))
        for action in ACTIONS:
            action_values[action - 1] = sum(
                transition_prob[state, action][next_state] * 
                (reward[state, action] + gamma * value_function[next_state])
                for next_state in STATES
            )
        
        # 选择价值最大的动作
        best_action = ACTIONS[np.argmax(action_values)]
        
        # 更新策略
        if old_action != best_action:
            is_policy_stable = False
            policy[state] = best_action
    
    return policy, is_policy_stable


def policy_iteration():
    """
    Returns:
        tuple: (最优策略, 最优价值函数)
    """
    # 初始化
    current_policy = initial_policy.copy()
    value_function = {state: 0.0 for state in STATES}
    
    iteration_count = 0
    print("开始策略迭代算法...")
    
    while True:
        iteration_count += 1
        print(f"\n第 {iteration_count} 次迭代:")
        print(f"当前策略: {current_policy}")
        
        # 策略评估
        value_function = evaluate_policy(
            current_policy, value_function, TRANSITION_PROB, REWARD, GAMMA
        )
        print(f"价值函数: {value_function}")
        
        # 策略改进
        current_policy, is_stable = improve_policy(
            current_policy, value_function, TRANSITION_PROB, REWARD, GAMMA
        )
        
        # 检查策略是否收敛
        if is_stable:
            print(f"\n策略在第 {iteration_count} 次迭代后收敛!")
            break
    
    return current_policy, value_function


def main():
    """主函数"""
    print("=" * 50)
    print(f"状态空间: {STATES}")
    print(f"动作空间: {ACTIONS}")
    print(f"折扣因子: {GAMMA}")
    print(f"初始策略: {initial_policy}")
    
    # 执行策略迭代
    optimal_policy, optimal_value = policy_iteration()
    
    # 输出结果
    print("\n" + "=" * 50)
    print("最终结果:")
    print(f"最优策略: {optimal_policy}")
    print(f"最优价值函数: {optimal_value}")


if __name__ == "__main__":
    main()
