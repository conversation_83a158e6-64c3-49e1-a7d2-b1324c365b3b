{"cells": [{"cell_type": "code", "execution_count": null, "id": "initial_id", "metadata": {"collapsed": true}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import tushare as ts\n", "import time\n", "from datetime import datetime\n", "import matplotlib.pyplot as plt\n", "\n", "# 设置pandas显示选项\n", "pd.set_option('display.max_rows', 100)\n", "pd.set_option('display.max_columns', 20)\n", "pd.set_option('display.width', 1000)\n", "\n", "# 设置matplotlib中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题\n"]}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["# Tushare API配置\n", "TOKEN = '349eb0a373e77df911a0d9673cc9a21a4678d42ee77d2466e336ad3b'\n", "ts.set_token(TOKEN)\n", "pro = ts.pro_api()\n"], "id": "b9c4901674882d87"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def process_violation_data(df: pd.DataFrame) -> pd.DataFrame:\n", "    \"\"\"对原始违规数据进行处理\"\"\"\n", "    print(\"\\n正在处理日期并提取年份...\")\n", "    df['DeclareDate'] = pd.to_datetime(df['DeclareDate'])\n", "    df['Year'] = df['DeclareDate'].dt.year\n", "\n", "    print(\"正在标准化公司代码格式...\")\n", "    df['Symbol'] = df['Symbol'].astype(str).str.z<PERSON>(6)\n", "\n", "    years_count = df['Year'].value_counts().sort_index()\n", "    print(f\"\\n年份分布:\")\n", "    for year, count in years_count.items():\n", "        print(f\"- {year}年: {count}条记录\")\n", "\n", "    print(\"\\n正在按照公司和年份进行分组判断...\")\n", "    violation_yearly = df.groupby(['Symbol', 'Year'])['IsViolated'].apply(\n", "        lambda x: 'Y' if 'Y' in x.values else 'N'\n", "    ).reset_index()\n", "\n", "    violation_yearly.rename(columns={'IsViolated': 'HasViolation'}, inplace=True)\n", "    violation_yearly['Fraud'] = np.where(violation_yearly['HasViolation'] == 'Y', 1, 0)\n", "    \n", "    return violation_yearly\n"], "id": "b41773a267856ddb"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def fix_symbol_format(violation_data):\n", "    \"\"\"标准化Symbol格式\"\"\"\n", "    print(\"\\n检查并修复股票代码格式...\")\n", "    \n", "    violation_data['Symbol'] = violation_data['Symbol'].astype(str)\n", "    violation_data['Symbol'] = violation_data['Symbol'].str.strip()\n", "    violation_data['Symbol'] = violation_data['Symbol'].str.zfill(6)\n", "    \n", "    symbol_length_counts = violation_data['Symbol'].str.len().value_counts().sort_index()\n", "    print(\"股票代码长度分布:\")\n", "    for length, count in symbol_length_counts.items():\n", "        print(f\"  长度为{length}的代码: {count}个\")\n", "    \n", "    print(\"\\n修复后的股票代码样本:\")\n", "    sample_symbols = violation_data['Symbol'].sample(min(10, len(violation_data))).tolist()\n", "    for symbol in sample_symbols:\n", "        print(f\"  {symbol}\")\n", "    \n", "    return violation_data\n"], "id": "a84f28805c6eefbb"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def build_comprehensive_mapping():\n", "    \"\"\"构建股票代码映射字典\"\"\"\n", "    try:\n", "        stocks = pro.stock_basic(exchange='', list_status='L', \n", "                               fields='ts_code,symbol,name,area,industry,list_date')\n", "        print(f\"获取当前上市公司列表，共 {stocks.shape[0]} 家\")\n", "\n", "        delisted = pro.stock_basic(exchange='', list_status='D', \n", "                                 fields='ts_code,symbol,name,area,industry,list_date')\n", "        if delisted is not None and not delisted.empty:\n", "            print(f\"获取退市公司列表，共 {delisted.shape[0]} 家\")\n", "            stocks = pd.concat([stocks, delisted], ignore_index=True)\n", "\n", "        suspended = pro.stock_basic(exchange='', list_status='P', \n", "                                  fields='ts_code,symbol,name,area,industry,list_date')\n", "        if suspended is not None and not suspended.empty:\n", "            print(f\"获取暂停上市公司列表，共 {suspended.shape[0]} 家\")\n", "            stocks = pd.concat([stocks, suspended], ignore_index=True)\n", "        \n", "        print(f\"合并后的公司总数: {stocks.shape[0]}\")\n", "\n", "        symbol_to_ts_code = {}\n", "        for _, row in stocks.iterrows():\n", "            symbol_std = row['symbol'].zfill(6)\n", "            ts_code = row['ts_code']\n", "            symbol_to_ts_code[symbol_std] = ts_code\n", "            symbol_to_ts_code[row['symbol']] = ts_code\n", "            \n", "        return symbol_to_ts_code, stocks\n", "    except Exception as e:\n", "        print(f\"构建映射时出错: {e}\")\n", "        return None, None\n"], "id": "4934d92d38527e6f"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def get_financial_indicators(ts_code, year):\n", "    \"\"\"获取公司的财务指标数据\"\"\"\n", "    try:\n", "        time.sleep(0.5)\n", "        period = f\"{year}1231\"\n", "        fin_indicator = pro.fina_indicator(ts_code=ts_code, period=period)\n", "        \n", "        if fin_indicator is not None and not fin_indicator.empty:\n", "            print(f\"获取 {ts_code} {year}年 财务指标成功\")\n", "            return fin_indicator\n", "        else:\n", "            print(f\"获取 {ts_code} {year}年 财务指标为空\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"获取财务指标出错: {e}\")\n", "        time.sleep(2)\n", "        return None\n"], "id": "3a49fb4d822a32a2"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def get_income_data(ts_code, year):\n", "    \"\"\"获取利润表数据\"\"\"\n", "    try:\n", "        time.sleep(0.5)\n", "        period = f\"{year}1231\"\n", "        income = pro.income(ts_code=ts_code, period=period)\n", "        \n", "        if income is not None and not income.empty:\n", "            print(f\"获取 {ts_code} {year}年 利润表成功\")\n", "            return income\n", "        else:\n", "            print(f\"获取 {ts_code} {year}年 利润表为空\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"获取利润表出错: {e}\")\n", "        time.sleep(2)\n", "        return None\n"], "id": "630de474c3f248ed"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def get_balance_sheet(ts_code, year):\n", "    \"\"\"获取资产负债表数据\"\"\"\n", "    try:\n", "        time.sleep(0.5)\n", "        period = f\"{year}1231\"\n", "        balancesheet = pro.balancesheet(ts_code=ts_code, period=period)\n", "        \n", "        if balancesheet is not None and not balancesheet.empty:\n", "            print(f\"获取 {ts_code} {year}年 资产负债表成功\")\n", "            return balancesheet\n", "        else:\n", "            print(f\"获取 {ts_code} {year}年 资产负债表为空\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"获取资产负债表出错: {e}\")\n", "        time.sleep(2)\n", "        return None\n"], "id": "4156b4deaa090909"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def get_cashflow(ts_code, year):\n", "    \"\"\"获取现金流量表数据\"\"\"\n", "    try:\n", "        time.sleep(0.5)\n", "        period = f\"{year}1231\"\n", "        cashflow = pro.cashflow(ts_code=ts_code, period=period)\n", "        \n", "        if cashflow is not None and not cashflow.empty:\n", "            print(f\"获取 {ts_code} {year}年 现金流量表成功\")\n", "            return cashflow\n", "        else:\n", "            print(f\"获取 {ts_code} {year}年 现金流量表为空\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"获取现金流量表出错: {e}\")\n", "        time.sleep(2)\n", "        return None\n"], "id": "a67cd796343bb4d1"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def get_stock_returns(ts_code, year):\n", "    \"\"\"获取股票年度收益率和波动率\"\"\"\n", "    try:\n", "        start_date = f\"{year}0101\"\n", "        end_date = f\"{year}1231\"\n", "        time.sleep(0.5)\n", "        \n", "        daily = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)\n", "        \n", "        if daily is not None and not daily.empty:\n", "            if len(daily) >= 2:\n", "                first_close = daily.sort_values('trade_date').iloc[0]['close']\n", "                last_close = daily.sort_values('trade_date').iloc[-1]['close']\n", "                yearly_return = (last_close - first_close) / first_close\n", "                volatility = daily['pct_chg'].std()\n", "                \n", "                print(f\"获取 {ts_code} {year}年 收益率数据成功\")\n", "                \n", "                return {\n", "                    'YearlyReturn': yearly_return,\n", "                    'Volatility': volatility,\n", "                    'TradingDays': len(daily)\n", "                }\n", "            else:\n", "                print(f\"获取 {ts_code} {year}年 收益率数据不足\")\n", "                return None\n", "        else:\n", "            print(f\"获取 {ts_code} {year}年 收益率数据为空\")\n", "            return None\n", "    except Exception as e:\n", "        print(f\"获取收益率数据出错: {e}\")\n", "        time.sleep(2)\n", "        return None\n"], "id": "8643037e65078165"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def build_company_feature_set(ts_code, year, fraud_label):\n", "    \"\"\"构建单个公司年度的特征集\"\"\"\n", "    try:\n", "        company_features = {\n", "            'ts_code': ts_code,\n", "            'Year': year,\n", "            'Fraud': fraud_label\n", "        }\n", "        \n", "        features_count = 0\n", "        \n", "        # 获取财务指标\n", "        fin_indicators = get_financial_indicators(ts_code, year)\n", "        if fin_indicators is not None and not fin_indicators.empty:\n", "            key_indicators = [\n", "                'eps', 'dt_eps', 'total_revenue_ps', 'revenue_ps', 'capital_rese_ps', \n", "                'surplus_rese_ps', 'undist_profit_ps', 'extra_item', 'profit_dedt', \n", "                'gross_margin', 'current_ratio', 'quick_ratio', 'cash_ratio', 'ar_turn', \n", "                'ca_turn', 'fa_turn', 'assets_turn', 'op_income', 'ebit', 'ebitda', 'fcff', \n", "                'fcfe', 'current_exint', 'noncurrent_exint', 'interestdebt', 'netdebt', \n", "                'tangible_asset', 'working_capital', 'networking_capital', 'invest_capital', \n", "                'retained_earnings', 'diluted2_eps', 'bps', 'ocfps', 'retainedps'\n", "            ]\n", "            \n", "            for indicator in key_indicators:\n", "                if indicator in fin_indicators.columns:\n", "                    value = fin_indicators[indicator].values[0]\n", "                    if pd.notna(value):\n", "                        company_features[indicator] = value\n", "                        features_count += 1\n", "            \n", "            print(f\"获取到 {features_count} 个财务指标特征\")\n", "        \n", "        # 获取其他财务数据并计算特征\n", "        income_data = get_income_data(ts_code, year)\n", "        balance_data = get_balance_sheet(ts_code, year)\n", "        cashflow_data = get_cashflow(ts_code, year)\n", "        returns_data = get_stock_returns(ts_code, year)\n", "        \n", "        # 处理各类数据并添加到特征集\n", "        if all(data is not None for data in [income_data, balance_data, cashflow_data, returns_data]):\n", "            # 计算财务比率\n", "            total_assets = balance_data['total_assets'].values[0]\n", "            if total_assets != 0:\n", "                company_features['asset_liability_ratio'] = balance_data['total_liab'].values[0] / total_assets\n", "                company_features['ROA'] = income_data['n_income'].values[0] / total_assets\n", "            \n", "            # 添加现金流指标\n", "            company_features['operating_cashflow'] = cashflow_data['n_cashflow_act'].values[0]\n", "            company_features['investment_cashflow'] = cashflow_data['n_cashflow_inv_act'].values[0]\n", "            \n", "            # 添加收益率指标\n", "            company_features.update(returns_data)\n", "            \n", "            print(\"成功添加财务比率和市场表现指标\")\n", "        \n", "        return pd.DataFrame([company_features])\n", "    \n", "    except Exception as e:\n", "        print(f\"构建特征集时发生错误: {e}\")\n", "        return pd.DataFrame()\n"], "id": "8db4ba0522d673a5"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def handle_missing_values(df):\n", "    \"\"\"处理缺失值\"\"\"\n", "    print(\"\\n处理缺失值...\")\n", "    \n", "    missing_stats = df.isnull().sum()\n", "    cols_with_missing = missing_stats[missing_stats > 0]\n", "    \n", "    if len(cols_with_missing) > 0:\n", "        print(\"缺失值最多的5个列:\")\n", "        print(cols_with_missing.sort_values(ascending=False).head())\n", "    \n", "    numeric_cols = df.select_dtypes(include=['int64', 'float64']).columns\n", "    for col in numeric_cols:\n", "        if df[col].isnull().sum() > 0:\n", "            df[col].fillna(df[col].median(), inplace=True)\n", "    \n", "    return df\n"], "id": "8293b6d9a5210e1a"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def handle_outliers(df):\n", "    \"\"\"处理异常值\"\"\"\n", "    print(\"\\n处理异常值...\")\n", "    \n", "    exclude_cols = ['ts_code', 'Year', 'Fraud', 'Symbol']\n", "    numeric_cols = df.select_dtypes(include=['int64', 'float64']).columns\n", "    numeric_cols = [col for col in numeric_cols if col not in exclude_cols]\n", "    \n", "    for col in numeric_cols:\n", "        if df[col].std() != 0:\n", "            z_scores = np.abs((df[col] - df[col].mean()) / df[col].std())\n", "            outliers = z_scores > 3\n", "            if outliers.sum() > 0:\n", "                print(f\"列 {col} 中发现 {outliers.sum()} 个异常值，已替换\")\n", "                df.loc[outliers, col] = df[col].median()\n", "    \n", "    return df\n"], "id": "3435cfb562f53174"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def select_features(df, target_col='Fraud', top_n=30):\n", "    \"\"\"选择最重要的特征\"\"\"\n", "    print(\"\\n选择重要特征...\")\n", "    \n", "    exclude_cols = ['ts_code', 'Year', 'Symbol', target_col]\n", "    feature_cols = [col for col in df.columns if col not in exclude_cols]\n", "    numeric_feature_cols = df[feature_cols].select_dtypes(include=['int64', 'float64']).columns.tolist()\n", "    \n", "    if len(numeric_feature_cols) <= top_n:\n", "        print(f\"可用数值特征只有 {len(numeric_feature_cols)} 个，全部保留\")\n", "        return numeric_feature_cols\n", "    \n", "    correlation = df[numeric_feature_cols + [target_col]].corr()[target_col].abs().sort_values(ascending=False)\n", "    top_features = correlation.nlargest(top_n).index.tolist()\n", "    top_features = [f for f in top_features if f != target_col]\n", "    \n", "    print(f\"已选择 {len(top_features)} 个最相关特征\")\n", "    return top_features\n"], "id": "50728792577d2074"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["def main():\n", "    \"\"\"主函数：执行数据处理和特征构建流程\"\"\"\n", "    print(\"===== 开始处理违规数据和构建特征 =====\")\n", "    \n", "    # 读取原始违规数据\n", "    violation_file = 'STK_Violation_Main.xlsx'\n", "    try:\n", "        print(f\"正在读取违规数据文件: {violation_file}\")\n", "        df_raw = pd.read_excel(violation_file)\n", "        print(f\"原始数据集: {df_raw.shape[0]} 条记录\")\n", "        \n", "        # 处理违规数据\n", "        processed_df = process_violation_data(df_raw)\n", "        processed_df = fix_symbol_format(processed_df)\n", "        \n", "        # 构建股票代码映射\n", "        symbol_to_ts_code, _ = build_comprehensive_mapping()\n", "        if symbol_to_ts_code is None:\n", "            print(\"无法构建股票代码映射，程序退出\")\n", "            return\n", "        \n", "        # 选择样本进行处理\n", "        sample_size = int(input(\"输入要处理的样本数量（建议小于20）：\"))\n", "        fraud_records = processed_df[processed_df['Fraud'] == 1]\n", "        non_fraud_records = processed_df[processed_df['Fraud'] == 0]\n", "        \n", "        fraud_sample_size = min(sample_size//2, len(fraud_records))\n", "        non_fraud_sample_size = min(sample_size - fraud_sample_size, len(non_fraud_records))\n", "        \n", "        samples = pd.concat([\n", "            fraud_records.sample(fraud_sample_size),\n", "            non_fraud_records.sample(non_fraud_sample_size)\n", "        ])\n", "        \n", "        print(f\"\\n选择了 {len(samples)} 个样本，其中违规样本 {fraud_sample_size} 个\")\n", "        \n", "        # 构建特征数据集\n", "        all_features = []\n", "        for _, row in samples.iterrows():\n", "            ts_code = symbol_to_ts_code.get(row['Symbol'])\n", "            if ts_code:\n", "                features = build_company_feature_set(ts_code, row['Year'], row['Fraud'])\n", "                if not features.empty:\n", "                    all_features.append(features)\n", "        \n", "        if not all_features:\n", "            print(\"未能成功构建特征数据集\")\n", "            return\n", "        \n", "        # 合并所有特征\n", "        features_df = pd.concat(all_features, ignore_index=True)\n", "        \n", "        # 数据清洗和预处理\n", "        features_df = handle_missing_values(features_df)\n", "        features_df = handle_outliers(features_df)\n", "        \n", "        # 特征选择\n", "        top_features = select_features(features_df)\n", "        final_cols = top_features + ['ts_code', 'Year', 'Fraud']\n", "        final_features_df = features_df[final_cols].copy()\n", "        \n", "        # 保存结果\n", "        final_features_df.to_csv('fraud_features_dataset.csv', index=False)\n", "        print(\"\\n特征数据集已保存至 fraud_features_dataset.csv\")\n", "        print(f\"最终数据集维度: {final_features_df.shape}\")\n", "        \n", "        # 可视化特征相关性\n", "        plt.figure(figsize=(12, 8))\n", "        correlation_matrix = final_features_df[top_features].corr()\n", "        plt.imshow(correlation_matrix, cmap='coolwarm', aspect='auto')\n", "        plt.colorbar()\n", "        plt.title('特征相关性热力图')\n", "        plt.xticks(range(len(top_features)), top_features, rotation=90)\n", "        plt.yticks(range(len(top_features)), top_features)\n", "        plt.tight_layout()\n", "        plt.savefig('feature_correlation.png')\n", "        plt.close()\n", "        \n", "    except Exception as e:\n", "        print(f\"处理过程中出错: {e}\")\n", "        import traceback\n", "        traceback.print_exc()\n"], "id": "9276cd123f5306dd"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": ["if __name__ == \"__main__\":\n", "    main() "], "id": "8b70e81f0ce26a26"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}