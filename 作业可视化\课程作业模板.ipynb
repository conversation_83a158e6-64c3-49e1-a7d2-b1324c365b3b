{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# <center> 我的标题"]}, {"cell_type": "raw", "metadata": {}, "source": ["数据分析结构可以调整"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 一、选题背景及问题的提出"]}, {"cell_type": "raw", "metadata": {}, "source": ["    二手房市场......."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 二、数据处理与说明  "]}, {"cell_type": "raw", "metadata": {}, "source": ["    1. 现有采集自.......\n", "    2. 说明\n", "    3. 介绍"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （一）数据预处理  "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# 模块导入\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# 数据集合并\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### （二）数据说明\n"]}, {"cell_type": "raw", "metadata": {}, "source": ["    处理后的变量及数据说明见下表："]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 三、数据分析\n"]}, {"cell_type": "raw", "metadata": {}, "source": [" 从那几个方面分析数据"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （一）探索性数据分析"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# 分布探索\n", "\n", "\n"]}, {"cell_type": "raw", "metadata": {}, "source": ["上述分析说明，......"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### （二）模型分析"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# 建立...模型\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# 模型评估\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 四、结论\n"]}, {"cell_type": "raw", "metadata": {}, "source": ["本实验/研究……\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.10"}}, "nbformat": 4, "nbformat_minor": 4}