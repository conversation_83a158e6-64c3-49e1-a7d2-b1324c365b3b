{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-21T02:01:53.169884Z", "start_time": "2025-06-21T02:01:53.157647Z"}}, "source": ["#李洋依202456248 \n", "import pandas as pd\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "warnings.filterwarnings('ignore')\n", "# 设置中文字体，避免中文显示乱码\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  \n", "plt.rcParams['axes.unicode_minus'] = False  \n"], "outputs": [], "execution_count": 84}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:01:54.074906Z", "start_time": "2025-06-21T02:01:54.039627Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 读取学生信息表\n", "student_info = pd.read_excel('2024级会计7,8班学生名单.xlsx')\n", "print(\"学生信息表的形状：\", student_info.shape)\n", "print(student_info.head())\n"], "id": "904e6887287f8369", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学生信息表的形状： (69, 6)\n", "          学号    院系   专业         班级   学校代码      学校\n", "0  202216003  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "1  202456235  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "2  202456236  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "3  202456237  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "4  202456238  会计学院  会计学  2024会计学07  10562  大连财经学院\n"]}], "execution_count": 85}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:01:55.216713Z", "start_time": "2025-06-21T02:01:55.167102Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 读取作业完成情况表\n", "homework_scores = pd.read_excel('2024级会计7,8班_课后作业成绩（前5次）.xlsx')\n", "print(\"作业完成情况表的形状：\", homework_scores.shape)\n", "print(homework_scores.head())\n"], "id": "1ac7936794f4ccbe", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["作业完成情况表的形状： (69, 7)\n", "          学号         班级  第1次作业  第2次作业  第3次作业  第4次作业  第5次作业\n", "0  202216003  2024会计学07    NaN    NaN    NaN    NaN    NaN\n", "1  202456235  2024会计学07   74.0   82.5   70.0  101.0  100.0\n", "2  202456236  2024会计学07   82.0   85.0  100.0  101.0   87.5\n", "3  202456237  2024会计学07   95.0   85.0   90.0  101.0   87.5\n", "4  202456238  2024会计学07   90.0   77.5   90.0   80.6   62.5\n"]}], "execution_count": 86}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:01:56.137225Z", "start_time": "2025-06-21T02:01:56.123218Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# \"学号 班级\"列作为合并键\n", "merged_data = pd.merge(student_info, homework_scores, on=['学号','班级'], how='inner')\n"], "id": "c13bfce0a9205ee7", "outputs": [], "execution_count": 87}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:01:57.183566Z", "start_time": "2025-06-21T02:01:57.173128Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 查看合并后的数据\n", "print(\"合并后的数据表的形状：\", merged_data.shape)\n", "print(merged_data.head())\n"], "id": "790893e79ce0625b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["合并后的数据表的形状： (69, 11)\n", "          学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  第4次作业  \\\n", "0  202216003  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN    NaN   \n", "1  202456235  会计学院  会计学  2024会计学07  10562  大连财经学院   74.0   82.5   70.0  101.0   \n", "2  202456236  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0   85.0  100.0  101.0   \n", "3  202456237  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0   85.0   90.0  101.0   \n", "4  202456238  会计学院  会计学  2024会计学07  10562  大连财经学院   90.0   77.5   90.0   80.6   \n", "\n", "   第5次作业  \n", "0    NaN  \n", "1  100.0  \n", "2   87.5  \n", "3   87.5  \n", "4   62.5  \n"]}], "execution_count": 88}, {"metadata": {}, "cell_type": "markdown", "source": "# # ## 数据清洗", "id": "51d59cd18510b129"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:03.829555Z", "start_time": "2025-06-21T02:02:03.811279Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 检查重复行\n", "duplicate_rows = merged_data.duplicated()\n", "print(\"是否存在重复行：\", duplicate_rows.any())\n", "if duplicate_rows.any():\n", "    print(\"重复行数量：\", duplicate_rows.sum())\n", "    print(\"重复行索引：\", merged_data[duplicate_rows].index.tolist())\n"], "id": "fbae22f63d81cd17", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在重复行： False\n"]}], "execution_count": 89}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:04.670804Z", "start_time": "2025-06-21T02:02:04.642347Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 检查空值\n", "null_values = merged_data.isnull()\n", "print(\"是否存在空值：\", null_values.any().any())\n", "if null_values.any().any():\n", "    print(\"每列的空值数量：\")\n", "    print(null_values.sum())\n", "    \n", "    # 查看哪些行包含空值\n", "    rows_with_nulls = merged_data[merged_data.isnull().any(axis=1)]\n", "    print(\"包含空值的行数：\", len(rows_with_nulls))\n", "    if len(rows_with_nulls) > 0:\n", "        print(\"包含空值的行：\")\n", "        print(rows_with_nulls)\n"], "id": "4d3e7f2b938c9a69", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在空值： True\n", "每列的空值数量：\n", "学号        0\n", "院系        0\n", "专业        0\n", "班级        0\n", "学校代码      0\n", "学校        0\n", "第1次作业    13\n", "第2次作业    13\n", "第3次作业    14\n", "第4次作业    15\n", "第5次作业    12\n", "dtype: int64\n", "包含空值的行数： 18\n", "包含空值的行：\n", "           学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  \\\n", "0   202216003  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "6   202456240  会计学院  会计学  2024会计学07  10562  大连财经学院  100.0   95.0   90.0   \n", "12  202456247  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "16  202456251  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "17  202456254  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "24  202456262  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "26  202456264  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "34  202456273  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "39  202456279  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   85.0    NaN   \n", "43  202456284  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0  100.0  100.0   \n", "44  202456285  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "48  202456289  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "53  202456294  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   90.0   70.0   \n", "54  202456296  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   87.5   60.0   \n", "56  202456298  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "58  202456300  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "59  202456302  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "68  202456312  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "\n", "    第4次作业  第5次作业  \n", "0     NaN    NaN  \n", "6     NaN  100.0  \n", "12    NaN    NaN  \n", "16    NaN    NaN  \n", "17    NaN    NaN  \n", "24    NaN    NaN  \n", "26    NaN    NaN  \n", "34    NaN    NaN  \n", "39    NaN   62.5  \n", "43    NaN  100.0  \n", "44    NaN    NaN  \n", "48   94.4   62.5  \n", "53    NaN   62.5  \n", "54   80.8    NaN  \n", "56    NaN    NaN  \n", "58    NaN    NaN  \n", "59    NaN    NaN  \n", "68   74.0   62.5  \n"]}], "execution_count": 90}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:05.398768Z", "start_time": "2025-06-21T02:02:05.388073Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "#空值填充为0分\n", "merged_data = merged_data.fillna(0)\n"], "id": "6b3de2528ff82a39", "outputs": [], "execution_count": 91}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:06.024625Z", "start_time": "2025-06-21T02:02:06.016624Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 验证是否还有空值\n", "print(\"填充后是否还有空值：\", merged_data.isnull().any().any())\n"], "id": "462cd9978a758196", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["填充后是否还有空值： False\n"]}], "execution_count": 92}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:06.763018Z", "start_time": "2025-06-21T02:02:06.746018Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 分数列应该在0-100之间\n", "numeric_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        min_val = merged_data[col].min()\n", "        max_val = merged_data[col].max()\n", "        print(f\"列 '{col}' 的值范围: {min_val} - {max_val}\")\n", "        \n", "        # 检查是否有超出合理范围的值\n", "        if min_val < 0 or max_val > 100:\n", "            print(f\"警告: 列 '{col}' 中存在可能的异常值!\")\n", "            # 显示异常值\n", "            abnormal_values = merged_data[(merged_data[col] < 0) | (merged_data[col] > 100)]\n", "            if len(abnormal_values) > 0:\n", "                print(\"异常值所在行：\")\n", "                print(abnormal_values)\n"], "id": "bb833f8fc45fa701", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["列 '第1次作业' 的值范围: 0.0 - 100.0\n", "列 '第2次作业' 的值范围: 0.0 - 100.0\n", "列 '第3次作业' 的值范围: 0.0 - 100.0\n", "列 '第4次作业' 的值范围: 0.0 - 101.0\n", "警告: 列 '第4次作业' 中存在可能的异常值!\n", "异常值所在行：\n", "           学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  \\\n", "1   202456235  会计学院  会计学  2024会计学07  10562  大连财经学院   74.0   82.5   70.0   \n", "2   202456236  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0   85.0  100.0   \n", "3   202456237  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0   85.0   90.0   \n", "7   202456241  会计学院  会计学  2024会计学07  10562  大连财经学院   87.0   95.0   90.0   \n", "11  202456246  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0  100.0  100.0   \n", "13  202456248  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0  100.0  100.0   \n", "15  202456250  会计学院  会计学  2024会计学07  10562  大连财经学院   87.0   95.0  100.0   \n", "20  202456258  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0  100.0   90.0   \n", "36  202456275  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "42  202456282  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   95.0   90.0   \n", "49  202456290  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   95.0   90.0   \n", "50  202456291  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "51  202456292  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   92.5   80.0   \n", "55  202456297  会计学院  会计学  2024会计学08  10562  大连财经学院   90.0   95.0   90.0   \n", "60  202456303  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   90.0   90.0   \n", "61  202456304  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "63  202456307  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   95.0   80.0   \n", "64  202456308  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   95.0  100.0   \n", "65  202456309  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   90.0   90.0   \n", "\n", "    第4次作业  第5次作业  \n", "1   101.0  100.0  \n", "2   101.0   87.5  \n", "3   101.0   87.5  \n", "7   101.0  100.0  \n", "11  101.0  100.0  \n", "13  101.0   87.5  \n", "15  101.0   75.0  \n", "20  101.0   87.5  \n", "36  101.0   62.5  \n", "42  101.0  100.0  \n", "49  101.0  100.0  \n", "50  101.0   50.0  \n", "51  101.0   62.5  \n", "55  101.0  100.0  \n", "60  101.0  100.0  \n", "61  101.0   62.5  \n", "63  101.0   75.0  \n", "64  101.0  100.0  \n", "65  101.0  100.0  \n", "列 '第5次作业' 的值范围: 0.0 - 100.0\n"]}], "execution_count": 93}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:07.638317Z", "start_time": "2025-06-21T02:02:07.625798Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 处理异常值：小于0的替换为0，大于100的替换为100\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        # 将小于0的值替换为0\n", "        merged_data.loc[merged_data[col] < 0, col] = 0\n", "        # 将大于100的值替换为100\n", "        merged_data.loc[merged_data[col] > 100, col] = 100\n", "\n", "# 验证是否还有异常值\n", "print(\"\\n处理异常值后的验证：\")\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        min_val = merged_data[col].min()\n", "        max_val = merged_data[col].max()\n", "        print(f\"列 '{col}' 的值范围: {min_val} - {max_val}\")\n"], "id": "320224a69d5fed6b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "处理异常值后的验证：\n", "列 '第1次作业' 的值范围: 0.0 - 100.0\n", "列 '第2次作业' 的值范围: 0.0 - 100.0\n", "列 '第3次作业' 的值范围: 0.0 - 100.0\n", "列 '第4次作业' 的值范围: 0.0 - 100.0\n", "列 '第5次作业' 的值范围: 0.0 - 100.0\n"]}], "execution_count": 94}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:19.627951Z", "start_time": "2025-06-21T02:02:19.592376Z"}}, "cell_type": "code", "source": ["# 李洋依202456248  \n", "# 保存清洗后的数据\n", "merged_data.to_excel('李洋依202456248.xlsx', index=False)"], "id": "69462696e248d65a", "outputs": [], "execution_count": 95}, {"metadata": {}, "cell_type": "markdown", "source": ["# # # （二）数据分析\n", "# # 利用Pandas库对整合后的数据进行统计分析\n", "# "], "id": "96646d2b29148a4e"}, {"metadata": {}, "cell_type": "markdown", "source": ["# 1. 个人学习成绩分析\n", "# 计算本人的作业平均成绩、最高成绩、最低成绩"], "id": "1d77a472b3f7582a"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:31.107922Z", "start_time": "2025-06-21T02:02:31.080850Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "my_student_id = 202456248\n", "my_data = merged_data[merged_data['学号'] == my_student_id]\n", "\n", "if len(my_data) == 0:\n", "    print(f\"未找到学号为 {my_student_id} 的学生数据\")\n", "else:\n", "    print(f\"学号为 {my_student_id} 的学生信息：\")\n", "    print(my_data[['学号']].iloc[0])\n", "    \n", "    print(\"\\n所有列名：\")\n", "    print(merged_data.columns.tolist())\n", "    \n", "    score_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns.tolist()\n", "    score_columns = [col for col in score_columns if col not in ['学号', '学校代码']]\n", "    \n", "    print(\"\\n作业成绩列：\")\n", "    print(score_columns)\n", "    \n", "    my_scores = my_data[score_columns]\n", "    print(\"\\n本人各次作业成绩：\")\n", "    print(my_scores)\n", "    \n", "    # 计算平均成绩、最高成绩、最低成绩\n", "    avg_score = my_scores.mean(axis=1).iloc[0]\n", "    max_score = my_scores.max(axis=1).iloc[0]\n", "    min_score = my_scores.min(axis=1).iloc[0]\n", "    \n", "    print(f\"\\n本人成绩统计：\")\n", "    print(f\"平均成绩: {avg_score:.2f}\")\n", "    print(f\"最高成绩: {max_score:.2f}\")\n", "    print(f\"最低成绩: {min_score:.2f}\")\n"], "id": "ed0b71ea3e0dd7cc", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学号为 202456248 的学生信息：\n", "学号    202456248\n", "Name: 13, dtype: int64\n", "\n", "所有列名：\n", "['学号', '院系', '专业', '班级', '学校代码', '学校', '第1次作业', '第2次作业', '第3次作业', '第4次作业', '第5次作业']\n", "\n", "作业成绩列：\n", "['第1次作业', '第2次作业', '第3次作业', '第4次作业', '第5次作业']\n", "\n", "本人各次作业成绩：\n", "    第1次作业  第2次作业  第3次作业  第4次作业  第5次作业\n", "13   95.0  100.0  100.0  100.0   87.5\n", "\n", "本人成绩统计：\n", "平均成绩: 96.50\n", "最高成绩: 100.00\n", "最低成绩: 87.50\n"]}], "execution_count": 96}, {"metadata": {}, "cell_type": "markdown", "source": ["# 2. 班级学习数据分析\n", "# 分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少"], "id": "4cf2709d005ac1f9"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:36.274282Z", "start_time": "2025-06-21T02:02:36.257181Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "my_class = my_data['班级'].iloc[0]\n", "print(f\"学生 所在的班级是: {my_class}\")\n", "\n", "# 筛选出同一班级的学生\n", "class_students = merged_data[merged_data['班级'] == my_class]\n", "print(f\"班级 {my_class} 共有 {len(class_students)} 名学生\")\n", "# 计算每个学生的平均分\n", "student_avg_scores = class_students[score_columns].mean(axis=1)\n", "class_students['平均分'] = student_avg_scores\n", "merged_data.loc[class_students.index, '平均分'] = student_avg_scores\n", "\n", "# 查看班级平均分的统计信息\n", "class_avg = student_avg_scores.mean()\n", "class_max = student_avg_scores.max()\n", "class_min = student_avg_scores.min()\n", "\n", "print(\"班级成绩统计：\")\n", "print(f\"班级平均分: {class_avg:.2f}\")\n", "print(f\"班级最高成绩: {class_max:.2f}\")\n", "print(f\"班级最低成绩: {class_min:.2f}\")\n"], "id": "532b2a94f9f2d12d", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学生 所在的班级是: 2024会计学07\n", "班级 2024会计学07 共有 35 名学生\n", "班级成绩统计：\n", "班级平均分: 70.88\n", "班级最高成绩: 99.00\n", "班级最低成绩: 0.00\n"]}], "execution_count": 97}, {"metadata": {}, "cell_type": "markdown", "source": "# 查找成绩优秀或需要辅导的学生", "id": "35b3a173a334df7a"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:39.956052Z", "start_time": "2025-06-21T02:02:39.942343Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 按平均分排序，找出班级内成绩最好的前10名学生\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "print(f\"班级 {my_class} 成绩优秀的前{top_n}名学生：\")\n", "print(top_students[['学号', '平均分']])\n", "\n", "# 找出班级内成绩最差的后10名学生\n", "bottom_students = class_students.sort_values(by='平均分').head(top_n)\n", "print(f\"\\n班级 {my_class} 需要辅导的后{top_n}名学生：\")\n", "print(bottom_students[['学号', '平均分']])\n"], "id": "8b84b3de3b9ab6fd", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024会计学07 成绩优秀的前10名学生：\n", "           学号    平均分\n", "11  202456246  99.00\n", "13  202456248  96.50\n", "9   202456244  95.84\n", "29  202456267  95.16\n", "22  202456260  94.84\n", "7   202456241  94.40\n", "23  202456261  94.34\n", "19  202456257  92.52\n", "21  202456259  92.48\n", "20  202456258  91.90\n", "\n", "班级 2024会计学07 需要辅导的后10名学生：\n", "           学号    平均分\n", "0   202216003   0.00\n", "12  202456247   0.00\n", "17  202456254   0.00\n", "16  202456251   0.00\n", "24  202456262   0.00\n", "26  202456264   0.00\n", "34  202456273   0.00\n", "6   202456240  77.00\n", "14  202456249  78.02\n", "4   202456238  80.12\n"]}], "execution_count": 98}, {"metadata": {}, "cell_type": "markdown", "source": "# 分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布", "id": "65aa8034694d58d9"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:46.812887Z", "start_time": "2025-06-21T02:02:46.797881Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 定义分数段\n", "def get_score_range(score):\n", "    if score < 60:\n", "        return '60分以下'\n", "    elif 60 <= score < 70:\n", "        return '60-69分'\n", "    elif 70 <= score < 80:\n", "        return '70-79分'\n", "    elif 80 <= score < 90:\n", "        return '80-89分'\n", "    else:\n", "        return '90分以上'\n", "\n", "# 应用分数段函数到班级学生\n", "class_students['分数段'] = class_students['平均分'].apply(get_score_range)\n", "merged_data.loc[class_students.index, '分数段'] = class_students['分数段']\n", "\n", "# 统计班级内各分数段的学生人数\n", "score_range_counts = class_students['分数段'].value_counts().sort_index()\n", "print(f\"班级 {my_class} 各分数段学生人数分布：\")\n", "print(score_range_counts)\n", "\n", "# 计算班级内各分数段的百分比\n", "score_range_percentage = score_range_counts / len(class_students) * 100\n", "print(f\"\\n班级 {my_class} 各分数段学生人数百分比：\")\n", "for range_name, percentage in score_range_percentage.items():\n", "    print(f\"{range_name}: {percentage:.2f}%\")\n"], "id": "a765559b1950c16", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024会计学07 各分数段学生人数分布：\n", "分数段\n", "60分以下      7\n", "70-79分     2\n", "80-89分    11\n", "90分以上     15\n", "Name: count, dtype: int64\n", "\n", "班级 2024会计学07 各分数段学生人数百分比：\n", "60分以下: 20.00%\n", "70-79分: 5.71%\n", "80-89分: 31.43%\n", "90分以上: 42.86%\n"]}], "execution_count": 99}, {"metadata": {}, "cell_type": "markdown", "source": "# 数据可视化", "id": "c64b7b0a054d251d"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:51.096290Z", "start_time": "2025-06-21T02:02:51.083291Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "from matplotlib.gridspec import GridSpec"], "id": "9cc86b5eaf8c7141", "outputs": [], "execution_count": 100}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:52.560999Z", "start_time": "2025-06-21T02:02:52.284577Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 1. 绘制班级成绩Top10的柱状图\n", "plt.figure(figsize=(12, 6))\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "\n", "# 将学号转换为字符串，以便在图表中正确显示\n", "top_students_ids = [str(int(id)) for id in top_students['学号']]\n", "\n", "# 使用字符串类型的学号作为x轴标签\n", "bars = plt.bar(top_students_ids, top_students['平均分'], color='skyblue')\n", "plt.title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "plt.xlabel('学号')\n", "plt.ylabel('平均分')\n", "plt.ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.xticks(rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "            \n", "plt.tight_layout()\n", "plt.savefig('班级成绩Top10柱状图.png')\n", "plt.show()"], "id": "e8895a4c36a84f84", "outputs": [{"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 101}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:02:56.732749Z", "start_time": "2025-06-21T02:02:56.558654Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）\n", "plt.figure(figsize=(12, 6))\n", "my_scores_values = my_scores.iloc[0].values\n", "plt.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "plt.title(f'学号 {my_student_id} 的成绩趋势图')\n", "plt.xlabel('作业序号')\n", "plt.ylabel('分数')\n", "plt.xticks(range(len(score_columns)), [f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "plt.grid(True)\n", "plt.ylim(0, 120)  # 设置y轴范围\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    plt.text(i, score + 2, f'{score:.1f}', ha='center')\n", "    \n", "plt.tight_layout()\n", "plt.savefig('个人成绩趋势图.png')\n", "plt.show()\n"], "id": "6768b86351af85e8", "outputs": [{"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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********************************/77rulHj16lE4++eQ6+7fffvvSt771rXx6+PDhdabvnX766XlK2jnnnFO67777SkOHDi21b9++9NOf/rT2OnvuuWd+zBVXXLG04447lrp3715acsklS//4xz8afB6HHnpoqW/fvqVZs2bV7rvmmmvy7cpT3epP33vjjTfyNMLya7jccsuVJk6c2KjX7+yzzy597Wtfy9PayltMlbvnnntKDz74YOnKK68sDRkypPb6Y8aMyZdXmjBhQoPf09tuu6306aefljp16pS/v9OnT89T8E466aR5jiO+f/Fa3XHHHaUvas011yxdf/31n3md8tS5OL6yadOmlV566aXSGmusUbr44otLb775ZumII47Ix3z33Xfn91FM8YvXPaYExrTMmCr6+uuvzzN9L+4rvpcvvvhiae21186PFe+DV155ZZ5jAAA+m+l7AFBFYupSrF4WVTHlKV0x5SkaiEf1SkyHiibQ9UWj8UmTJqW33norN8+ORtKrr756Pj2/LaZtRVVWedW1xojKmag+ieqimDpWruCpX/1TruSqX/EUosolKmzKlVYhVsGLSppLL720wceNBudx+SmnnJKraqJCKU7//ve/z9U58fxj6ldU20T1UDQ7j69RNRQNteu79957c8VNTJcrT4uLld6icieatZenutUX1T0xHewPf/hDPuZopB0N6eN71RhRaRaVPuUtpr5FxVdU+nTv3j0fQ0yZK08RrL9qYvl1jeqhcgVTiAqyqFpae+21cxVUVDnF84vXqlI0No/pczFVMN5bMb0yxJS9uH25iq68xb5Yna/+dLmoeqrfDD7ElL+Y+hePG++VaJZebtxeOQ3vsccey1VMURkV1WnxPorHiymhsfpgnI59sdphVExFBVa8RtEMvVK8r2N66uDBg3PlWlwnpiLGVL6oFosm/TEltP4KhABAw4RSAFBF4oN3hBPl6XQRGq255pq5r84111yTQ4wIY+qLKVMRoERAFGHGv/71r7zqWJye3xbT2uJD/cKuFlcZhEQIFn2dKoOeCAwiGKtUDmliqlilCA5ii1AnjiPEFL8IMKKfVDnoqi+mpUXYVinClAij4vaxgl2EMxEqlUOmuP9vf/vbOZypFLeJ1QHjsv32269O4BT3GX2JGhJTCOP7ESu7xe1j9blYiTCee0w5bIyY+hbHUd4qQ70Ii+L5lqe5RZBS/3sVU+gqX98INEP59YtV/WLKXkzLi9e23MOrLL6PceyxAl+EbHvvvXdtb6yYEhlTAiNYirAoTkeoU3+6XKwAGEFPOTCL1yROx/54PvHeLG9xnA313dp0003zcUYgGKfjWCK0imOoL74/EYrGqon1RfgUoWRMB43piP/9739zIBbPK8ZETP+LcK+x73kAqFZ6SgFAFYjePKF+pVFUM0VT7Qhvol9RucF5iA/XEVrUD3CigfXdd9+dZs6cmS644IIGHy9Cg7je/PoafZbozxTVOxHyVN4++kVF/6AIFiKgCuXKmzjOsggLvvvd7+aQJBpZV1YtRchSDkbqV1wNHz48rbPOOrlheuX9lYOvCG3K4UxUxVSKIKSyQXhUJEVfqggnosqqUjnsqGzoHqI/UVTeRK+peM0rq3Qi+Fp55ZVzVVZjRFXU/AK4CGUi4Inji1Apvp8RulSKICyuVw6K6odS2223XQ7dogrppJNOajDg2WeffXIPp+gZFsFR/ecelXT77rtvGjp0aIOvS1RpRaVY+fWNyrFomB7HGu+Ps846q871I1iN5xThYfm+oil7OWCrr/7jhXifRXDV0Ps6quNChGgxbqK5eTzPCDujuq98DADAggmlAKAKHHDAAbnKZ34Nx4899tg6l8UH+gilYpW9Qw45pM5143oxxW1+YqraP/7xj88VSMWH+j/+8Y/pL3/5S26QXSkaVUclTgQo8RhxjBdffHFab731agOcqPyKaW4RsJUDgrLddtttnmqm22+/PT/Hu+66Kz9e3DaCoVgBruzqq6/OoUiEKhE2RbASzz+moJVF4/CokimLkCJeg5jaFSvvVap/DOVplTHtLAKacuVQrAZXfg0ijIrAMFZQXJRi2mZs8b2KxvLR8LtSTHuLYygHQhEWRkhVrj4rB3Yx5bMcaEWj8wiBwo477lh7XxEKxWsS4WJ5NcF4zLjPqJAqK6+aVxarJMbqeFGJFCpDsnhPl5vpx/4ImKKiLcT3qvyejlA2Arry84jvQQRqN954Y53KwPvuuy+/xpWhZENiKmdMXYwAMyrO4tjqV9gBAAsmlAKAKlB/pbWyCAXiA36EQAvb+ymqRVZbbbX5Xh7hQ/2V9BZGTPOKnkBRNRMh0OjRo2sfL6ZLRajw61//OodkzzzzTJo2bVquSLnllltq7yPCkZimFtUrlc85QpIItOpXv8R1IqjYaKONasO5WG0vQpp4DjFV65577sn3V1797YQTTsi9puJ0vA433HBD7k0U1wsxVe2KK65IJ554Yq6mKT+PeJ1jqmT5seqL+4qtd+/e+flHOBZVXRG6xMpu8TrEsTVGVHdFyDU/8ViV/ZLqh1Lx+kZgFvfxi1/8Iods8brEc49AJ/o/xXFGlVBUuEUlWVSbxfdk4MCBeYpiBGwRVMWKdhFcxfcnXr+YDhpTRqOaLB7juuuuS2eeeWae+lkpgqw99tgjh2dxX/V7gI0aNSq/NyKQKgdT8X2O4KscSlX27oogqzyFMgKlykqpqBqM16R+xVgEXJXicW6++eb8PKNaMF6H+J7HSor1w6vK3lYAQF1CKQBgkYopdbvvvnujbxcf8uNDfDSZjq0spq2Vg5UInSJwiHAqRGhUfqyYihiVLiH6XVWKqXkLE+hERVmEM3H/MU0wqrCiN1VlT6iYLhaVQtGLKY4rqmX+9Kc/5cqbEGFNiMvL/ZpChDcPPvjgAo8hKrEioItQK6ahRbgSTbWj8qt+9dhnidcyKnjKVUuV+xsSQV9lVVdUgz366KM5OCpPHYxpeDG98LTTTsvHE4HMMccckyvT4jWPsCeCsAi34nnEFM4I4eK5RI+wOP4IgWJaZYRQ0TPsuOOOy5VM0acrLi83Kg8ROD3xxBM56Itm8tG/KcKxeI9FxVX0hIoQKwK7qJyLBv7x+DHNMKq84jUvix5UUfUWjcwjOIv3WxxLvL4RHsaxxveuodAwLo/XLQKtsgjg4v0YfbPifRfHU768XK0Vt6uc1gkA1NUmluCrtw8AaOViOlT0XooP6rFKXYQgMT3u84jblqekRYAQPX+iUXdMl6PpRNVZVDpVBmFRORRVcd/4xjfy+ajwivMxhS6+Zz//+c/Tj370o3xZrEoXIUuEVeUm49FjKm4b759ouh6VRuWKs5iCF4FeBHZR0RTVS/VFs/GoLItpk+WKqTPOOCOHQ1GldMQRR6Q777wznX322TmAiutGSFUOG0NUXEWF0sMPP5zfvxEARdVcBECVIliMPlfROD6Cyzj2CKPi+UX4WK6iiueyxhpr5FAqKqSi51f59SmLAC1u01jl+wQAGiaUAoAqFKFBVKVEZUn0RorG5fVXPWtMKDVo0KBcZRMNwHfeeef0s5/9rMEG0hTnjjvuyE3CI3Qsu/TSS3PfrGhMXm7oHSvoRQVUTJuMSqfydLOoPIvqqKhwqhR9vCLkqV+1FaFPNP7efPPN5wl1yuLxYkpfVFVFk/X6vZviz9KowIrKsHIFXEwxLTdI/ywxxa48zS6mX1b2NIvjigqoqOhqSPQEi2b1MU20/kqOISqsotorgrOFEWFgTF+Mqaz1p0QCAHMJpQCgSkVgEauoNfQhvLFiCpRpSrRWM2bMyIFU/amQAMAXI5QCAAAAoHBNuhxI+T9OlavCRD+DKP2Pef5RZh19DMrGjx+fS8yjV0E0xpSnAQAAALRMbZsykIp+ApWB1IQJE3J/gV/+8pe5aWU0nYyGlyGWQ951113TpptumpdWjuasI0aMaKrDBwAAAKAlhlKxtPL+++9fZ19URUUgFY02l19++bzEcHk1n5EjR+ZmkbG0cv/+/dPQoUPz6j4AAAAAtDw1a+E2gVjeN6buHX/88bX7onKqUizzu/rqq+fT48aNy6sDlZfVjVVioloKAAAAgJanyUKpBa1eEqv4nHfeeXn53TB9+vQ6t4llpmOp36lTp+YeU/XFdL/YyubMmZOXNu7Zs6clqgEAAAAWk+gBHqvX9unTJ7Vt27b5hVILcsYZZ+Qlqss9paLxeadOnepcp3PnzmnmzJkNhlLnnHNOOuusswo7XgAAAADmevXVV9NKK62UWlQodf/996dLLrkkPfroo6lDhw55X48ePfLqe5UidevYsWOD93HqqafWVlmF6Ef1pS99KU2cODEtvfTSqSWbPXt2euCBB9I222xT+/oArZ+xD9XJ2IfqY9xDdZrdisZ+5DUx221B+UuzC6UiNBo8eHAOpdZZZ53a/ZtttlnuQ1V5vZieF2FVQ6Kqqn5lVYjrd+3aNbX0N2r01oqpiC39jQosPGMfqpOxD9XHuIfqNLsVjf3y8S+ofVKTrb7XkA8//DA3O999993Tnnvumd5///28xVzEQYMG5b5Sw4cPz9eN1fe222673FcKAAAAgJalWVVK3XPPPXlFvdjqV0WtssoqadiwYbmKasiQIblR1oMPPtikxwsAAABACw2logqqLCqkKs/Xt9tuu6UJEyakMWPGpAEDBuSSNgAAAABaniYPpRqrd+/eaeedd27qwwAAAADgC2hWPaUAAAAAqA5CKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCKNjbb7+d+vXrl1566aXafePHj0+bbbZZWmaZZdKQIUNSqVSqveyhhx5Ka6+9dlphhRXSbbfdttCPc8kll6Tll18+rbrqqun+++9f5M8DWPzjvlevXunCCy9c6Mcx7qF58Tsf4LMJpQAK/uN0l112qfPH6axZs9Kuu+6aNt100zR69Oj09NNPpxEjRuTL3nrrrbTbbrulwYMHp4cffjj/sfrggw8u8HHuvvvudNJJJ6UrrrgiXXPNNemII45I77zzzmJ9bsCiH/ejRo1K119/fXryyScX+DjGPTQvfucDLJhQCqBA++23X9p///3r7Bs5cmR677330vnnn5/69++fhg4dmq666qp82bXXXpv69OmTTjvttLT66qunb3/722n48OELfJzLLrssHXLIIWn33XdPW265Zf566623LrbnBSy+cf+Tn/wk3XfffQt8HOMemhe/8wEWTCgFUKArr7wyHXfccXX2jRs3Lg0YMCB16dIln99ggw3yf07Ll22zzTapTZs2+Xz8kfqf//xngY8Tt9t2221rz2+++eZpzJgxi/jZAEWM+5jmM2HChAU+jnEPzYvf+QALJpQCKFD0lahv+vTpdfbHH6Pt2rVLU6dOneey+CP2tddeW+Dj1L9d165d05QpUxbJcwCKHfcxft99990FPo5xD82L3/kACyaUAmhi7du3T506daqzr3PnzmnmzJnzXNahQ4e8v7H3Wb4/oOWN+9gffWgae5/GPTQ/fucD1CWUAmhiPXr0yM1NK82YMSN17Nhxnss+/PDDvL+x91m+P6DljfvYHx86G3ufxj00P37nA9QllAJoYtEvJlbYKps4cWKuiog/Mhu6LJqgNvY+x44dm1ZcccXFcPTA4h73jz/+eOrZs2ej79O4h+bH73yAuoRSAE1s0KBBuR9EeYWdWIlnu+22yz0mYmnof/zjH3nlrdmzZ+fVdHbYYYd8vTlz5qRp06alUqk0z33us88+6dJLL02TJ09Ob7zxRl7ZZ8cddyz8uQFffNyfd955aaONNsrXM+6hZfM7H6CuBdeCA7BYxbScYcOGpcGDB6chQ4aktm3bpgcffDBf1qtXr3TBBReknXbaKS211FK5HP/UU0/Nl73yyiu5sWk0R+3evXud+9x1113TTTfdlFfuCV//+tfTXnvt1QTPDvii4z7G9xlnnJEvM+6hZfM7H6CuNqWG4vZWKP4j0a1bt/Tee+/lFSlasvjPyV133ZV/YUUDRKB1eP311/MSzrFUdP2pOlHCP378+PTBBx+kvffee6HH/mOPPZZvs9VWW9UuMQ20nHH/7LPP5ssefvjhhf69b9xD8+d3PtDaP+tPX8gMRqUUQDPRu3fvtPPOOzd4Wfx3dKWVVsq/pBoj+kwALXfcxxZ/oDaGcQ/Nn9/5ADX0lAIAAACgukKpt99+O/8n4KWXXqrdF6WqkfIvs8wyeZ515ezChx56KK299tp5vvX555/fREcNULxPP42fgW3Sww+vmL/GeaD1M/ah+hj3QDVp25SB1C677FInkIrlUKNR36abbppGjx6dnn766TRixIh82VtvvZVXpIimgLHk6bXXXpseeOCBpjp8gMLccktKq6yS0vbbt0/nn//l/DXOx36g9TL2ofoY90C1abJQar/99kv7779/nX0jR47MTbCiCqp///55idRY0jRECNWnT5902mmn5ZUlTj/99NrLAFqr+CN0n31SmjSp7v7Jk2v2+yMVWidjH6qPcQ9UoyZrdH7llVfmqXvHH3987b5x48blFSi6dOmSz2+wwQa5Wqp82TbbbFO7ksTmm2+eTjnllCY6eoBF55NPUnr33agIjSrSmi1Ov/lmSr/+dUoNrZFa3rfffimtuWZKFtmB1iPG93PPGftQTRZm3MfHpt13T6ldu8IPD6D1hVIRSDW0ZGDl/gig2rVrl6ZOnZovW2eddWoviyUFp0yZMt/7j6mAsVXed4gVbBq7ik1zUz7+lv48oDWKPxxnzKgJlt55p01F0NSmzr533pm7b+rUz/+pMn4MjB+/SJ8C0AIY+1B9ooJq9dVLaeDAUtpkk5ptgw1K6f/+nw+0ArNb0Wf9hX0OTRZKNaR9+/apU6dOdfZ17tw5zZw5c57Lyvvn55xzzklnnXXWPPvvueee2kqslu7ee+9t6kOAVm/27DZpxoxOafr0jhXbZ5//5JNi/4XZvv2nqV27Bv61CrRIn37aZqF+jhj7UH3jfuLENnm7+uqa823bltJKK81I/ftP+7/tvdSv33upc2fd0aElu7cVfNb/rLym2YZSPXr0yKvvVZoxY0bq2LFjviyandffPz+nnnpqOvHEE+tUSvXt2zftsMMOucqqpSeO8SbdfvvtU4cOHZr6cKDFmDMnpffeq5kaFxVL5alyUbEUlUtvvVXztbKyafr0xTc3plu3UurVK6WePWu+1mzl06X02mtt0hlnLPgP1JEjS2mrrXwwhdbioYeiyfGCr2fsQ/WN+wih5syZ+7dJnH7lla55e+CBL9VeJ6b3lqupYttww1JaaqnF+QyARWF2K/qsX56t1qJCqc022yz3miqbOHFinoIXgVRcdt1119VeNnbs2LTiiivO976iqqp+1VWIb2xz++YOHz48nXfeeWnSpEnpm9/8Zrr44otTr1690nHHHZdPl0Xz9xdeeGGBz+Whhx5KRx99dA7xfvzjH9cJ56A1+fDDuj2YPut0ecrc4lpWOTLyZZctB0sLPt2zZ9ym/Edlw8FXHGv8SIwGpw31mIheMiutlNI227TXXwJakW22qRnbxj5Uj4Ud908/3SY99VRKY8bM3eJ89KesDKqeeSalZ55pk669du7t11orgqqUNt20Ztt445SWXrq45wgsvA7NMLdorIU9/mYVSg0aNCinaRHSHHbYYXn1ve222y73ldptt93S97///XTfffelrbbaKp177rlpxx13bOpD/sLi+UT4dMstt6Q111wzHXPMMWnPPfdMjzzySBo9enS6884705ZbbpmvG6/DgkQQFa/VD3/4wzR48OC8yuHGG2+cm8RDcxYBTDT7XtiQKbYPPlh8x9Ojx8KFS+XT8d/HRd1wOIb8RRfVrLgT9135R2r5sS68UMNTaG2Mfag+Czvu4++Nr3ylZiv76KOUnnyyblAVk08q27nE/dUEVbGq+dz7XWONuSFVBFaxtfBJJUAL06ZUaiiLL/AA2sS86IlplVVWyedvv/32HKYsscQSqW3btunBBx+sbXB++eWX5wBnqaWWSt27d0+jRo1Kyy+//EI9ToRd3bp1S++9916zmr538MEH5+MqV0TFaoPrrrtueuONN9Lqq6+eJk+enJ9v/ZK+u+66K+20007zpI8XXnhh+v3vf5/vJ17b2267Ld10003pmmuuKfR5Ud3ip0oERvUrlT4rZIpAanH9NFpiibkh0sKETBFItW9GkX0sAR0r7lQuEd23b80fp3vt1ZRHBixOxj5Un0U17mO9p3JQ9Z//1Hx94om6QdX8rL763KCqHFZ16/b5ng/QOLM/47N+S7OwGUyTf+yqn4lFlc+ECRPSmDFj0oABA1LPmOPyf2JKWlRHPfvss2ngwIHzhDUt0dtvv53WX3/92vPlaqhnnnkmzZkzJ2200UY5mIrqsCuuuCJ96Us1c8XnZ9y4cbkqKgKpsPnmm6dTTjllMT8LWrv4A6bca2lhKpjidMXil4tU27Y1U98aM1Wupa9tEH+ExhLQDzzwSRo58vH0zW9uZNoOVAFjH6rPohr30cXky1+u2co+/rimgqqyoiqCqthf6fnna7b/9//m7uvff96gapllvuCTBWgOoVRDevfunXbeeecGL+vXr1/eWotNNtkk/eUvf8nT7aIybMSIEbl/VvSXiul85f5SJ5xwQjryyCPTX//61wWmkeXKshCJ5JQpUwp4JrQUkQNHs+/GTJObNm3xHU/0MmjMNLnu3WuCqWoTf4xGQ+MPPoiQekMfSqFKGPtQfRbXuI/+l+Upet/9bs2+CKSefrpuUDVu3Lz/XJwwoWa78ca5+1ZdtW5IFV+j2hygxYdS1eSkk07KjckjnIopi48++mi6+uqr0wEHHJC3sksvvTSHcRE6xfXmp3379nUavHfu3Hmhl2KkZYo/GhpTwRRfK5thLkpRYTp3FbmGw6XKfbE1sB4BAAAFiKBqo41qtu98Z26FfDmoKk/9e/zxmt5VlV58sWa76aa5+6IjS2VFVWwVE18A5iGUamLRGyuamseqer/5zW/StGnT0v777z/P9ZZbbrk8ne+1115Lq8a/JeYjViqMZudlM2bMSB3jtw0twpw5KU2d2riQ6f33F9/xRFVSY6bJxVThRd3sGwCA4sQ/GTfcsGY7/PCaffEPzWiSXllRFUFVrIRc6aWXarb//d+5+6L7SP2gKv52BAhCqWaiT58+eQW+6BsVfaWGDBmSV80rB1TR1D2m9/WNToefIab+XXfddbXnx44dm1ZcccXFfvw0LIrUGlPBFH2bIphaHKIi6bOafdffF+XXLby3HgAAi0AsABNtcGM79NC5QdVzz9UNqsaOrfn7t9Irr9Rst946d198pKk/9W8h168CWhmhVDMRvaPWWmuttMcee+TzG264YfrpT3+aVxf89NNP07HHHptX6uvSpUvuyB9T8uJr/Y780Sj++9//frrvvvtyc/Rzzz03N4fni4tfvLFCXGNCpvr/PVpUohopSqEXtoIptiWXVMUEAMCiC6rWXbdmO/jgmn2ffjo3qCpP/Yugqn5l/6uv1mx//vPcffF/9PoVVb17F/ucgOIJpZqBqVOn5vCoson5gQcemJ566qm0995758qpOD906NDay48//vgcSO2zzz517iuaol9wwQV5CclYnTCmB0bzdOZt9j1jxrwh0meFTDGtbnGJwGhhK5jia6x2otktAADNSfx9GmsuxXbQQXODqljNr35FVfwtXmny5Jrt9tvn7lthhXmDqj59in1OwOIllGoGlllmmfROzNuq55xzzslbQ6688socPDXk6KOPztVRzz77bBo4cGAOp1q7WDmkHCAtbD+m+svfLspfxo2pYIrtM3rXAwBAixV/G6+1Vs1WXscp2lW88ELdoCoqq6ZPr3vb115L6S9/qdnKonqqctpfbFFlZUYAtExCqVYqVuqLrSWKX1Lvvde4aXL1f4EtSt26NS5kiuu3bbv4jgcAAFqy+Ft5jTVqtsGD534GmDCh7tS/+DptWt3bvv56SnfeWbOVLbfcvBVVK60kqIKWQCjFYhd9lRY2XIqvUTQWZb6LQyxEuLDhUpyOvk0WLwQAgMUfVK2+es22335zW268+OK8FVX122q8+WZKI0fWbGXxt3xlNVVssRKgoAqaF6FUCxNhzUMPtUkPP7xiWnLJNmmbbYrtLRSPH82+GzNN7oMPFt/xxApxjQmZYiajX0QAAND8xd/t/fvXbPvuOzeoeumlukFVbPEZpVJ8Hrn77pqtLP7hXH/q3yqr+HwATUko1YLccks0OE9p0qT4tn05nX9+TVnqRReltNdejb+/+IEegdHCVjDF1/hhH7dbHKKv0sKGS/E1AqlY9QMAAKgOESBFl5LYyms+xeeTl1+uO/Uvtvj8UilmZNxzT81WFp8p6ldUxX0LqqAYPtK3oEAqfujWD4RihYrYf/PNKe26a80P2oWtYIrTs2YtvvLb+E9EY0KmLl0Wz7EAAACtVwRIUfEU29571+yLz02vvjpvRVV8BqoU/3S/776arax793mDqqjWElTBoieUagFiylxUSDVUoVTe11BgtSgtvXTjmn0vs4xm3wAAQNOIACl6SMW25541++LzUvxTv35Q9cYbdW8bzdXvv79mK4vFjMpBVfnraqv5zANflFCqBXjkkZiy99nXaUwgFVPeGlPBFBVPnTt/4acBAADQpEFVtD+Jbffd536OmjKl7rS/2F57re5tY3XwBx6o2cq6dk1p443rVlRFo3ZBFSw8oVQLUP8H4vzED8C11lpw0BQ/PJWeAgAA1S4+F624Ys0W7VAqP4NVrvgXX6PKqtL06bEIVc1WFgsr1Q+q1lij2MWpoCURSrUAK6ywcNe74oqUtt56cR8NAABA6/8MtssuNVtZTPOrP/Wv/oyW99+vmekSW9mSS9YEVZV9qqKYQFAFQqkWYeDAmhLTSOYbmqZXLkON6wEAALDoLb98SjvtVLOVvfnmvFP/Xnml7u1ixfO//71mK4tFnjbaqG5FVQRVVhen2njLtwCRoF90UU0z8wigKoOp8jS8Cy+UtAMAABRpueVS+sY3arayWOGvMqiK0y+9VPd2M2em9M9/1mxlSyyR0oYb1g2q1llHUEXr5u3dQuy1V0o331yzCl9liWhUSEUgFZcDAADQtKKP74471mxl77wzb0XVxIl1b/fhhyk9+mjNVhYLTpWDqvL0v3XXTalDh+KeDyxOQqkWJIKnWCXigQc+SSNHPp6++c2N0jbbtFchBQAA0IzFiubbb1+zlb37bkpjx9YNqiZMqHu7jz5K6V//qtnKOnVKaYMN6lZURVDVsWNxzwcWFaFUCxMB1FZbldIHH0xOW221oUAKAACgBerRI6Wvf71mK5s2bW5FVfnr88/Xvd2sWSk99ljNVhaB1Prr1w2q1luvJsCC5kwoBQAAAM1A9+4pbbttzVb23nvzVlT99791b/fxx3MvK4spfhFUVa76F+djSiA0F0IpAAAAaKa6dUtp661rtrLp01N6/PG6QdVzz9VdFGv27Jpqq9iGDavZF03To4KqsqIqpgIKqmgqQikAAABoQbp2TWnQoJqtbMaMukFVhFHPPpvSnDlzr/PJJzXXie2qq2r2RUuY6ElVGVRFc/VYDRAWN6EUAAAAtHBLL53SwIE1W9kHH8xbUfXMM3WDqk8/TemJJ2q24cPnBlXrrFN36t9GG6XUpUvxz4vWTSgFAAAArdCSS6b01a/WbJVBVQRQlUHV00/XhFNlcfrJJ2u2P/6xZl/btimtvfbckCoCqwiqllqq+OdF69G2qQ8AAAAAKC6o2mKLlH7wg5rKqAioYurfqFEp/e53KR1+eM30veg/VSmqq556KqWrr07p+ONrKrJiGmFUVB10UEoXXpjSI4+k9P77TfXMmqdhw4alvn37pi5duqStt946vfjii2nEiBGpTZs282xXx4u7ABtssEGd2xxxxBGpJVMpBQAAAFUs+kcNGFCzlX300bwVVePH1/SlKovG6jEdMLZrrqnZ16ZNSmuuWXfq38Yb1wRY1WbChAnpZz/7WbrttttSr1690llnnZUOPfTQdN9996U99tij9nrvv/9+2njjjdNXv/rV9Fx0rJ+PmTNn5vt88803U4dYXjGl1KlTp9SSCaUAAACAOmJFvs03r9kqg6oIpiqDqpjiFyv9VQZV0WA9tuuum7t/jTXqTv2LLVYWbM3Gjh2bBgwYkDaJJ5uiCu3w9K1vfSt17Ngxb2WXXnpp2nPPPVP//v0/M5SK+4tKqWWXXTa1FkIpAAAAYKGCqi9/uWYrmzVrblAVK/7F16iw+vjjurf9739rtuuvn7tvtdXqrvoX2U337qnVWGedddL999+fHn/88dSvX78cPm2//fZ1rvPRRx+liy66KP3rX/9a4P39+9//TpMmTcqh1OzZs9PgwYPThRde2KKrpYRSAAAAwOcSeUg5VCqLQCr6T1VWVEVQFQFWpRdeqNluuGHuvv796079i9M9eqQWG0rts88+eWpeiGCqfvh03XXXpa985StplVVWyUHTZ4kqqq997WvpzDPPTNOmTUsHHHBAuuCCC9Ipp5ySWiqhFAAAALDIxMy0yGFiK/fhjrwlVvmrDKrGjauZElhpwoSa7aab5u7r169uSBVfe/ZMzV5UNt1xxx3p0UcfTWuttVY699xz00477ZT3R5PycPnll+eQaWHEdSudfvrp6be//a1QCgAAAGB+oi93rOoXW6zwF6JpejmoKk/9e/zxlD78sO5tJ06s2W6+ee6+lVeuO/Uvtl69UrNy/fXXp/322y9XQoWf//zn6bLLLkvjxo1LG220UXrhhRfyVn9K38Jabrnl0uTJk1NLJpQCAAAACte+fUobbFCzHXbY3KAqmqRXVlRFUDVzZt3bvvxyzXbLLXP39e07b1C13HKpycyZMye9/fbbtednzJiRV9D79NNP8/kbb7wx7bLLLrUr6S3IFltskW/TN55oSmnUqFFp5UjnWjChFAAAANBsgqr11qvZDjmkZl9kOLEoXWVQNXZsSh98UPe2r75as/35z3P3rbTSvM3Ue/cu5rkMHDgwHXLIIXn1veWXXz4NGzYs9e7dO6+gF/7617+mQw89dJ7bRb+oHj16pHbt2tXZv+6666ajjjoqnXHGGenZZ59N5513XrrkkktSSyaUAgAAAJqtyGbWWadmO+iguUFVrOZXnvZXngL4/vt1bztpUs12221z9/XpM29F1QorLPrj3nvvvdMzzzyTV8h77bXX0nrrrZduvfXWXBn14Ycf5qbnV1xxRYPT8saOHZun+FX6zW9+kw477LC0zTbb5Ov8+te/zqFXSyaUAgAAAFpcULX22jXbAQfU7JszJ6Xnn69bURVB1YwZdW87ZUrNdscdc/dF9VT9oCrCq//rR/65RDPz0047LW/1LbHEEmlW/eUI/8/HH3/c4JS+7t2751CrNRFKAQAAAC1e27Yprblmzbb//nODqljNr35Q9d57dW/7+usp3XlnzVa2/PLzrvoX0wG/SFBFXUIpAAAAoNUGVauvXrPtt1/NvlKpJqiqnPoX27RpdW/7xhsp3XVXzVa27LLzVlRF33FB1ecjlAIAAACqRgRIq61Ws+2779ygauLEuiFVbFOn1r3tW29Fg/KaraxXr7mVVOUtFsVrTFD16acpPfRQm/TwwyumJZdsk7bZpmaKYmsnlAIAAACqWgRIq65as33rW3ODqpdfnjeoeuedurd9++2U7rmnZivr2XNuUFX+2q9fw0HVLbekdPzx0ZA9Ipovp/PPr5kmeNFFKe21V2rVhFIAAAAA9USAtMoqNdvee88Nql55Zd6pf1FBVSmCq3vvrdnKlllm3oqqxx+vCcHifitNnpzSPvukdPPNrTuYEkoBAAAALGRQFVPzYttzz5p9EShNmlS3kXp8jZ5UlWIq4N/+VrNV3l+pXiBVvs+47H/+J6Xdd2+9U/mEUgAAAACfU4RH0ew8tj32mBsqTZky79S/WOWvUqmBQKrysldfTemRR1LaeuvUKgmlAAAAABZxULXiijXbbrvN3R9BVbmS6vbba04vyGuvpVZLKAUAAABQgD59arZddklpq61SXmVvQVZYIbVabZv6AAAAAACqzcCBNavsNbQiX+W0wLheayWUAgAAAChYu3YpXXRRzen6wVT5/IUXtt4m50EoBQAAANAE9torpZtvruk9VSkqqGJ/XN6a6SkFAAAA0ET22iul3XdP6YEHPkkjRz6evvnNjdI227Rv1RVSZUIpAAAAgCbUrl00Pi+lDz6YnLbaasOqCKSC6XsAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFK5ZhlLDhg1Lffv2TV26dElbb711evHFF/P+8ePHp8022ywts8wyaciQIalUKjX1oQIAAADQGkKpCRMmpJ/97GfptttuS88++2zq379/OvTQQ9OsWbPSrrvumjbddNM0evTo9PTTT6cRI0Y09eECAAAA0BpCqbFjx6YBAwakTTbZJH3pS19Khx9+eHrhhRfSyJEj03vvvZfOP//8HFQNHTo0XXXVVU19uAAAAAC0hlBqnXXWSffff396/PHHcwh16aWXpu233z6NGzcuh1UxpS9ssMEGuVoKAAAAgJanfWqGodQ+++yTNt5443y+X79+6V//+lf65S9/mU+XtWnTJrVr1y5NnTo195iqL6b7xVY2ffr0/HX27Nl5a8nKx9/SnwfQOMY+VCdjH6qPcQ/VaXYrGvsL+xyaXSj173//O91xxx3p0UcfTWuttVY699xz00477ZS23Xbb1KlTpzrX7dy5c5o5c2aDodQ555yTzjrrrHn233PPPbXVVi3dvffe29SHADQBYx+qk7EP1ce4h+p0bysY+5HVLIw2pWa2hN0JJ5yQ2rZtm84777x8Pg6vZ8+e6eSTT86r7/3pT3+qvW737t3T888/n5ZddtmFqpSKFf3efvvt1LVr19TSE8d4k8a0xg4dOjT14QAFMfahOhn7UH2Me6hOs1vR2I8MplevXrkt02dlMM2uUmrOnDk5OCqbMWNGTtjat2+fRo0aVbt/4sSJOXTq0aNHg/cTVVX1K6tCfGNb+je3NT4XYOEZ+1CdjH2oPsY9VKcOrWDsL+zxN7tG5wMHDky33HJLuuCCC9J1112X9thjj9S7d+903HHH5aRt+PDh+Xqx+t52222X+0oBAAAA0LI0u0qpvffeOz3zzDPpwgsvTK+99lpab7310q233ppTtmHDhqXBgwenIUOG5Cl+Dz74YFMfLgAAAACtIZSKVfVOO+20vNW32267pQkTJqQxY8akAQMG5F5TAAAAALQ8zS6UWpCYyrfzzjs39WEAAAAA8AU0u55SAAAAALR+QikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAACieUAgAAAKBwQikAAAAAmn8o9fHHH6ejjz76M69z8cUXpwkTJnyR4wIAAACgFWvf2Bt06NAh/fGPf0xTpkxJffr0SWussUbacsst05e//OXUvn379M9//jOdeuqpafPNN0/9+/dfPEcNAAAAQHWFUm3atEnLLLNM+t73vpeDqRdffDGdcsop6b///W8aPHhwuvrqq9Pvf//79JWvfGXxHDEAAAAA1RNKDR8+PPXt2zcNHDgwde7cOX3jG9+ovezxxx9PP/zhD9Pll1+eNt1007T//vsvruMFAAAAoJp6So0ePTr9z//8T+rWrVt6++230+mnn54OOOCAPEUvqqa+/e1v5/2rrrpqOvHEExfvUQMAAABQHZVSl1xySf76xBNPpD/96U9p1KhR6cEHH8xT984+++za61166aVpww03TPvuu2/aYostFs9RAwAAAFAdoVRMz3vvvffSmmuumaZOnZquueaadPLJJ6dVVlkl7bLLLumQQw5Ju+66a57ed9ppp6XJkycv3iMHAAAAoPVP3xs6dGgaNGhQev/99/MqewceeGAOo77zne/kqqlrr7029evXL6299trp4IMPTvvss88XPrgIvSLoKhs/fnzabLPNcqP1IUOGpFKp9IUfAwAAAIBmHErtt99+6a677spT9u6///7UpUuXdOutt6ZJkyblPlN//vOf07bbbpsve/nll7/wgcU0wZgKeNFFF+Xzs2bNygFVNFKP/lZPP/10GjFixBd+HAAAAACacSi11157pd133z1tueWW6YUXXsjVUnvssUeerhfT+R555JH07LPPpt/97nfpmGOO+UIHNWfOnHTkkUemE044ITdODyNHjszTB88///zcXD0qt6666qov9DgAAAAANPNQ6qtf/WoOhLp27ZrDqVdeeSV17tw5VyyFn/zkJ6lHjx45vPrwww/TmDFjPvdBXX755enJJ5/M/apuv/329PHHH6dx48alAQMG5AqtsMEGG9Q+NgAAAACttNH53/72t7zSXvRxeu6559LFF1+cq6RiKl2EUw8//HCaMmVKvu4BBxyQ/v73v+epdo0VPavOOOOMXCEV0wBjpb+f//zn+bGiZ1VZmzZtUrt27XKVVvSYqi+m+8VWNn369Px19uzZeWvJysff0p8H0DjGPlQnYx+qj3EP1Wl2Kxr7C/sc2pQa2S08qpbizqMa6rXXXkvrrbdebnQe0/rKZs6cWVvR1FhXX311Ovroo3MlVq9evdInn3yS1l9//fT666+nww47LFdrlfXt2zc9+uijacUVV5znfs4888x01llnzbP/uuuu+9zHBgAAAMBni1xo//33z22YYsbdF66UKuvYsWPeDjrooPTBBx+ku+++u04gFb5I6BON02OaXgRS+QDbt89T9aJf1VtvvVXnujNmzMjH0pBTTz01nXjiiXUqpSLE2mGHHT7zBWkJIhS899570/bbb586dOjQ1IcDFMTYh+pk7EP1Me6hOs1uRWO/PFttQRodSoWYUveXv/wlr7S3qK200kq5CqtSTOO78MIL85TBsokTJ+bpedHHqiGdOnXKW33xjW3p39zW+FyAhWfsQ3Uy9qH6GPdQnTq0grG/sMe/0I3Oy2655Zb03e9+N/32t79N3/ve99JOO+2UjjrqqHT22Wen4cOHp3/961/pi9h5551zA/Nodh5VU/E40eQ8GqhH0haPEWL1ve222y73lQIAAACgZWnbmF5SP/rRj/K0vcsuuywHU+PHj0977LFH6tOnTw6Qbr755vTNb34zNyr/vHr27Jnuuuuu9Mc//jGtscYa6aKLLko33nhjnno3bNiw9IMf/CBP7bvtttvSr371q8/9OAAAAAA0nYWavvf888+nQYMGpY033jiNGTMmrbXWWunTTz/NK+AdccQRqW3budlWzH888sgjG2wyvrC++tWv5ubp9e22225pwoQJ+Rii71QEWAAAAAC00lBq1VVXzVVQERYtSL9+/dKQIUPS4tK7d+88xQ8AAACAVh5KRd+mhgKpUqmUbr/99rw6XgRXYbXVVssbAAAAAHzhnlLHHntsnppXXzQ4X3/99XP/pz/84Q8Le3cAAAAAVLGFCqXmzJmTll566bTvvvvmFe+eeuqp3FMqxGp7U6dOzT2kTj/99HTSSSct7mMGAAAAoBpCqWhkPnTo0PTKK6+kddddN22++ebpggsuSFtttVUOpzp27JgGDx6c/v73v6frr78+XXLJJYv/yAEAAABo/dP3QlRLXXTRRemuu+7KoVT//v1zIFW2yiqrpGuuuSb9+Mc/Tu+8887iOF4AAAAAqi2UKosKqaiKiql7U6ZMqXPZNttskyulevbsuaiOEQAAAIBqXH2vIbHC3rhx4/LUvvoOPPDAL3pcAAAAALRibb/QjRsIpAAAAABgQaRKAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABROKAUAAABA4YRSAAAAABSu2YdS3/jGN9KIESPy6YceeiitvfbaqVevXun8889v6kMDAAAAoDWGUtdee226++678+m33nor7bbbbmnw4MFp1KhR+bIHHnigqQ8RAAAAgNYUSr377rvphz/8YVpzzTXz+Qih+vTpk0477bS0+uqrp9NPPz1dddVVTX2YAAAAALSmUCoCqT333DMNGDAgnx83blzaZpttUps2bfL5zTffPI0ZM6aJjxIAAACAz6N9aoZiWt7f/va39NRTT6Vjjz0275s+fXpaZ511aq/TtWvXNGXKlPnex6xZs/JWFrcPs2fPzltLVj7+lv48gMYx9qE6GftQfYx7qE6zW9HYX9jn0OxCqY8++igdddRR6bLLLktLL7107f727dunTp061Z7v3Llzmjlz5nzv55xzzklnnXXWPPvvueee1KVLl9Qa3HvvvU19CEATMPahOhn7UH2Me6hO97aCsf9ZeU2zDqXOPvvstNlmm6Wdd965zv4ePXrkZudlM2bMSB07dpzv/Zx66qnpxBNPrFMp1bdv37TDDjvkKquWnjjGm3T77bdPHTp0aOrDAQpi7EN1Mvah+hj3UJ1mt6KxX56t1uJCqeuuuy6HT927d69N12688cZ8esstt6y93tixY9OKK6443/uJqqrKyqqy+Ma29G9ua3wuwMIz9qE6GftQfYx7qE4dWsHYX9jjb3ah1COPPJI++eST2vMnnXRSbnZ+6KGH5kqn++67L2211Vbp3HPPTTvuuGOTHisAAAAAn0+zC6VWWmmlOueXWmqp1KtXr7xdcMEFaaeddsr7opJqxIgRTXacAAAAALSiUKq+yuDp6KOPztVRzz77bBo4cGAOpwAAAABoeZp9KFVfv3798gYAAABAy9W2qQ8AAAAAgOojlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAAonlAIAAACgcEIpAAAAAArXLEOp2267La266qqpffv2aaONNkrPPPNM3j9+/Pi02WabpWWWWSYNGTIklUqlpj5UAAAAAFpDKDVhwoR02GGHpV/+8pdp8uTJaY011khHHHFEmjVrVtp1113TpptumkaPHp2efvrpNGLEiKY+XAAAAABaQygVVVERSO27775p+eWXT8ccc0waO3ZsGjlyZHrvvffS+eefn/r375+GDh2arrrqqqY+XAAAAAA+h/apmdlll13qnH/uuefS6quvnsaNG5cGDBiQunTpkvdvsMEGuVoKAAAAgJan2YVSlT7++ON03nnnpRNPPDG98MILqV+/frWXtWnTJrVr1y5NnTo195iqL6b7xVY2ffr0/HX27Nl5a8nKx9/SnwfQOMY+VCdjH6qPcQ/VaXYrGvsL+xyadSh1xhlnpCWXXDL3lPrpT3+aOnXqVOfyzp07p5kzZzYYSp1zzjnprLPOmmf/PffcU1tt1dLde++9TX0IQBMw9qE6GftQfYx7qE73toKxH1lNiw6l7r///nTJJZekRx99NHXo0CH16NEjr75XacaMGaljx44N3v7UU0/NFVaVlVJ9+/ZNO+ywQ+ratWtq6YljvEm33377/NoA1cHYh+pk7EP1Me6hOs1uRWO/PFutRYZSEydOTIMHD86h1DrrrJP3bbbZZunKK6+sc52YnhdhVUOiqqp+ZVWIb2xL/+a2xucCLDxjH6qTsQ/Vx7iH6tShFYz9hT3+Zrf63ocffpibne++++5pzz33TO+//37eBg4cmJO24cOH5+vF6nvbbbdd7isFAAAAQMvS7CqloudTrKoXW/3KqGHDhuUKqiFDhqS2bdumBx98sEmPFQAAAIBWEkpFhVSpVGrwslVWWSVNmDAhjRkzJg0YMCD17Nmz8OMDAAAAoBWGUgvSu3fvtPPOOzf1YQAAAADwBTS7nlIAAAAAtH5CKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHBCKQAAAAAKJ5QCAAAAoHAtLpQaP3582myzzdIyyyyThgwZkkqlUlMfEgAAAACtOZSaNWtW2nXXXdOmm26aRo8enZ5++uk0YsSIpj4sAAAAAFpzKDVy5Mj03nvvpfPPPz/1798/DR06NF111VVNfVgAAAAANFL71IKMGzcuDRgwIHXp0iWf32CDDXK11PyqqmIrizArvPvuu2n27NmpJYvjnzlzZnrnnXdShw4dmvpwgIIY+1CdjH2oPsY9VKfZrWjsz5gxI39dUMulFhVKTZ8+PfXr16/2fJs2bVK7du3S1KlTc4+pSuecc04666yz5rmPytsDAAAAsPjCqW7durWOUKp9+/apU6dOdfZ17tw5J4n1Q6lTTz01nXjiibXn58yZk6ukevbsmcOslizCub59+6ZXX301de3atakPByiIsQ/VydiH6mPcQ3Wa3orGflRIRSDVp0+fz7xeiwqlevTokVffqxRPsmPHjvNcN8Kr+gFW9+7dU2sSb9KW/kYFGs/Yh+pk7EP1Me6hOnVtJWP/syqkWmSj88022yyNGjWq9vzEiRNz36gIqwAAAABoOVpUKDVo0KBczjZ8+PB8Plbf22677XJfKQAAAABajhbXU2rYsGFp8ODBaciQIalt27bpwQcfTNUmpiWeccYZ80xPBFo3Yx+qk7EP1ce4h+rUqQrHfpvSgtbna4Zef/31NGbMmDRgwIDcuBwAAACAlqVFhlIAAAAAtGwtqqcUAAAAAK2DUAoAAACAwgmlAAAAACicUAqgldAiEABaL7/noTqV6o391vazQCjVCpTflK3tzQl8tvpjvk2bNg3uB1qvTz75JM2ZMyefNvahdSv/nv/ggw+a+lCAgpRKpdqxf9NNN6XJkyfXnm8thFKtQPlN+dRTTzX1oQBNMPZ/97vfpeOOOy6dd9556fnnn8/7fTiF1u2NN95IJ5xwQtp2223TiSeemMaPH9/q/kgF6orf7XfeeWf6yle+kqZMmdLUhwMUGEgde+yx6ZRTTkkdO3ZMrY1QqpV49dVX06BBg9Ltt9/e1IcCFGjIkCHpjDPOSK+//nq644478vlJkyb5cAqt3Mknn5z+85//pN122y29//77OZSOrwJpaL3id/sWW2yRXnjhhXT00UcLpqCVa/N/f8/HP5+vvfbadNttt6Vll122znVaw+/99k19ACwa7dq1y3+Mfv/730/Tp09PBx54YFMfErCYvfLKK+n+++9Pf/vb39JGG22U7rvvvvTLX/4yvfjii2mllVaq898VoPWIisjRo0fnUCr+YxqB9NChQ1OnTp3qTOM1/qH16dChQ1p99dXz7//DDjssDR8+PPXp06epDwtYTO666648K+K1115Lyy+/fPr73/+ennnmmbTccsul9ddfP6266qp5Gn/bti233qjlHjl1dOvWLf/n5Ic//GE67bTT0jXXXNPUhwQsZksuuWT6+OOP07vvvpvPb7fddqlr167pwgsvzOd9IIXWqWfPnnl8/+Mf/8jnYwpfTOeLf0ydc845ady4cabxQisUY3rppZfOFZJ/+MMf0hprrJH23Xff2r8D3nzzzaY+RGARGzRoUFp55ZXzP6DuvffetPPOO6errroq/epXv0p77bVX/lsgAqmW/DtfKNVKxAfT+C/Jfvvtl+ea/vSnPxVMQSvXvn37/ItpxRVXrG10/O1vfztXTYZPP/209rot+RcVUFd8KP35z3+eVllllTy2r7zyytS3b980Y8aM3F8y/hbQYwpan/KYjgrJG2+8MV188cWpf//+afDgwXmWxIgRI9Ls2bOb+jCBRWTOnDlpqaWWSjfffHO6+uqr81i/7LLL0qOPPpquu+66tOOOO6ZDDz00/fe//23Rv/OFUi1U5YfNmTNnpmWWWSb98Y9/TL17904HHHCAYAqqYOxHheQxxxyT1lxzzdqS3TgdH0Zjal9M643rP/fccy36FxVQd+zH9J0ddtgh9evXL4/t+CP1oYceStdff32ulIzpvCNHjszXFUhD6xj3s2bNqj0dH0TL1VHx9//LL7+cbrjhhvTVr341/3wAWsfYb9u2bS4+WWedddL222+f/wG14YYb5sviH1NHHXVUnsb373//u0X/zhdKtSARPsV/REJ82Jw6dWraf//909133533lT90Rpp68MEH52AqGiBHUzSgdY39+BB666235nLeUK6UioA6/iDt3r17Ph9zzWM6D9C6xv4SSyyR/2iNP0Cjx0RZr169Uo8ePdK0adPyeYE0tI6/96OvTNlaa62V3n777dzw/Hvf+14e57vuums+HdcHWs/Yv/POO/Pv/PhnVKzAt+6669beJoKpjz76KPebasm/84VSLUj8B+Sss86q/YAZHz7jDXj22WfnP0rjg2g5He3SpUsOpmIlriOOOCL985//bOKjBxbl2I/V9spjP5Qrpb70pS/lhofRU2bjjTfODc+jnB9ofWM//mgt/wEa0/b++te/5tU34z+mcV2g9f29X57CG9P29thjj9z0OBY6ueWWW3JfOWMfWtfY/9nPfpb/+fyVr3wlHX744fmyyZMn58XNYnGjCLOilUdLJpRqAT744IP835FYYSNW1vrLX/6SfzGF3/72t/kX0//+7//Oc7sIpg466KCcrm655ZZNcORAUWO//Idq9JKIX2BbbbVVns57zz331KmkAlrX2I+xHU3O4wNpTOfdc889c3n/SSed1MTPAlhc4z4C6Wh+HNWS0Wum/IH0yCOPbNFTeKAafbCAsR+LGJXH/ieffJLGjh2bvv71r+fqyFjkaLXVVssVVS1aiWbvF7/4RalNmzal66+/Pp8fMWJEaYsttij98pe/zOcPP/zw0j777LPA+/n0008X+7ECTTf2y2P8+OOPLx111FHz7Ada7+/9GTNmlCZPnlx64YUXavcZ+9C6x/20adPy1zlz5jTBEQNNNfZHjx5deuihh0q33HJLq/id376pQzEWLPpHxNScH/3oR/k/H4ccckj+evnll+e+MdHQfLPNNsu9paLxYaW4Xrm0vzy9B2idY788xs8888zanlJRRWHsQ+v/vR/9JGMrV0jEV2MfWve4jwVPgOob+5tuumn+Wv6d39L/3hdKtQCxuk6U40bPqJNPPjnvi6UfQ7xZYwnIKOmLPjJRxhslvVHyt8kmm+Tl4oHqGfsx7zzG/i677OJDKVTx7/2W2uwUqpW/96E69VsEY7+l/70vlGrGKhPPWEknVtSIN2KkqJVv1uHDh+dGaNFDJsRy0LHq3t/+9rcmPHqgKcZ+VEmVx74PpdCy+L0P1ce4h+pk7M8llGqGygloNCzu1KlTOu200/Jyj1GuF2/SuOzUU0/N14s3a7yhhw0blnr27Jkv+/GPf5xGjx6d01Og5TD2oToZ+1B9jHuoTsZ+A5q6qRV1zZ49O38dO3ZsadCgQaWpU6eW9t1339KAAQNKffv2LV1zzTW5qdmVV15ZWnnllfP58Je//KXUo0ePUrdu3UqPPfZYEz8LoLGMfahOxj5UH+MeqpOx3zCVUs1ApKRPPPFEXs41lnB/+umn87KOBx98cG5udsMNN6TnnnsuXXvttem4445LK6+8cho6dGj6wQ9+kFPUuM0rr7ySl4j85z//mdZdd92mfkrAQjD2oToZ+1B9jHuoTsb+QphPWEWB/vnPf5bWXnvt0tNPP12aMmVKaY011ij97ne/y5fNmjWrNHLkyNolHkeNGpWXifzkk09K77zzTj4dS0guvfTSrTI1hdbM2IfqZOxD9THuoToZ+wvWstu0txJbbLFFWm211dKxxx6bttxyy7T99tun73//+zlVXW+99dIDDzxQ2wRtwIABac8998zzSaMh2qRJk1LXrl3To48+mr785S839VMBGsHYh+pk7EP1Me6hOhn7CyaUaiLRuCwalF155ZX5/N57750efvjhtN122+U3X5zeeuut08Ybb5x+9atf5eucd955adasWfmNGS666KJc2hed99dZZ50mfT7AwjH2oToZ+1B9jHuoTsZ+Iy1ENRWLwbvvvlv67ne/Wxo4cGA+P2nSpNKKK65Yuv3220u/+tWvcole+bJw4IEH5lK/Dz74IJ///e9/n0v5Ro8e3WTPAWg8Yx+qk7EP1ce4h+pk7DeOSqkmsswyy6S99torJ6ijRo3Kjc+WX3751KZNm3TkkUemU045JXXp0iW9+OKL6eijj07jx49PTz75ZN734Ycf5pK+f//732nTTTdt6qcCNIKxD9XJ2IfqY9xDdTL2G6dNJFONvA2LQLzs8abccccd07Rp09L111+fhg8fntZff/207777ptdeey396U9/ShdccEFq3759evXVV/PtPv7449SxY8dcEhhvVqBlMfahOhn7UH2Me6hOxn7jqJRqIvEmDZtsskl67LHH8rKQ0dQs3qTxJl5hhRXSoYcempeCXHXVVdMzzzyTr9+hQ4f8tZrepNCaGPtQnYx9qD7GPVQnY79xhFJNpFygFm/Ofv365TdslPKNGTMmv4nj8uWWWy595zvfSbvuumv61re+lbvul9/gQMtk7EN1Mvah+hj3UJ2M/cZp38jrs4iU33CRkkZJXywPudRSS6WjjjoqXXHFFfmNG3r37p0GDx6c5syZk+ehAi2bsQ/VydiH6mPcQ3Uy9htHT6kmFss+3nnnnWnQoEHp/fffTxdffHF66KGH6rxZQ7XNK4XWztiH6mTsQ/Ux7qE6GfsLRyjVzEycODH97ne/S//4xz/ShRdemAYMGNDUhwQUwNiH6mTsQ/Ux7qE6GfsNE0o1sw794aWXXkpnnnlmeu6559IDDzyQOnXqVLXzS6G1M/ahOhn7UH2Me6hOxv5nE0o1U88//3yedxrzUIHqYexDdTL2ofoY91CdjP26hFIAAAAAFK5t8Q8JAAAAQLUTSgEAAABQOKEUAAAAAIUTSgEAAABQOKEUAAAAAIUTSgEAfAGPPfZYmjhxYu35m2++OT3xxBOL/XEnTZqULrnkksX+OAAAi4tQCgDgCzj77LPTwQcfnE9/8skn6ZhjjkkPPvjgAm934YUXpqWWWiqf/vWvf53WWmutRj3uBx98kH7wgx+kV1555TOvN2zYsNSlS5e0yiqrzLPF48dxAAA0hfZN8qgAAK3A66+/nu655568heuvvz4HQN/73vfqXK9UKqU2bdrU2bfsssumpZdeOp+Or926dZvv49x0003phBNOSN27d6+zv1evXmm77bZLHTt2THPmzEkzZ85M6667brrzzjtrr9OpU6e0ww47pD//+c/z3O+hhx6abwsA0BSEUgAAn9O5556bVlxxxTRo0KD08ccf56qp6dOnpx49euStHEhFWPSvf/0r9e3bN4dT7du3z+FVORDq3Llz6tChQ+39vvDCC2m11VarPT979uy03HLLpf/85z9p1qxZ+Xb1Q675icf6LHF8AABNwfQ9AIDPIabNXX755bXVTj//+c9z0HTNNdekTTbZJL300kt5e/nll9Nbb72VvvSlL6XVV1893X777fn6ESo1FCy99957eSpfVEdVBkvl60awtfLKK9eZhtezZ898+S9+8Yt57u+jjz7KlVwNTd+L/lcRpgEANAWVUgAAn0P0cypXQz333HO5L9RDDz2U3n333QavH8HSgAEDcmi11157zfd+4z5iyt03v/nN2n0ROLVr1y6ffvPNN/PXM888M2200UY5+Pr973+fTjvttHT00UfPc3+HHXZY3gAAmhuhFABAI9122215Kl1M17vooovSmmuumZ599tlcwfTXv/41/f3vf6/t/xSVSD/5yU/yduCBB6Z99tknV0PNz3333Zf23HPP2ibo4f33389BVaVtt9027bfffjkYGzNmTK7SqvTd73433XLLLfl+yv2mylVd9cVle+yxR7riiiu+4CsDALDwhFIAAI20884756l4//3vf2v3RSBV9rWvfa3OCnzlvk3RcDzCpVtvvXWepuXlqXbXXnttuu666+rsnzZtWg6fohoqptyVq6batm2bpkyZkjbffPPa6x500EHp5JNPTldeeWXeyg3Y4/T999+/SF8HAIAvQk8pAIBGiql466yzzkJfv9wPKhqU77TTTumGG25o8Ho33nhjvk6sqFfpjTfeSP3790+nn356evLJJ9P48ePzFlMIDz/88Hx61KhRuX9VNF2vL8KoCMoAAJoToRQAwCJWnr5X3qJxeVn0gvrDH/7Q4O2ij1T0nCpXQpWNHTs2h1KxQt/8VtOLKXqXXnpp2mKLLea5LIKsuKxXr151tpjy91n9rQAAFifT9wAAFrHK6XsxdW/WrFm1l0X/qflZdtll09e//vU6+6ZOnZoefvjhNHTo0M98zG7duuWpew159NFHG9x/yCGH5LALAKApCKUAAD6nTz/9NFco1VfuIVWeute5c+fcTDz2R3VSeTrf/HzyySdp+vTpuY/UVVddlZZffvm08cYb114+Y8aMvOJfNFtfY401GnXMUTUVvaii2Xo0ZR8xYkSjbg8AsKgIpQAAPqdoTB5bpdmzZ9epjCqLle1OOOGEHAhVhlKV0/HKpyPsiil8EU7Fin1bbrllvl1ZnN5xxx1T7969049+9KNGHfMZZ5yR7rjjjtSnT588XbB+/yoAgKK0KVX+Kw8AgMWiHF5F1dSCxJ9nUQ3VtWvXRX4cEZpF+LWgai0AgMVNKAUAAABA4ay+BwAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEDhhFIAAAAAFE4oBQAAAEAq2v8HNSMf+TiB0JMAAAAASUVORK5CYII="}, "metadata": {}, "output_type": "display_data"}], "execution_count": 102}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:03:01.216160Z", "start_time": "2025-06-21T02:03:01.063690Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 3. 绘制本班级不同分数段比例图（饼图）\n", "plt.figure(figsize=(10, 8))\n", "# 确保分数段是按照正确顺序排列的\n", "ordered_ranges = ['60分以下', '60-69分', '70-79分', '80-89分', '90分以上']\n", "ordered_counts = [score_range_counts.get(range_name, 0) for range_name in ordered_ranges]\n", "\n", "# 定义饼图颜色\n", "colors = ['#FF9999', '#FFCC99', '#FFFF99', '#99FF99', '#99CCFF']\n", "explode = (0.1, 0, 0, 0, 0)  # 突出显示60分以下的部分\n", "\n", "wedges, texts, autotexts = plt.pie(\n", "    ordered_counts, \n", "    labels=ordered_ranges, \n", "    autopct='%1.1f%%', \n", "    startangle=90, \n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=True\n", ")\n", "plt.title(f'班级 {my_class} 不同分数段学生比例')\n", "plt.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "plt.setp(autotexts, size=10, weight=\"bold\")\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级分数段比例饼图.png')\n", "plt.show()\n"], "id": "ca128d52618d3ddc", "outputs": [{"data": {"text/plain": ["<Figure size 1000x800 with 1 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAMWCAYAAADs4eXxAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAA1rxJREFUeJzs3Qd8nFeZ9uF71Lslufce2+m995ACgdAWSCEJJZRQAgECu0AoGwjf0ssCAUJZAiGkEZKQHtLs9MR23G1ZsiTLktUsyepl3u/3HM0oY1mSJUsz74z0v3YPVhmNjkYjRff7nPOcgOd5ngAAAAAAwJhLGvu7BAAAAAAAhtANAAAAAECUELoBAAAAAIgSQjcAAAAAAFFC6AYAAAAAIEoI3QAAAAAARAmhGwAAAACAKCF0AwAAAAAQJYRuAAAOkud5fk8BMcb3HAAwUoRuAPDRqlWr9J//+Z/Dum13d7cuuOACrVmzxr3e2trq3jYRQsAvf/lLfe9731MwGBzW7cvLywd936OPPqqf/exnQ358V1eXNm7c2Pd6UVGRrrjiCjU3N/e9rbGxUStWrNBf/vIX+WH37t1qampSW1ub2tvbDzjsdnV1derp6XEfb8+b4Xxc/xH++IFUVFT0vWzfq7/97W/atGnTkF+H3WfYT37yE23fvt3NzZ7fZteuXXrxxReH/TyvqqrShg0b3Mv289HQ0LDPsO/tQOzzVFdX973+2GOP6fOf//w+t3niiSd00kkn6eWXXx7wPiKfH6alpUUpKSm65ZZbNBqPP/64br/99mHf/p577lFJScmwHiv7mob7cwUAOEgeACCq9u7d63V3d7uXu7q63Othf/nLX7xp06Z5RUVF3qZNm7xt27Z5W7dudS9v3759n/t5+OGHvaysLG/nzp3u9cWLF1sKGXD8+c9/9sYLeyyys7Pd41RfX3/A2//ud7/zCgsLvbVr1w74/v/+7//2TjrppCHv42tf+5qXk5PjPfvss+719evXe5mZmd6VV17Zd5sf/ehHXiAQ8P7xj394sWbPo8G+9wca9hwzW7ZsOaiP/9vf/jbovI477rh9HiN7/fLLLx/ya3n3u9/tfec733EvZ2RkuOf5448/7p7f9nPzhz/8wUtPTx/ye29z+vKXv+yde+65Xmpqqnf66ae7tz/11FP7zf+OO+4Y8D7sY5YtW+ZVVla61+3z2u1///vf993mHe94h1dQUOAVFxcPeB+f/exnvcMOO8yrrq52r/f09Lj7+L//+z/vQKqqqtxt//Wvf+33vmuuueaAz9mwYDDoTZkyxbv66qu9Bx980Lvzzju9u+66y43bb7+97+sz999/v3tsAQDRRaUbAKJs+fLlrtoVCASUmpqqJUuW9L0vKSlJ+fn5+q//+i9ddNFFOuaYY1wl7cILL9TXvva1fe7HqrNWeZs9e7b27NmjlStX6vnnn1dBQYF73cYLL7zgbnvmmWeOeJ6PPPKIDj/8cKWlpWnq1Kn67ne/u19F8tprr9XkyZO1aNEi3XnnnftV+T760Y+6+aSnp+v000/Xli1bBv18P/3pT7VgwYIh51RTU6N3vOMdete73qWzzz5b73//+wetVIZ9+MMfdp/7/PPPH/Dz2/fAvh9DufHGG3XiiSfqP/7jP9zXddhhh+m///u/9eSTT6q+vt5VMH/4wx/qc5/7nJtbfzt27NBb3/pW5ebmuvtZt25d3/v+9Kc/uefCQONDH/qQhsPmX1ZW5iqVNp/w99+GVYuzs7O1bdu2fd5ut7Nqbvgxt++RsWq0VUUPNDZv3uxuP2nSpEFXELz22mu66qqr+t5mj9kdd9yhZ599dtCv5eabb9b//M//uAq3fW/s+ffjH/9Y11xzjZKTk/Xqq6/q3HPPdc+rwfz85z93z31bjWC3f/rpp/vel5mZ6arkNubPn6+MjIwB7+Ovf/2rWznwkY98pO95dPHFF+vuu+92r7/++ut68MEH9cc//lELFy4c8D6eeeYZzZs3z/38hH++I/8dSnheA83PHpfw9+tAnnvuOdXW1uqLX/yim7v9brHH0qrt//u//+ueA2H2PMnKyhrW/QIADt7Qf3UAAEbtlVdecYFq5syZbpms/eEfZm+3P6bDAfbSSy91ofw73/nOPvdhH2d/9P/97393QfTQQw/Vv//9bxfq7A9yC+7Ggov90R/5OYbDQuG73/1uF25uuOEG94f717/+dXc/H/zgB91tPv7xj+uBBx5w4d+Wo1oosc918sknu/d/4hOfcMHdPn7KlCnua7DAbGGtf+iwedpFhXA4GWz59Fve8hb3tVpQ7ezs1FlnneUuSNjy2cFCmAU1C3o2L7tI8fDDD4/osbAlyHaBwcLr2rVrXei2YYH/bW97m5uHhUJ7DD7zmc+44GvsYoR9Lzo6OtwcLTzZ98sCp71uy6wtsNpjYs+JSHafdt/HHnvssOc5d+7cAeduy/C/8IUv7HNxZ7DHycyYMcM9Ny3Ih98Wyb5Ou9ARXt5tX2N/tuTcni/nnXee+56F2ddkFx/sYoIF8v7fM1uObs8ZC4i2hNrux4KtPe/sc65fv94tf7aPt6/N2DzsfdOmTeu7HwvqdlEmHJjD7LZ5eXkDft2RbFm53YeFUnsswt9TeywLCwvd61/60pfchRy7KGav28/u9OnT++7D5vrGG2/sd7FqIDZ/e1wjg3R4XgMFdJtb/8fdAr49t+wCUf+tGPY8OuKII9wFAvt5tItP999/f9/ntrnb7xz7+RvOBQEAwChFuZIOAIhYDmxLpc0999zjlpzaUtfjjz++73Yf+MAH3NLmSA0NDd6KFSv6lox//etf9w499FB3n6tXr/Zmz57dd9svfOEL3qWXXjri+V122WXeJZdc4pamhp188sneO9/5Tvfy5s2b3VLqv//9733vv/HGG723ve1tfe9PTk72Xnnllb73P/LII+5rtjlGss9x9tlne3l5ed78+fMHnM/rr7/uzZs3z7viiiu8zs7Ovrc3NjZ655xzjrdw4UK3DHkoJSUl3p49e/Z7+/e+9z3vtNNOG/Tj3vOe9xzUsuvw1/6b3/zGLXEuLy/vu88zzjjD+/73vz/o5/z1r3/tzZ0712tvb/dGw5ZyR85lKDY/u60twbalyEN9bWeddZbX1tbmXral3/3dfPPNXkpKyoBL+m07xNSpU93ybfv+RVq3bp13/vnnu2XbtrTc7v/MM890S87tbb/61a8GnI8tPY903nnnuaXd9vNlzx273/DjavcXZs+3gbYD2HaEkX6/bctDpE9/+tMj+vjPfe5z+3y8bTsJfz/6s9va12js596ew/Ycs68ncrm4Peft7fb9jPydct111+1zf/ZYP/bYY+5zTZ48eb/PBwAYW1zeBAAf2LJwWx5uVabBlruG/eIXv3DVYluCa5Vl+9feZpVJq8hGLpW2+zz11FNHPJ+bbrpJv/3tb10FLMwqt+GmWVZVt2W6Vg0Ps2XVTz31lLvNnDlzXCXz+OOP3+fjTf/GW7/+9a9dZf2rX/3qfvOwZl82l1NOOUWXXHKJu619fLiJl1X7rEJn1VNb+mu3iVy6bJV4q5zasGq6rQBYvXq1a6xlj6ENWylgnyf8ulWg7TZWbQ5XFd/3vve52wxnhJdd28cZW4JuFXl7TCIfK6vYDsQ+ry2xtkrxcJcQD8S+f5FL/u17Y8vjbY4H8q1vfctVau2x6D/ssfvd73436Mfacu5vf/vb+sY3vqEjjzxyv/fbdoh//etf7n7suRnZoM62M1jDMttWYRVmW+5szQLtdavM2jJpq+iHl4fb42TV5nCjtEi33nqre97Y1opvfvOb7mN/8IMf6AMf+EDfbawy/eUvf9l9f//85z/3vd2+d/YzNdzvuT3W4e+3KS0tdY+RPXft+RU5jD2Pw6/bCg7bftB/+0iYbRuw55R9P2x1S2VlZd/7/vnPf+roo4/Wj370I/2///f/XAXbViqE2dfdf/uFrZKxn197LtiwBoC2pHygFQsAgOhgeTkA+MD+cLc/2i1QHih027JmC5zHHXecWzpqy2htGbixP6Cty7MFElsCbX+kW2fl6667zi2NHW5n9MWLF+/zut2XBfivfOUrfcuAbW965B/qtq/bvg57n10MOOqoo/a5D1vWnZOT45a5RoYTu08LSP3DoO0/tXlbx2l72W7Tf2lwmAUWWwJvy6gt4Nrnsj3xttTWHtNwV29bNnzOOee4+wxfnLDl3xZMwsvijT2+FtItIFrws/BrFyBsGfFAS3vD7HtnS/xtz3f4goU9HrYEOZI9VrbHeiC2FN4+/9VXX62DZUuyP/vZz7owZvvMzaxZs9z30Ja22/sHeyzNgfbW9+8yHmZfky2Xt+XMtpTcAvVAy5XtMfrDH/6g66+/3t3WQro9D2x/sc3blvHbRQl7n4Vs+z5aMLRgbM/xMAugFpwHujhhYdouHoRZl//TTjvNbYsIu++++9zSflvaHXmByJaQ2wUae27Y57Pv+WD7/u1rsaXt9j019rP3sY99zL3dnr8DPc72c2BbLobjU5/6lPvc9hy1CzLhrSYWnO0iiH1/P/3pT7vnaSR7/G677TYtW7Zsn8fAfubs4oN9/+zxsS0PkRfXAADRR+gGgCi78sorXUA0tufVQqIFE6uEWjXOgps1OrKKnzW8soBnx1DZ3lBrDHXIIYe4P7ytomsVsMhqZrh6ZhXet7/97e5l+2PdQpSF84Nl4c3Cq4UJYwE2vG88MkiE52ChO5J9HdYozcJBZEXQ7s+q1FZ9tH3akSw022Nl4cmq5OHHKXIPrgUcC9D2dgsdVsWzEQ7Q4ePU7PELX3AI7wUOswqhhVALpAOxsB+u4J5wwglDPk4WlO3rsKpk2GCPVbjq2Z/tI7bH5WCr3NYIzfY8W5XVLtCEQ7eFLwti9jyw5509v4ZqRmYfb9Xq/uxxtAs4/YXDpu0LtlULkSF2IHbRxgKvzdUq0sZWPNj38qWXXnLHvNl+cKty2/P5H//4h6sK2/PQLojYhSEL3XZM20DsZ8lCvz0/LLDb884antnKkP77uN/5znfusxIhXDm35ni2B3ootk/avga7D2MBNlzJttUg1mTPPv9gwdaCtH1NdtuB2HOz/8+u9Sawx9ees5E/T/1Xz9jH2YULeyyMfd/s8bT96Pb9t6r4QHvaAQDRxfJyAIgyC5Hhs5ytAZeFGDsv2YKZ/WsB05YWW/dn+6P58ssvd8ujLYSGWeiwoGpLWK0qF67+2fJSC2u2rNTYfVoAtCBzoBA0GPt4+2Pdqsnh6px9jv5/rIdDxUDLl63BmIWDcKXc/P73v3cVzV/96lcDfl4Lptbt2sKIXTiw1y1Y20WJ8LD3WUXRQo2FFws4tgJgIAfqUH4gFlasYh5uImbDvl9WObWv3aqodpGgv8Eeq4EeJwuhtiz/k5/85IjnZ8HNwq41urOLMvY868+Wb9tzyZ4ntiy7/wWI/vO250xkx3ML64N1K7evyZaAWwM0u5BiX5/NyZaJ24WN8GNmwxqcWQXbnk8WKsPd9e3zWdjfu3evewzseWsXBuw5bsOe13YbaxpmrNJrqwoGu1hiKwzsAow9FvZ57YKMPd8siNuwnyPr5j1YILbnra3ysNuG525B3n4ubW72s2md8fuzCwL2eW1rgT1v7bbhjvTGnifh1+1xtor/SFkFfbDAbeyCSbhZWngp+l133eXmvHXrVvezN9IGiwCAsUGlGwCizJZX2x/xxpZgW8Uv3FHbjleyZarh5b0WMi04223CrLpty8ktYNqyXAtZFvjsD2xbSm5/wFu18//+7/9cR3Hr7G0B62BCpwUnu38LOpEVP1tOu3Pnzn1uawE0POdINhcbto83XFm1JdfWodqWGA+1zNaWwfbvxnwg4crjWLPgbAE/zJaa2/fS3m4XUSzMDGSwx6r/4xQ+psr2OQ/UiXwoFgTtuWPPDVuebnuUB2PfS6sa2/Jve5xsifVALBDa1xZZpR+so3lkEAwvp7bb2ddp1ejILQXh739kZTnMnif2/LWPtaXddsHnoYceci/b3m57DtnLNn/rJ2Che6DQa+xiVuTycmMXI+xnxVZc2HPRLurYz1rkz1ek/ls97IKF9Q2wr8cC/IFWPtjFBAv2dnEoHLjtIoStZnjve9/rXreLOAfaUnKwwqtPwp/HHjP7mbZ52zzC1flwJ3oAQGwQugHAR1bBC/8xPhgLqbbE3CqvVtW15bVLly51VUtrlmVB3EKBhRc7E9oqyvfee+9BzcfCiS3ztcZikWHLLhbY/l3bIx0+qskqtOG9w5Fh0JYcW8C26meYVUOtOj/Q12rhJBycLejbx1pFzz6/7b21Pcn29dkqgTB7uwUHC2RDNYSy29i+b6u4RzacGkp4qXH/sGnnXNuyXbtwYEt4ww3UjFXd7XGw75U9VhYSI9ljFfk4hb8Gq0QeaDnzQCw42lFqt99+u3tuHIhVrC3gDxUarUptI7IabheLDnQueiSrrtr3o38wHmife/j5ZtVl+55bAzF7POxxsePOwg3QLrvsMheWbbuFLZm3ffcHYsvSbe+3XXCwsGk/D3aGvO1njjxHPPw12lFfFoT770e377F9/ba33J4P4e+5Pa9sj7RV3SOrz7YCI7zXO5KtFhju8y+SfZ7BtiUcSLhxmrFVA/Y424WMyCPLOC4MAGKD0A0AMWZLaa0SVlJS4gLuGWecMeTtLcg9/fTT7mX7o99eXrVqlasqWvCyZa02bHmtVfWsg3Rk4B0uW1Ju1XJb/tt/j7aFKFsGb3tev//977swYN2ebfly+KxiqwSHz5ruvwfYqoUW5CNZpf6WW25xlc3w5+u/r9mabNle1fA+ZWsmZVVmW2ZvIchCzlCssZWFTVveO9zQY/voi4qKBl0pYHOKfJ89Fha67WuxPd4WdCwk2tdlj4ddbLDHtX812vYxWyMxa0Q2UuEmYyMRDmCDsQsYto+9/77v4W5TsGXZVlm2z9O/mZiF7oGq+XbBxSrj9tjZfm7rV2CrBOwxtbBtbF+2daq3PgM///nP9/l4u539DNk2ALuAZefcv/DCC+5rCZ+lbf0S7IKNPWb2M2eNyiLZRQa7IGA/k/1DaHiFiv1c9f+8VkG2C1HRWrJtVXar3oeD8kDssbOLCgdij499T+zrsLBtF4bs67XHCQAQfYRuAIghO/LIwqbt37aqm+0VtSrUUCxkW9C1peQW2C0A28dZCIkMXlYRtsr3gZbADsQavdm+cmvEZY2xLHyFQ7BVma16aUu/LVTaEl8LKtbUKbKibsHWqnLWYT3c0Cwcmiyw9/867TZWJbQjkAZiFWB7jGyfbLhRmi3Rts9pYcE6NQ9WqbOO7ta12ZZeW1Af7HMMJHLu/UOlLTcfqNFVJHu8rKpqIduW91q4tvn0r2hbkzO7ENC/c/xo9D+ebSRsflZ57m+whl+RbP+3XVix54U1Lotkzwl7/2BL6C002t52ezzsY60Dv1Xy7bltzzf7Hoe/z/07qFvgDode+9cqzxb8I7/f9jX97Gc/c9smbN9z+CJR5EWtwar59r22iyJD7YUfK+GvzX6GrZ+CXTywlSH2GAx0ZJs9H+1xsq93qNUSdhHDfn9YL4LwsvZwY8fwxTwAQHQRugEgiixs2ZJZWz5twcHCh1XzrOpm4dT219ofz1Y1s2Brf/xboLQqtu3htgqwhVYLZ/aHt1W97D5sj7eFnPe85z3uY62hllW6rYGVBXsL59Y9fKhu1ZHsiC6rgFlItRFmQcbOFA6HagvOFr6NzT28R9SWXofPobYu6qPZc21fn1UjLdRbOLD9t/Y12mNpAc2q6PZ+CxC2lH4gtlzYLh5YYO+/vzjcICua7BxnW4Vge5ZtO4A9Vv1XD9jFgIM5U30o4cpluEI7EvacHGrlwECB3p4zdhHEnn+29N6q++FwbZ3J7blkXfdtZcBgDe+MLRu3i0r2XLPgbfvK7TGzZfF2AcOWi9vzzsKlfW1WAbYqtn0uu4hlH2OV8v57um1+1gndvt/2sm2PsAr4wSz1Hor9vNryeLtINdCFIKtIR25HMPazbo93+KKLhW1jjeVstYA9h20lSfjCh217sIsT4T4D9nvEVlj0v6AU+fy2Crfdl30eu6g20POF48MAIAY8AEDU1NTUeIWFhd7VV1/tbd682b2tvLzcW7x4sfeOd7zDvX7rrbd66enp3qRJk/pGTk6Oe9tArrzySu+cc87xGhoavB//+Mfe3LlzvWOPPdbbsGGDe/+rr77qzZw50ysoKPCee+45L9G8//3vt8Tgvn4b9nJaWpo3depUb8mSJe5rPe2007xAIOD913/914D3sWfPnr7Ho7+vfe1r7j6Gq6Kiwnv88ce9//3f/3VziefHdOPGjW6OTz311AFvu2XLlmHf9jOf+Yx38sknu9s/++yzfW9/5pln3Pfmoosu8rZv377Px7S2tnrTp09336v7779/wPstLS31vvSlL7nn8HHHHeetXLnSvb2np8f79re/7X4OLr74Yq++vt693Z7vSUlJ7mfHbhNmPw833nhj3+urV6/2vvrVr3oLFy50P08//OEPvXvuucebMWOGl5KS4p1yyine9773vUG/3vXr17vv+cc+9jEvOTn5gI/PX/7yFy81NdXNN/LneLCRl5fnntvve9/79nm83vOe93ivvPLKfvdvX8+8efPc4x8e9nV8+MMf3u+2n/70p71LL73U/b5Zvny5l5mZ6a1atWqf29j36txzz3W/h5YtW3bArw8AMDpUugEgimzpqlXvIrtgW2Xbqn7W0dhYJc/GcNn+YKtwWVXNKlq2f9iq0OHGX3bf1gXaKt1jXUmNBVtaa020bOmsLQW26vpAS5ytsdZgXaitUtr/rOwwW45rY7jscbU9xVaZtPOlbT9svLKq+nCr+LaiYLhspYFVam25fGTF2rp1WwXWGp/1Z98zqyoPxTqa23xtlUbk3mWrFtt92hnokc33rFmg9QwIH8sVuTQ7cum5HY9lK0yuvfZat6c7vOLDquG2YsTu1yrfg7EjtmwVif2MDedn0/oA2BgNe7zs5IGBWDXbtksMhz1P7feDrZCx54Ptl+//e8CavVl13prSWaNBAEB0BSx5R/lzAACQ0Czw2AWUgY79mghsSfdozz2PpfCfNkMtnR6qe7ctpbfl4nZRYKju+AAADAehGwAAAACAKOGARgAAAAAAooTQDQAAAABAlBC6AQAAAACIEkI3AAAAAABRQugGAAAAACBKCN0AAAAAAEQJoRsAAAAAgCghdAMAAAAAECWEbgAAAAAAooTQDQAAAABAlBC6AQAAAACIEkI3AAAAAABRQugGAAAAACBKCN0AAAAAAEQJoRsAAAAAgCghdAMAAAAAECWEbgAAAAAAooTQDQAAAABAlBC6AQAAAACIEkI3AAAAAABRQugGAAAAACBKCN0AAAAAAEQJoRsAAAAAgCghdAMAAAAAECWEbgAAAAAAooTQDQAAAABAlBC6AQAAAACIkpRo3TEAAONeMCh1d+87enokz3tzGPs3OVmaNs3vGQMAgBgjdAMAJm5gbm8fenR27h+qw6Or681QPRx5edKll0bzKwIAAHGI0A0AGH9hurl5/9HaKnV09IbptrbeQA0AABBlhG4AQGKxsLx375thOvJlGxaoR1KBBgAAiCJCNwAgPrW0SHv2SA0Nbw573UI1AABAgiB0AwD8XQre1LRvqA6/bHumAQAAEhyhGwAQG9bV20J1TY1UW9s76ut73w4AADBOEboBAGPPgrQFagvW4ZBtr1tlGwAAYAIhdAMARs8q2FVVBGwAAIB+CN0AgJGxMF1XJ1VW9gZtG3YMFwAAAPZD6AYAHHipeHX1myF7926anAEAAAwToRsAsH/ItoAdHrZknGZnAAAAB4XQDQDoPbarrEzauVPatUvq7vZ7RgAAAOMCoRsAJiIL1Rauy8t7h4VuAAAAjDlCNwBMpA7j4ZBte7NZMg4AABB1hG4AGM9dxi1cl5RIpaVSc7PfMwIAAJhwCN0AMN6Cti0bLy7uDdptbX7PCAAAYEIjdANAorNl4tYALVzR7ujwe0YAAAAIIXQDQKI2QrNu4xa07V/OzQYAAIhLhG4ASKSl4xawi4p6/+VYLwAAgLhH6AaAeFdTI23b1hu229v9ng0AAABGgNANAPGopaU3aNuwo74AAACQkAjdABAvbLm47dG2oF1RIXme3zMCAADAKBG6AcBvdsTX1q29gZuGaAAAAOMKoRsA/GDHelnQ3rhRamz0ezYAAACIEkI3AMRSba20YYO0fTvdxwEAACYAQjcARFtPT2/Itqp2dbXfswEAAEAMEboBIFqamnqD9pYtvcvJAQAAMOEQugFgLFnH8bKy3rBdXu73bDCOba+XniyRApICgd5/FfFycpKUkiSlhv51LydHvDzI29OTpYwUKTNVykzpfR8AADh4hG4AGAu2P9sao61bR2M0xIQX8e8+p8uFXu4Kjs3nSQ68GcDDYdz92+/l7LTefy30AwCANxG6AWA0bNm4NUaz0dbm92yAMdfjSc2dvWM4AT0nTcpNl3IH+NdCOgAAEw2hG4ixnp4eJSf3rtdsb29XZWWlFi5c6Pe0MFLNzdIbb/Tu1+ZsbaAvoDd29I6B2JL2/qE8L10qzOx9nSo5AGA8InQDMXbVVVfpqKOO0pe//GXdfffd+sEPfqC1a9ce8ON+8pOfqKGhQd/+9rf73tbS0qKPfvSj+sIXvqATTzzRva2+vl5NTU1KT09X4AB/wXZ0dCgzM1PTpk0bg69sgqirk+z7Zd3I91nTC+BAbMn7nvbe0Z/tJ8/P6A3gBRlSQWbvyxbSAQBIZEl+TwBIVF/5ylf0jne8o+/19evX64QTTlBBQYFuuOEGeQMEsuLiYhe0L774Yvf65Zdfrq6uLv3yl78c8HO8+OKLuuWWW9zLb3nLW1zw/utf/9r3/ra2Nv3973934Tvs17/+tZYtW6bly5f3jUWLFmnOnDn7vM3GoYceqq9//etj+riMWxUV0kMPSffcIxUVEbiBMdYdlGpbpa110ksV0iNF0u3rpD+tkf65WXq2VFq3W6poklpZXAIASCABb6BkAGBIb7zxhk477TRXobZAaxVjC7EXXnihC9zXXXed/uM//kMf/vCH9/m4d77znZo8ebL+8Ic/9L3tySefdG9/9NFH3X1GsgB9xx136JlnnnGvW2C3SvlLL72kI444QmVlZZo/f74qKio0a9asQed766236i9/+YuefvrpMX8sxr0dO6TVq6WaGr9ngkSXlyddeumY3V1RvfTvEk1Y1rRtarY0LWKk0WkdABCHWF4OjFAwGNTHP/5xXX/99S5wm4cffliNjY368Y9/rKysLN1888369Kc/vU/ovu2227Ry5UpXEY903nnnueXhF110ke69916df/75fe9LTU3dZ4m4BXkL5/fcc48L3Tt37nS3mTFjRky+9gmltFR67TWpttbvmQAYQFu3VNbYO8JseXpkCLfl6UnsEwcA+IzQDYyQLfdet26dC97333+/C8tW8T755JNd4DZHHnmkNto5zSFWmb722mvdx1qlu9uOl4rwjW98wzVYs0r5Rz7yEf30pz9VTk7OgJ//V7/6lQvapry83AX/pKTh7xTZtWvXkFXxCc/O1n71VSrb8I0tQCvbWanunt7fEwH7v4CN3kO4kwIB14yxqdN+3+T6Pd240tDeO2yJerib+pSsfYO4NW8DACCWCN3ACDQ3N+ub3/ymC7qlpaWuev2d73xHZ5xxxj4dyAOhP4r37Nnj/r3kkktc4zTbP33llVcOeN+2z/uuu+7S7bff7pqbDSYcuI1VzW1f9nDZxYFTTz1Vf/7zn/Xe97532B83YfZsW9jevdvvmWCC276jXLf9/T61trXv083bwrf7fxe8k5Qza5mmHN3bHwKDd1Pf3dI7wrJTpVm5vWN2Ho3aAADRR+gGRsCWf1vTsqeeekpTpkxxFWtb5m17tPvv387IyFBra6tmz57tGqJZKP/Sl76klJQUtxz8scce09FHH+1ue+ONN7qAbkF4sDBsy9p/85vfuIp52BNPPDGi8Gxd061Sftlll7n94tb5fMKrrOwN2/YvEAc6Ozu1t7lFC+fNsbq3XOOV0P94oUq4F/QUCK2swci0dEnb6nuHsSPLXAAPBXHOEgcAjDVCNzACtofalpFb4DYWoG0p+ebNm1XTbzny3r17lZbWW0IJV8Ft+bk1P7OK+WGHHeY+3thZ3UuXLh3089of2bac/aGHHnJVcwvyr776ql5++WXXXG0krr76andBwCrudr/XXHONJiSraFvYtgo3EIdSUobuChZIp0Q7Fpo6esfmUPsGO67MKuDhajjN2QAAo0XoBkbAjt2yY7oi2TJz24P9i1/8ou9tJSUlrqN5YWHhfvdhR36deeaZ+ywht33W55xzzoCf0/Z6WxU9HLItcNscPvGJT7j3//vf/x50yfpgPvCBD7iLApFL1SfUOdsvv9y7dxsA+gmfI76+2q3md3vCLYTPzZOm59CYDQAwcpzTDYyA7bu2BmnWEM2q3j//+c/dPun3vOc9ampq0h//+Ed3O+tebudq237uSKtXr3bvszO+I9l9WaDvr729va/j+bPPPutuY1Vxa96Wm5vr9pR/7GMfc2d1j5RVuK3qPWG0tkp29Nq99xK4AQyLLeevaZXWVEkPbJVuWys9VSIV75E6e/yeHQAgUVDpBkbAOo/bEm/bm23HfM2cOVN33nmn5s6d687Ctr3Sdk63dROPPBPblnHbMnALujfddFPfsWAWprdu3eqGnfPdn1W3jzvuOLd3Ozs72521bZ/bmqHZy7Zc3Zao29ndNjcL+oPtB7c5TEjWKX7t2t7Rr2s8AIxER8+b+8Gt4m3Lz+dPkubn05ANADA4QjcwQqeddppeeOGF/d5ue623b9+u1157ze37thAcZh3Obfm5NUK79NJL+95uXcTvu+8+d763LRvvz95urGHbkiVLVFdXp+9+97u67rrr+s7vfv/7368NGza4avdgoduWulvVfEKxiwxbt/bu226JaF0MAGMg6Ek7m3rHqnJpcmZv+LYQbkvSIzvPAwAmtoA3YctfQOxYaLY91AUFBQd9H1ZhP/744zVt2rT93mc/xuEQjtDxXy++2Lt/G4gXeXlSxEW3wWzcUqRbb7tLSxfNH/J2gfz5Spp/2hhOEGMlK1WaN0lamN+7H5x94AAwsVHpBmLAloCPJnCbt73tbYO+j8Ad0tDQG7bLyvyeCYAJrLWrtxu6jYwUaVGBtKRQmp5NBRwAJiJCN4DEZ0vnbRn5pk29y8oBIE60d0sba3qH7fteHArgkzlmHQAmDEI3gMRlAXvz5t4jwDo6/J4NAAypuVNau7t32Hngiwt7A3heut8zAwBEE6EbQGKqrZVWrpSqq/2eCQCMmJ0F/uqu3jEtuzd82zJ02w8OABhfCN0AEktnZ+9S8g0bWEoOYFyobukdL5T3Nl5bNllakC8lJ/k9MwDAWCB0A0gc27dLdlxba6vfMwGAMWeXEcPHkFkDtkMmSyumSJMy/J4ZAGA0CN0A4l9jo7RqlbRzp98zAYCYNWB7Y3fvmJkjrZjaewQZ1W8ASDyEbgDxq6dHWr1aWru292UAmIAqm3uHVb+XFvYG8Hyq3wCQMAjdAOKTVbWtUVpTk98zAYC4qX6vq+4dM6z6PUVaWCClUP0GgLhG6AYQf43Snn9e2rrV75kAQNyqau4dz5dLSydLh0/j6DEAiFeEbgDxo7xcevZZqaXF75kAQELo6JHWV0sbqqX5+dKR03ur4ACA+EHoBhAf1W3rSr5li98zAYCE7Xy+o6F32LnfR0zrXXqeFPB7ZgAAQjcAf1HdBoAxZWd+P1ki5VZIh02Tlk+R0pL9nhUATFyEbgD+oLoNAFG1t1N6caf02q7ejue27zsnze9ZAcDEQ+gGEHtUtwEgZrqCved9r9stLSro3fc9NdvvWQHAxEHoBhA7VLcBwNd939v39I6ZOdIxM6U5eX7PCgDGP0I3gNiorJSeekpqbvZ7JgAw4VU2S5XbejudH0v4BoCoInQDiK5gUHrtNWnNGsmzOgsAIF7YWd8PbZOmZ0vHzSJ8A0A0ELoBRE9Tk/Tvf0vV1X7PBAAwhN0thG8AiBZCN4Do2LpVWrVK6uryeyYAgIMI37bsfO4kv2cEAImP0A1gbFnIfu45qajI75kAAEYRvh8ukqZZ5ZvwDQCjQugGMHZqa6Unn5QaG/2eCQBgDFRHhO/jWXYOAAeF0A1gbKxbJ730Um/jNADAuAvftux8bp500hypMNPvGQFA4iB0Axid9nbpmWek0lK/ZwIAiLLyJmnnRumQyb2V7+w0v2cEAPGP0A3g4NXUSI8/ztnbADCB2OGPW+qk7XukI6dLR02XUpP9nhUAxC9CN4CDs2WLtHKl1NPj90wAAD7oDkqvV0qbanqPGVs+RUoK+D0rAIg/hG4AI2Mh+/nnpU2b/J4JACAOtHVLK8uk9dXSSbOl+fl+zwgA4guhG8DwtbT0LievrvZ7JgCAONPQLj26XZqZI508R5qa7feMACA+ELoBDE9lpbwnnlCgrc3vmQAA4lhls/SPzdKSwt7wnZXq94wAwF+EbgAHtm6dvBdfVMCz9jkAABxYUb1U2tDb5fywaez3BjBxEboBDK67W3r2WamoSPytBAAYqa6g9MJOaWuddPo8aXqO3zMCgNgjdAMYWFOTvMceU6C+3u+ZAAASXF2b9M8t0rLJ0klzpAz+AgUwgfArD8D+KirkPf64Ap2dfs8EADCO2PneOxqkE2f3HjEWYBkVgAmA0A1gX5s3y3vuOfZvAwCioqNHeq6sN4DbkvMpWX7PCACiKynK9w8gUXievJdecnu4CdwAgGirbpH+sUlaVSZ19vg9GwCIHirdAFzDNO/f/1Zgxw6/ZwIAmEDsEu+GGql4j3TK3N5jxgBgvKHSDUx0ra3q+ec/CdwAAN+0dUv/LpEeKZJau/yeDQCMLUI3MJHV16vnnnuUXFfn90wAAFBZo3TXht4jxgBgvCB0AxOUV16unn/8Q8ltbX5PBQCAfRqtPb2DqjeA8YPQDUxAPevWyXv4YSX30LkGABDfVe9tVL0BJDgaqQETieep45lnlL51q98zAQBgWFXvp3b0Nlo7Y76Uler3jABg5Kh0AxNFMKi2Bx8kcAMAEk4pVW8ACYzQDUwE3d1queceZVZW+j0TAABGVfV+lL3eABIMoRsY57z2djXfcYey9+zxeyoAAIxZ1buo3u+ZAMDwELqBcay7sVEtf/ubclpb/Z4KAABjWvW2c72fKpG66AkKIM7RSA0Yp9qqqhR84AHleJ7fUwEAICq21UvVLdJ5i6QpWX7PBgAGRqUbGIcai4ul++9XNoEbADDONXZI922W1u32eyYAMDBCNzDO1KxZo4zHH1em3xMBACBGgp70wk7pkSKpvdvv2QDAvgjdwDiy87nnlP/SS0oPBPyeCgAAMVfWKN29Udq11++ZAMCbCN3AOOB5noofekgzN25UKoEbADCB2XFi/9oqvVzRWwEHAL8RuoEE19PTo6333qsF5eVKJnADACDL2muqpAe2SHs7/J4NgImO0A0ksK6uLm26+24tra1VEoEbAIB97G6R7tkkFe/xeyYAJjJCN5CgOtrbtfHvf9ehDQ0EbgAABtHZIz1RLK0qY7k5AH8QuoEE1NrSovV33KEjWloI3AAADMOGmt7l5rbnGwBiidANJJjmpiatu/12HdPRQeAGAGCky803SlXNfs8EwERC6AYSyJ66Or1x++06IRgkcAMAcBDauqUHt0rrq/2eCYCJgtANJIiaqiqt/9vfdHIgQOAGAGAUbG/38+XSv0uk7qDfswEw3hG6gQSwq6xMG+64Q6elphK4AQAYI0X10v1bpOZOv2cCYDwjdANxrqyoSJvvvltnZmYSuAEAGGO1rdI/NrHPG0D0ELqBOLZj2zZt/Mc/dHZ2NoEbAIAo7/PeWOP3TACMR4RuII4D9xv/+IfekpdH4AYAIAb7vFeWSc+WSj3s8wYwhgjdQJwG7lfvvVcX5eUphcANAEDMbK6VHtomdXT7PRMA4wWhG4gzpdu26YV77tHb8vKUlsSPKAAAsVbZLP1zi7S3w++ZABgP+IseiLPA/ezdd+vteXnKSk72ezoAAExYDe3SfZulmha/ZwIg0RG6gTgK3E/ddZcuzs1VbkqK39MBAGDCswZrD2yVdjT4PRMAiYzQDcRJ4H78rrv01pwcFaal+T0dAAAQ0h2UHt8ura/2eyYAEhWhG4iDwP3onXfqguxsTc/I8Hs6AACgH0/S8+XSC+WSZ68AwAgQugGfA/fDd96pczIzNS8z0+/pAACAIayrlh4v7q1+A8BwEboBn+wsKdHDd92l09PTtTQnx+/pAACAYbD93Q9uldq6/J4JgERB6AZ8sLuiQo/cdZeODAR0eF6e39MBAAAjUN3Se6SYdTgHgAMhdAMxVldd7ZaUz2hr06mTJ/s9HQAAcBCaOqR/bu4N4AAwFEI3EENNe/a4JeUptbV664wZfk8HAACMQkeP9K+t0q69fs8EQDwjdAMx0rJ3rx65+27tLS3V++bNU3Ig4PeUAADAKHUFpYe3SWWNfs8EQLwidAMx0NHWpkfvvVcVW7bo8kWLlE7gBgBg3OjxpMe2S9vr/Z4JgHhE6AairKuzU0/cf7+2rlmjyxYv1qQkfuwAABhvgp707xJpc63fMwEQb/jrH4iinu5uPf2vf2ntiy/qvYsXa0Zyst9TAgAAUeJJerZUWrfb75kAiCeEbiBKgsGgVj3+uF5buVLnL1igpWlpfk8JAADEwAs7pdcr/Z4FgHhB6AaiwPM8vfLss3rxqad03KxZOj4jw+8pAQCAGHp1l/TiTr9nASAeELqBKNjw+uta+eijWlxYqHNzchSgcRoAABPOG7ul50rtYrzfMwHgJ0I3MMZKt23TUw88oLz0dL2toICjwQAAmMA21UpP7ehttAZgYiJ0A2OopqpKj//jH+psbdW7Zs5UJoEbAIAJr6heerKY4A1MVIRuYIw0NzXpsXvuccH7HQsXaiqBGwAAhJQ0SE/vYKk5MBERuoEx0NnRoSfuu0+lRUU6c/FiHcLRYAAAYICK9zPs8QYmHEI3MEo9PT165qGHtHH1ah0+f75OSknxe0oAACBOba2TVpb5PQsAsUToBkZ5NNirzz2n11et0uyZM3VuZqZSWFYOAAAO0Fzt+XK/ZwEgVgjdwChsXrtWqx57TDmTJumCSZOUQ+AGAADDsL5aeolzvIEJgdANHKTy4mL9+4EHpEBAZ0+dqpkEbgAAMAJrd0uv7vJ7FgCijdANHIS66mo9du+9amlq0vFz5uiwJH6UAADAyL1e2TsAjF8kBWCE2lpb3Vnc1RUVWj5/vk4hcAMAgFGwavcbu/2eBYBoIS0AIxAMBvXsI4+oePNmzV2wQGelpiqVZeUAAGCUXtzZu88bwPhD6AZGYM0LL7gxdeZMnZyerkkEbgAAMEaso/mWWr9nAWCsEbqBYSrdtk0rH3tMWdnZWp6bqyUsKwcAAGPs2VKprNHvWQAYS6QGYBga6ur0xD//qfbWVs2dNk0nEbgBAEAUeJKeKJaqW/yeCYCxQnIADqCzo0NP3n+/dldUaNb8+TotOVlpLCsHAABR0h2UHimSGtv9ngmAsUDoBobgeZ6ef/xxbXnjDc2cN09Hp6RoKoEbAABEWXu39HCR1Nbl90wAjBahGxjChtde06vPPafJ06ZpbkaGDiVwAwCAGGnq6K14d/X4PRMAo0HoBgaxq7RUzzz8sFLS0jQ5P1+nJiUpidANAABiqKa1d4930DZ7A0hIhG5gAHsbG/XEffe5f+14MGuclkXgBgAAPihv6u1qDiAxEbqBfnq6u/X0gw9q544dmj1/vg5JStJcAjcAAPDR1jrp5Qq/ZwHgYBC6gX7WvPSSNrz+uqbPmaPClBQdQ+AGAABxYE2VtLHG71kAGClCNxChYscO1608OzdXWVlZbh93CqEbAADEiVVlUskev2cBYCQI3UBIa0uLnnrwQbU0N6tw2jQdHgiogMANAADiiPVTe2qHVNvq90wADBehG5AUDAa16rHHVFZUpFnz5qkwENBhBG4AABCHuoPSY9s5wxtIFIRuQNLmNWu05sUXNWXmTKWlpupkjgcDAABxrLmzN3j3BP2eCYADIXRjwqvdvVvPPfKIklNSlJOXx7JyAACQEHa3SCvL/J4FgAMhdGNC6+zocPu462tqNG3WLBVIOpTADQAAEsSWOml9td+zADAUQjcmLM/z9PLTT2vb+vWaOX++UpKSdArLygEAQIJ5oVza2eT3LAAMhtCNCatkyxa98uyzyp8yRWnp6W5ZeT6BGwAAJGBH8yeLpcZ2v2cCYCCEbkxITQ0Nevqhh9Td3a38wkIVsqwcAAAksI6e3sZqnT1+zwRAf4RuTMjjwVY++qiqyso0Y+5c90NAt3IAAJDo9rRL/y6xLXR+zwRAJEI3JpzNa9dqw2uvadrs2UpOTtYRLCsHAADjRFmj9Mouv2cBIBKhGxNKY329Vj32mDseLCsnR5MkrSBwAwCAcWRNlVRU7/csAIQRujGxlpU//riqKytdlducyLJyAAAwDj1bKu1p83sWAAyhGxPG5jVr3LLy6bNnKykpSYsCAU0lcAMAgHGoOyg9Udz7LwB/EboxITTU1bkqd0poWXmapKMJ3AAAYJw3VltZ5vcsABC6MTGWlT/2mGqrqvqWlVvgziB0AwCAcW5rnbSl1u9ZABMboRvj3qY1a7Tx9dc1bdYst6x8iqTFBG4AADBBrCpnfzfgJ0I3xv2y8lW2rDwtzS0rt6h9QlKSAoRuAAAwwfZ3d/X4PRNgYiJ0Y9zq6el5c1n5rFnubcsCARUQuAEAwATD/m7AP4RuTJhl5ZmSjiBwAwCACWpbPfu7AT8QujEuNTU06IUnnuhbVm6OS0pSKqEbAABMYFbtrmd/NxBThG6MO57n6aWnnlJ1ZaWmzZzp3maLy+cRuAEAwATX47G/G4g1QjfGnbKiIq1/9VVNnj5dScnJ7kluVW4AAABIDezvBmKKJIJxpauzU88/+aQ6OzqUl5/v3nZIIKBcqtyYAFo7O1XX3Oz3NAAACbK/e1ud37MAJgZCN8aVN15+WTu2btX0OXPc62mSDidwI8b+uWaNFn3ta0q59lodfdNN2lRZ6d6+vqJCJ9x8swquv1433H232woxHHe/9prm/9d/adaXv6y/vfxy39u/869/acoXvqDHN250r9v7mjs6ovRVAQDG4/ndzZ1+zwIY/wjdGDfqa2r00tNPKzs3V2np6e5t1q08jdCNGNpeU6MP/9//6f+9+92q+J//0SHTp+ua225TR1eX3vHLX+q4+fP16le/qo2VlfrT888f8P4sqF/xhz/oxosv1qOf+5y+8cAD2lJV5d5366pV+t2VV+q3zz3nXi+rr9f8yZOj/jUCAMaHzh7pmR3WD8fvmQDjG6Eb40IwGNQLTz6phro6FU6b5t6WK2kJgRsxZlVtC9zvP/54Tc/L07VnnaXVZWV6eMMGNba16cfve58WT52qm9/1Lv1+1aoD3t+tK1fqnEMO0TWnn64jZs/WZ84+W7e9+GLf+4+aM0f1LS16fvt2nbJoUZS/OgDAeFOxV9pQ4/csgPGN0I1xYfumTdq0erWmzpzpzuQ2RyclKZnQjRh7+5FH6uNnntn3ulWll06frrXl5Tp50SJlpdmmB+nIOXNctftA1u7cqXOXL+97/cQFC/RaWW/3G1uevrupSXmZmXp0wwZdeNhhUfmaAADj20s7e5urAYgOQjcSXltrqzuT21ZG2dJyY7XuuQRu+Kyzu1s/euIJffLMM9XU3q6FEUu/A4GAkpOStKelZcj7cB83ZUrf6xawdzU2upcvPuIInfWjH+ncZcs0JSfH3ScAAAdzjNhTJVKQZeZAVBC6kfBWP/+8du7YoemzZ/e97ViOCEMc+OYDDyg7Lc0tDU9JSlJ6Sso+789ITXUdx4fS/+MiP+ZXl1+umh/9SEHPc8vMD/3Wt/TFu+6K0lcDABjPalqlNb0tQwCMMZIJEtruigq9tnKl8goKlJKa6t62MBBQIRU/+Ozfmzfrl08/rds/+lGlJierMDtbNf2O89rb3q60fkG8P/dxe/fu+zHJyX2v56ana09rqx544w199LTTdN+aNWpuZ40gAGDkXq+Ualv9ngUw/hC6kdjN0/79b+1talJBaPmtRZGjCNzwWUltrS679Vb98rLLdOisWe5tJyxYoBeKi/e5TUd3twvVQ+n/cdaUbXboDHpjDdouOuww1be26pi5c937LIQDADBSwdAy8+6g3zMBxhdCNxK6edq29es1fdasvr2sKwIBZRG64aO2zk69/X//V+886ii9++ijXdXZxhlLl7r92X8MdSy/+eGH9Zbly92+btPQ2qqe4P5/5bz3mGN0x6uval1Fhbufnz/11D4N014sLnYN2vIzM7WtulpVTU3Kz8qK4VcMABhP9rRLr1T4PQtgfBl6XSMQp7o6O/Xy00+77s2ZoUphRih0A356bONG15Xcxu9Wrux7e8l3v6tbr7zSVcBvuOce12X/6S98oe/9Bddfr9Vf/7qOnjt3n/s7au5cfe7cc3X8zTcrIyVFS6dN06fOOsu9r7a5WUtCR+RdcdJJLuy/7fDDlZthPw0AABycddXS/HxpVm9/WgCjFPAstQAJZu1LL+nhO+/UjLlzlZae7t52XCCgZTRQQ5yramx0R36dvHChJufkDPvjNu7apYqGBp11yCEH3AeOOJWXJ1166QFvtnFLkW697S4tXTR/yNsF8ucraf5pYzhBAHhTTpr0H4dKaW+2EQFwkPjLDQmnualJrzzzjNIzM/sCd6akJVS5kQBmTJrkjvoaKdsbHt4fDgBAtDV3Sq/ukk7ddwEWgINAWRAJZ82LL6q6slJTZszoe9vhduYxoRsAAGDMbKiWqlv8ngWQ+AjdSCh1u3drzQsvuCPCkkPHJtmO7kUEbgAAgDFle1CfK+3tag7g4BG6kTCs/cArzz2npoaGviPCDFVuAACA6Khrk9bt9nsWQGIjdCNhlBcXa+Pq1W5ZefiIMGtDtZDADQAAEDWvVUp7O/yeBZC4CN1ICD3d3Xrp6afV2d6uHOsAHHJEIKAkQjcAAEDUdAellWV+zwJIXIRuJIRtGzaoZPNmTZs9u+9tFr3nE7gBAACirrxJ2l7v9yyAxEToRtzraG93Ve6k5GRlZNrhYG/u5abKDQAAEBvPl0sd3X7PAkg8hG7EvU1r1mhXaammRZxRPIkqNwAAQEy1dUsvVfg9CyDxELoR19pbW/XaypVKz8xUSmpq39uPSErqa6YGAACA2NhcK1U1+z0LILEQuhH3Ve7qXbtcx/KwfElzfZ0VAADAxMXZ3cDIELoRt1pbWvTaqlXKyMpSSkpK39sPDQSocgMAAPhkT7u0tsrvWQCJg9CNuLVp9ereKvf06X1vy5I0j8ANAADgq9VVUkun37MAEgOhG3GptblZr69apaycHCVHVLlX0LEcAAAgLs7ufpmmasCwELoRl9a/9ppqq6o0OaLKnSZpEYEbAAAgLmyrl6pb/J4FEP8I3Yg7zU1NWvPCC8rKzVVycnLf2w8JBJRK6AYAAIgbL5T7PQMg/hG6EZ9V7t27VThtWt/bkkOhGwAAAPFjd4tUVO/3LID4RuhGXNnb2Oiq3Dl5eftUuW1ZeQahGwAAIO68tLN3jzeAgRG6EVfWvfqq6mtqVDh1at/bLGovJ3ADAADEpZYujhADhkLoRtxoamjQmhdfVO6kSUqKqHLPDQSUS+gGAACIW2t3c4QYMBhCN+LGpjVr1FBbq4KIKnf4mDAAAADEL44QAwZH6EZcaGtt1RsvvaTs3FwlJb35tLQDwyYTugEAAOIeR4gBAyN0Iy5sXbeut2N5/yp3RAAHAABAfOMIMWB/JBr4rquz0+3lTsvIUHJKSt/bcyTN9HVmAAAAGAmOEAP2R+iG77Zv2qSq8nJNmW6Lyd+0NBBQgKXlAAAACeWVCino+T0LIH4QuuGrnp4eV+W2buWpaWn7PDHtbG4AAAAklr2d0uZav2cBxA9CN3xVum2byouLNblflXt+IKB0QjcAAEBCWl0p9QT9ngUQHwjd8I3neXrjlVdctTsjM3Of9x1C4AYAAEhYLV3Sxhq/ZwHEB0I3fLOrtFTFmzdr8rRp+7y9kGPCAAAAEt6aKqmrx+9ZAP4jdMM36199VR2trcrKsT7l+zZQAwAAQGJr65Y2UO0GCN3wh53JvWXdOuVPnbpPh/K00H5uAAAAJL61VVIn1W5McIRu+GLDa6+pubFRefn5+7zdOpanELoBAADGhY4e6Y3dfs8C8BehGzHX2tysjatXK7egYL9zuFlaDgAAML6s2y21d/s9C8A/hG7EXNHGjWqoq1P+5Mn7vH2mpFxCNwAAwLjSFextqgZMVIRuxJQdD2YN1FLT0pScnLzP+5Yk8XQEAAAYj+z4sNYuv2cB+IOUg5jaWVysXWVlKux3TFi6pNm+zQoAAADR1B2UVlf6PQvAH4RuxNTmtWvV1dWljMzMfd6+IBBQEkvLAQAAxq1NtVJLp9+zAGKP0I2YsX3c2zZsUH5h4X7vW0jgBgAAGNeCnrSu2u9ZALFH6EbMWOBuamhQbr9jwuy1QkI3AADAuLepRuqgkzkmGEI3YqKrs1PrXn1VmdnZSurXMI0qNwAAwMTpZL6hxu9ZALFF6EZM7Ni6VTWVlSqcOnWftwdC+7kBAAAwMWyo7m2sBkwUhG5Ened52rB6tb3gjgqLNENSJqEbAABgwmjrlrbW+T0LIHYI3Yg6q3BbpbugX5XbLCJwAwAATDhrq3obqwETAaEbUbd13Tq1NjcrOzd3n7en2tnchG4AAIAJZ2+nVLLH71kAsUHoRlR1tLdr05o1ysnLU6BfwJ4XCCiF0A0AADAhranyewZAbBC6EVVl27ervqZGkyZP3u99dC0HAACYuOrapJ1Nfs8CiD5CN6Jq2/r1rpFaaqotJn9TtqRphG4AAABN9L3dwHhH6EbUNDU0qHjzZk0qLNzvfVS5AQAAULFXqmnxexZAdBG6ETUlW7Zob2OjcvPz93vfXEI3AAAArNq92+8ZANFF6EZU2JLyzWvXKiU1VUlJ+z7NrId5AaEbAAAAVqjZI+3t8HsWQPQQuhEVuysqtKu0VPlTpuz3PutaDgAAABg7rntjjd+zAKKH0I2oLS1va21VVra1TNsXoRsAAACRttRJPUG/ZwFEB6EbY66rq8udzZ2Vk7Pf2dwsLQcAAEB/7d3S9j1+zwKIDkI3xtzOkhLV7t6t/AHO5k6XtMezRUQAAADAm1hijvEqxe8JYPwp2rBBPT09Sku3iL2vWkkPB4PKCXUwtzGFyjcAAMCEV90i1bZKU7L8ngkwtgjdGFOtzc3atnGj8gY4JixSs6RNnudGVmQAt+UXhHAAAIAJaUO1dNYCv2cBjC2Wl2NM7di2TU319corKBj2x7Ra8wzP0xPBoO4LBvVyMKgqz1OQZegAAAATiu3r7uj2exbA2KLSjTFVvHmza56WnJx8UB/fbsvTPc8NW5w+OxBw3c6nS0qmAg4AADCudQelrXXSEfbHHzBOELoxZpqbmlylO3cEVe6hdFiI9zw3UkMB3Jagz7QnLgEcAABg3DZUO3yaxJ97GC8I3RgzZdu3q7mhQbMXLRrz++6ypeue54Y9aWdZAA/9m8pvZAAAgHGjsUOq2CvNyfN7JsDYIHRjbJeWJyUd9NLy4bJtPmWepzJrSuB5rvJtS9CtEp5GAAcAABgX1W5CN8YLQjfGbGl56RguLR+uoKQKG57nArht/7El6HMCAWUQwAEAABJSaYPU3CnlpPk9E2D06F6OMVG1c6eaGhrU2d6uzg7bjR17FsArJb3sefpHMKgne3q0NRhUG13QAQAAEor99ba51u9ZAGOD0I0B9fT09L3c3t6ukpKSIW8/e8ECnXLeecrJzVVNZaWreldXVqqj3fqR+/OLerekV0MB/PGeHm0OBtVCAAcAAEgI2+ok/nTDeMDycgzoqquu0lFHHaUvf/nLuvvuu/WDH/xAa9euHfT2mVlZOvvii3Xa+edrV2mpyoqLtXXdOtXt3u0q35nZ2ZpUWKj0jAx3pFis1djwPL3ueZocWoJuI5cl6AAAAHFpb6dU1SzNzPV7JsDoUOmOI1/5ylf0jne8o+/19evX64QTTlBBQYFuuOEGeSO81PeBD3xAn/3sZ/tet4///ve/r6VLl2rKlCn69Kc/rZaWlv0+rri42AXtiy++2L1++eWXq6urS7/85S8P+DlT09I0f+lSnXHhhbr6c5/TBz7+cRfGCyZP1p6aGlcBt6XobS0tI/56xkqdpDWepweCQT3U06N1waAauYwKAAAQd7bV+z0DYPSodMeJN954Q7/61a/6qskdHR0ugF944YW64447dN111+lPf/qTPvzhDw/r/h566CE9/fTT2rJlS9/bfv/73+tnP/uZ/vGPf2jSpEn64Ac/qE9+8pO67bbb9vnY66+/XldccYUOO+ww93pSUpJ+8Ytf6J3vfKeOPvponXbaacOaQ0pqquYuWuTGSWefrd0VFX0V8NqqKvd6emam8goKlJWd7UsFvMGG52md5ykvogJeSAUcAADAd8V7pFPnSimUCpHACN1xIBgM6uMf/7gLu4tCZ1w//PDDamxs1I9//GNlZWXp5ptvdpXp4YRuq15/6lOf0ve+9z3l5+f3vf3Pf/6zvvCFL+jEE090r3/729/WpZdeus/HWgBfuXKlq7JHOu+889zHXnTRRbr33nt1/vnnj+hrTE5J0az589044cwz3b7v8u3btXXDBlXv2qWaXbuUlp7uAnh2bq4vAbxJ0gbPcyMnIoDbcnQ/5gMAADDRdfb0djJfXOj3TICDR+iOA7fccovWrVvngvf999/vgq1VvE8++WQXuM2RRx6pjRs3Duv+LEx3dnYqJSVFjz/+uAvMVq2ura3VvHnz+m5n52lHnqn90ksv6dprr3XzmTx5srq77UTsN33jG99wDdas+v6Rj3xEP/3pT5WTY/F0ZOxzzpgzx43jzjjDVb3LS0q0bf16t/TcXrdl6uEAbnOPtWZJmzzPjayIAD7FKv8EcAAAgJguMSd0I5GxUMNnzc3N+uY3v+kq3KWlpfrJT36i008/XU1NTVq4cGHf7azSamF1z549OvbYY10Fu/+wwG73YUvI7WNtb7btE3/Xu97lqun2cf/85z/77tOWq4cr1vb5LrnkEtc47etf/7rS09OVmpq637CLAXfddZebR2Zm5qi/fgvU02bN0nGnneb2f3/wM5/RW9//fi045BC1t7a6anjFjh3uODL7GvzQKmmL5+mJYFD3BYN6ORhUlecpyD5wAACAqCtvlNq6/J4FcPACnl/drNC35Nv2VZeVlbnmZlZdPuKII1RVVeWWktvy8rC5c+fqxRdfdC9bY7P+pk2bph/+8Ie69dZbtXXrVmVkZGjv3r2aP3++2xe+fPlyvfWtb3UB3d5u1fVnn31WZ5xxhvt4OxbMwnpra6urks+YMUOPPfaY28dtbrzxRhe2rRIebfa03FNbq/LiYm3ftEk7d+xQS1OTu/CQm5+vnEmT9qnS+yFN0pxQBXyGVfCpgAMYSl6e1G9Lz0A2binSrbfdpaWL5g95u0D+fCXNH16PDQBIdKfMkY6Y7vcsgIPD8nKf7dy50y0jt8BtLOzaUvLNmzerpsYOunqTBeW0tDRNnTp1yPt7y1ve4gK3yc3Ndd3Ki4qKdMEFF7i92nbfVtG2UB0O3CZcWbcl7XYRwKrw1kzN5mQqKyvdfcWCVfYLp05146iTTlJjfb0L4MWbN6vMqt8lJe42FsBt+BHAO625h+e5kWpnlYcC+Ez7PhLAAQAAxnSJOaEbiYrQ7bM5c+aora1tn7fZEnHbL20dw8OsCm0dzQsLCw94f5s2bep73ZZkWxCfPXu2e92Cal5enp544gk9//zzg97PX//6V5155pn7LCHftWuXzjnnHPnBzvi2cfjxx2tvY6Nbdl6yZYt2bNvmArgJB/DwRYJYsnUHOzzPDYv/syTNCwQ0KxBQKgEcAABgVGpbpfo2qXD0uxuBmGNPt8/sLGxrkGZLti0c//znP3f7pt/znve4fdZ//OMf3e2se7lVsA9U0X3f+97n9nbfc8897v7+67/+yy1Ft48N+853vuNud8wxxwx4H6tXr3afz/aDR7L7s1Dvt9xJk3Toscfq4ssu04euv17vuvpqHXXyyQokJWnXjh0qKypSfXX1gEvwY6HH9h5JWuV5uicY1DM9PSoJBtXJTg4AAICDtq3O7xkAB4dKt8+sS7idqf2lL33JHck1c+ZM3XnnnW7/tu3Nvuyyy3TDDTe4hmN27vaBrFixQn/729/c/mvb171kyRLXPC07O9u935aZ33777dqwYcOA+6jvvvtuXXPNNbrpppv6mqzZknS7Lxu2LzyeWHfz5Uce6UZrS4urepds26biTZtUVV7uKv05ubmuE7p1RI81a/1WYcPzrIGC2/ttS9BtL3gGFXAAAIBhK6qXTpxtKzf9ngkwMjRSi3PWUO21115z+74toEeTBW1b0m7V9sjzu23/93333afPfOYzuu6665QIOtraXPO10m3bVLRpkxpqa9XT3a3svDwXwO1McD/ZfyumRQTwLP7rAYx/NFIDgFF721JpTp7fswBGhtCNPtY53Zq1FRQUaDzp7Ohwx46Vbt+uog0bXFf0rs5OZeXmalJ+vtLH4Oiz0ZoS2gNuITybAA6MT4RuABi1FVOkM4b+9QjEHZaXo481IBtvgdtYVXvhsmVunHb++aosLXUBfNv69aqrrnahPDM7W5MKClwAt2ZzsVZrw/P0uufJWuVZ+LYQnksABwAA6LOjQTp9HkvMkVgI3ZhQUlNTNW/JEjdOOfdcVe3c6Y4g27punQvgHe3tysjKcgHc/vUjgNfb8Dyt9TzlhwK4jXz+6wIAACa4tm5pd4s0I8fvmQDDR+jGhJWSmqo5Cxe6cdLZZ2t3RYXKiotdBbymqsq9bpVv2wOelZ3tSwBvsOF5Wud5yosI4IUEcAAAMEGV7CF0I7EQupHgWqyH+ajvJTklRbPmz3fjxLPOUvWuXSovLtbW9evdyzW7drll6hbArWO6HwG8SdIGz3MjO2IPuLXX82M+AAAAfi0xP2Wu37MAho/QjQRmPQDvDT2NF4aG9QQfXQC149lmzJnjxnGnn67aqiqVl5S4JmyV5eWq273bVcnDAdxu78elhk2e50ZmRAV8qs2fAA4AAMaxvZ1Sbas0JcvvmQDDQ+hGAquynT2hl98IDasBL5C0SHKnYo8+gE+bNcuNY089VfXV1W4JetHGjaosK3OvW5XcAnhOXp4vAdwega2e50aG5I4gswA+nQAOAADGcbWb0I1EQehGAtsxSA14Q2hkhgK4VcBnWQQd1WezJdyTp0934+iTT1ZDXZ1rwla8ebN2lpSofPt2JSUnKy8/XzmTJik5OVmx1i6pyPPcSIsI4Hb5IZkADgAAxtG+7uPtzzsgARC6Mc5Cd/8a8KbQSJc0P1QBn20RdNQBvGDKFDeOOukkNdbXuyXoxZs2uSBeUVLibpObn69cC+Apsf9R65RU7HlupNpXHQrgM+0HnwAOAAAS2J52qbFdmmTL/IA4R+hGgrKTrfeO4PYdtgg7NFJDAdwq4HPH5MdgUmGhG4cfd5z2Nja6JmwlW7Zox9atqtjRe3EgHMBtP3isddklCs9zwy432IVhC+AWxFMJ4AAAIAGVNEhH23I+IM4RujFOq9wHiqBFoZESCt4WwOdJblH26FiwPvSYY9xo2bvXVcAtfFsIt33gnue5/d+5BQXu3PBY65FUbsPzlOR5rvJtAdyWoqcRwAEAQAItMSd0IxEQujEBQ3ekbvuVHRrJoaXni0KVcFuSPjrW3Xz5kUe60drS4pad79i2ze0DryovVzAYVE5urmvElpo2+sA/UkFJFTY8TwHPc3u/wwE8gwAOAADiWE2r1Nwp5cT+TyhgRAjdSEDNkuqjVAMuC42k0CLshaFmbNaUbXSysrO19PDD3ehoa9POHTtUum2bijZt0u6dO9XT3a3svDwXwO1McD8OYKu04Xl6xfPc8WPzQgE8iwAOAADitIv54XZiLBDHCN2IS7W1VVr92gs64qgTNWOGVZ8j7YxRDXhnaKwMHT8WPgvcjiUbnfTMTC1escKN0y+8UBWlpSotKnJngddUVqq7q0uZ2dmaVFDgbutHAK+24Xl61fM0JSKA5xDAAQBAnCgldCMBELoRd2zP80P/+rtefP4p/evBv2vWzLlacegxOva4UzR77sLQjmRfasCSnpfcCdjhAJ476nu3qvbCQw5x47Tzz1elBfDt27Vt/XrVVVers6NjnwBuXdH9aFtX63l63fNUGFqCbiOPAA4AAHxU1Sx1B6WU0Z0MC0QVoRtxp66uWvV7anXE0Sdob1Ojamsq9cijd+vJJ/+p6dNm6YtfWSEftj9H2B0aL0quBrwwtA980qjv2RqrzVuyxI1Tzj1XVTt3qqy42AXw2qoqdbS3KyMry50FbkHcjwBuC/vrPU9rPU/5EQE8nwAOAABirMfrDd5z8vyeCTA4Qjfizs6dJWpra9bUqTM1aVKh5sxdqO7uLlXv3qWMzGafA/eANWBJr0iuBhyugNvLo2NHi81ZuNCNk84+2+37tk7oW9etU01VlWp27VJaZqYL4Fk5Ob4E8AYbnqd1nudq/ha+bRl6IQEcAADEyM4mQjfiG6Ebcbe0fPv2jUpOTt0nRKakpGrW7Pk6/PB4DnOuBizptVDVO1wBt2r46CQnJ2vW/PlunHDmmaretcudBb51/XoXvq0Kbt3PrQmbdUz3I4DbqekbPc+N7IgAPlnyZT4AAGDihG4gnhG6EVeamvaooqJMeXkFA75/5sxECW+NktaERm5EBdw6fYzua0hKStKMOXPcOP6MM1zgtiXo1oTNlqPXVVUpJSKA2+1jrUXSZs9zIzNiCbp1RE8igAMAgDFU3ya1dklZqX7PBBgYoRtxpby8RC0tTSosXLzf+1JTpclWNk04VgN+IzSyQ0eQWQCfOeoAbhXkqTNnunHsqaeqvrraBfDtmzZpV2mpez05JcUF8Jy8PF8CeJukrZ7nRobkOqBbALd2dARwAAAwVtXuQxLy78Sh9fT0uBWPpr29XZWVlVq40P6OjJ7GxkZ1d3drcmL+4R2X6POHuLJjx1b3i2WgcDhjhlV5Ez2kWQ14g6QHJf1F0rOhY8nsiDKNOoBPnj5dx5xyit774Q/rys9+VhdfeqmWHnaYujo6VL59u1uS3lhf736B+6FdUpHn6algUPcGg3oxGFSF56nHsw7xAAAAB2e8LjG/6qqr9P3vf9+9fPfdd+td73rXsD7uJz/5ib75zW/u87aWlhZdeumlevnll4f82JtuuknXXnvtKGaN/gjdiButrS0qLS1STs6kBF9aPpIa8GZJD0m6TdLTdtqkXdMckwBeMGWKjjzxRL376qt11XXX6R1XXKEVRx3lAndFcbEL4Q11derp7pYfOiUVe56eCQZ1TzCoVcGgyjxP3QRwAAAwQhVN1htIceePf/yjDj/8cOXn5+uyyy5Tba014JXWr1+vE044QQUFBbrhhhtcX6P+iouLXdC++OKL3euXX365urq69Mtf/nLAz/Xiiy/qlltucS+/5S1vccH7r3/9a9/729ra9Pe//92F76Gkp6crM9M2CGKsELoRN6qqytXc3KScnIHbT86YMd5Cd6QOW4Qt6VFJf5b0pP2qlTQ2gXhSYaEOP+44XfLBD+rqz31Ol1x5pY444QT3C75ixw6VFRVpT22turu65Af7Kks9TytDAfy5nh7tCAbVFY//9QQAAHGnrVuqs3pGHHniiSd03XXXufD7xhtvqKmpSe9+97vV0dGhd7zjHTruuOP06quvauPGjfrTn/6038dff/31uuKKK3TYYYe5120l6C9+8Qt95Stf0apVq/a7/erVq/W3v/3NvXzEEUfoD3/4gz72sY9p3bp17m2tra3u32XLlh2weEMT3LHFnm7EjcrKcgWDPa5TeX+5uVJOzkT54bfguz007Ed0bmgP+DxJoz8vLXfSJB16zDFutOzd644hK926VcVbtqiyrMwFcdv/nVtQ4M4NjzWr85fb8DwleZ7b+W57wGcHAkrnPwAAAGCIJeZTshQ3/vznP+tDH/qQzj//fPf6D37wAxeg//Wvf7l90z/+8Y+VlZWlm2++WZ/+9Kf14Q9/uO9jb7vtNq1cudJVxCOdd955+sIXvqCLLrpI9957b999G/u7LTIs/8d//IceffRR3XPPPS6E79y5091mhu3ZPIDOzk41NNjhsL1sj7fbysg+74NC6EZcsKBXXLxZGRlZE2Rp+UhqwCWhYU00ZocCuDVjSx/1vVt38+VHHulGW2urdpaUaIcF8M2btbu8XD3BoLuNnQWelj76zzdSttO9wobnKeB5mhEK4NaMLYMADgAA+i0xP/rAeTJmbCm5hd2wcEM0qzyffPLJLnCbI4880lW7w1566SW3p9qWilvItcAb6Rvf+IbbLnjhhRfqIx/5iH76058qJydnwDn86le/6iuilJeXa9GiRcNqrGsV83DVPOykk05yS9gxcoRuxIX6+hrV1VUPurR84obu/jXgstB4TtKsiAA++n03mVlZrumajY62Nu3csUOl27apaNMmdy647f3OyslxS9X9COC20LzShufpFc9zx4+FjyLLIoADADDhVTVL3UEpJU420B577LF68MEH9cUvftEFXVtCbvu4bZl5ZAdyqyBbIN+zZ4/795JLLtGXv/xlff3rX9eVV1454H3bPu+77rpLt99++5D7ryNXLVrV/NBDDx3W3D/4wQ/qd7/7Xd/rtiR+oH3nGB5CN+JCVdVOtbW1aMqU/S9PWp6aZsdbo18NeGdorLQd7xFngduxZKOTnpmpxStWuHH6hReqorRUpUVF7izwmspKdXV29gbwggJ321izX/nVNjxPr3mepkQE8BwCOAAAE1KPJ1XuleYO3JM35r70pS/pmWeeceHbgrFViW3JuYVfa1YWKSMjw+25nj17trudhXL7+JSUFLcc/LHHHtPRRx/tbnvjjTe6gP7e977XjYEEg0H95je/2acLue0xH+z2/Vn4tzlFzg8Hj9CNuLBzZ7ECgaQBl7sUFtpVOoLUMGrAkp6X3AnY4QCeO+p7t6r2wkMOceO0889XZWmpyrZv19YNG1RXXa3Ojg5lZme7s8AzMjN9abxhfUBrPU+rPU8Ftvs9FMDzCOAAAEwou+IodFvH8ueee05FRUX64Q9/6PZIWwdye7n/Xu29e/cqLa23d0+4Cm7Lz8vKytTc3Oz2glsAN3ZW99KlSwf9vFaR/vjHP66HHnrIVc0tyFvDNjsqzLqhI/YI3fBdV1enOyosO3vggDhlCsFpZHaHhu25mRIRwPNHfc+2RGnekiVunHzuuarauVNlxcXatn696nbv1u62NmVkZbk94BbE/Qjge2x4ntZ6niZFBPB8AjgAABNiiXm8mTVrlmt69tvf/tZVkG2JeeTS7ZKSErd8u9AqTf3YkV9nnnnmPkvId+3apXPOOWfAz2V7va0hWzhkW+C2o8I+8YlPuPf/+9//HnTJOqKH0A3f7d69S01NDZo82Sq0+5s6lbA0yhqwpFckVwO28L3I1g+M+p5TUlM1Z+FCN046+2ztrqhQeXGxtq5bp9qqKrcPPD0jw1XAbSm6HwG80ZqVeJ4bdknHwreF8EICOAAA41JNa3zt6zZ2zNfy5cv1rne9y71uIdr2ddsZ3haQrXu5nasdbrQWeQSYvc8CeyTrQj5nzpz9Pk97e7vreG4h+9lnn3Uh3qril156qXJzc11HdGu8ZhX1D3zgA1H+qhGJ0A3fVVaWuWp3evrAe0WmWLEWY1UDlvS6ndwdUQG3lmSjY/+RmDVvnhsnnHmmC9wWwG0PuIVx2wduy9QtgFs3dD8C+F5JGz3PjeyIPeD29OIsSgAAxoegJ9W0SDNHv8NuTNje6+9///t65JFH+t5my8RvvfVWXXbZZbrhhhvc9sqnn356n+Xhtgz8mmuu0U033dR3LJgtSd+6dasbFuL7s+q2nf1te7ezs7P1l7/8xe0LP/XUU93LtlzdPvdVV13luqJb0K+urnZhPby03bS0tLjgXlVVtd8+cZubVc8xMoRu+G7Hjm1KTR34/OnsbNvPQiCKTg14TWjkhjqgWwC31Qaje7ztPxwz5sxx4/gzznBVbwvg2zZscMvRbRm6VcltCXp2Xt6wjq0Yay2SNnueG5kRAdwuPyQRwAEASPgl5vESugsKClRXV7ff222v9fbt2/Xaa6+548Miz7/+zne+46rj1gjNqtRh1oTtvvvuc+d7DxR87e3GjhhbsmSJ+7zf/e53dd111/UVGN7//vdrw4YN+vvf/+5C99e+9jX93//93z6hO8zOE49k92vHm9mydYxMwKP3O3zU3NykP/3pJ0pJSVVeni1/3tf8+QGddlocrQ8a97IjArh1RB+7x95+1dRXV6u8pERFGzdqV2mpWvbuVXJKigvgORbA+y2rijXrIzontATdLj8QwDGm8vKkiD+eBrNxS5Fuve0uLV00f8jbBfLnK2n+aWM4QQAYH+bmSW8dvM9Y3LNwa43VLLAfLGuidvzxx2vaAEcA2d9krPKLLSrd8H1puQXv2bPfPKsw0tTRr3zGiGvAG0IjIyKAzx51ALdf7pOnT3fjqJNOUkN9vcq3b1fx5s2uEm7DlqnnWgCfNGm/fU2x0CFpu+e5kRoRwO3yQzL/cQIAICHsbrFg2XvsbCKyJeCjCdzmbW9726DvI3DHHqEbvtq1q8z9UhwsYNG53E/ttgg7NKwGPD8UwK1xx+gCsf2yL5g82Y0jTzxRTXv2uC7oJZs3q3T7dlUUF7v/Utoe8FwL4KEjMmKpy7qJep4b9tlnh5agz7JfnPzHCgCAuNXZI+1plwrfbPgN+IrQDd9YM4aSki3KzMwa8P2Ws/JHf8oVxqwGvDU0rAY8LxTA543JrxEL14cfd5wbzU1N7hzwHVu3qmTrVlWUlrrL1VYBtwBu+8FjrVtSqee5YZcbLHhbALcgnkoABwAg7uxuJnQjfhC64Zs9e2rV0FCv7Oy8Ad9v/SSSkgg08cdqwNtDIyVU+V4UCuADN8QbCdvbfegxx7hhe753lpS4AF68ZYsqy2xlhOcasFlQt3PDY61HUrkNz1OS57ml5/NCATydAA4AQNwsMV/BNkXECUI3fFNbW6W2thZNmWKxZX+cz50IrAa8IzSSQ3u/F4aWog98BNxI2PFiy4480o221tY3A/jmzdpdXq6enp7eAJ6f744ki7WgbZGwYQ1JPM81X7MKuO0FzySAAwDgm2prUwPECUI3fFNT03v232BHRhG6E43VgMtCIxBahB0+C3z067sys7K09LDD3Ohoa9POHTtUVlTkOqHbueA93d3KyslxFfD0jNEH/pGyYyDsGV3leXrV89zxY+GjyLII4AAAxFRDu9TRLaWTdhAHeBrCNzt3Fis9ffBwFHFcIRKORdCK0FgVOn4sHMDtWLLRSc/M1OIVK9w47YIL3L5v64RuZ4HbueBdnZ19ATwjM9OXr77ahufpNc/TlIgAnkMABwAgZtXuuZP8ngVA6IZPWlr2qra2WllZOQO+3xqopaURTsYHi6CVofG8pGkRAXzg/fwjYcvKFx5yiBunvOUtbt+3NWLbun699tTUqKO9XZnZ2UrPznbngOfl5irWam14nlZ7ngpCe8AtgOcRwAEAiOq+bkI34sHoDt4FRrGfu7V176Che/Jkwsj4ZTXglyTdIeleSattEZh7z/r163XCCSe4sylvuOEG1zTtQO6++27Nnz9fs2bNci/PW7xYp19wgXY2N+u7v/mNsmbOVOHUqXpm5Upt37JFVeXlam1uHtZ9R8MeSWs9Tw8Gg/pXT4/eCAa1x6e5AAAwntW2+j0DoBehG76ord3tmmClpAzcfbrAyoGYAKwG/IqkO9XRcbve8Y4Lddxxh+nVV1/Vxo0b9ac//WnIj7aQfsUVV+jGG2/Uo48+qm984xvasmWLe98f/vAH/f73v9eqV17RBz/zGc1eulTvu/JKTZs1S3sbGlS6bZuriluHdL8CeKN9DZ6nh4NBPdDTozXBoOoI4AAAjIn6Nr9nAPQidMMXlZVlSk4efHdDfj6V7onm4YdXqrGxQT/+8fFavPhV3XzzZfr9728Z8mNuvfVWnXPOObrmmmt0xBFH6DOf+Yxuu+22vvcfddRRqq+v10svvaQLLrpIJ559ti7/1Kd02bXX6oL3vEez5s1TS1OTSouKtKu01J0R7lcA3ytpo+fp0WBQ/+zp0evBoGo8z7f5AACQ6Jo7e5upAX5jTzdirqurS7t2lQ26tDy8pxsTy9q15Tr55EXKyrKzvht15JGeNm5cL+n2iD3gdijXmxdk1q5dq7e+9a19r5944on67//+b/eyhdXdu3crLy/PVcG/9a1v9XXLnzFnjhvHnX66anfv7mvCVrVzp+p271ZySoomFRS448gG664fTXbKyWbPcyMzogmbdURPYh84AADDVtcmzYp9OxdgH4RuxFxd3W41NzcpP996Ou8vK4smahNRU1O7Fi58s2V9IBBQcnKS9uzZrYKCZknr7NkhaYGkRa4jelNTkxYutDDeywL2rl12crZ08cUX66yzztKPfvSjvvvrz942dcYMN4459VTV19SovLhY2zdudB3R7XUL4HYOeI4F8GQ7izy2bGXcVs9zw04itzPArRGbtaNLJoADADCkulZCN/zH8nL4sp+7o6N90OPC2M89MaWkJCm932GaGRmpam3tjHiLdUTZKOlBSX9RSkqb0tObJAVDt89Qa2tv15Rf/epXqqmpUTAYdMvMDz30UH3xi18c9PNbAJ88bZqOPvlkvefDH9aV112niy+9VIccfrg7gqy8pEQ7i4vVWF/v+hH4oUPSds/TU8Gg7g0G9UIwqJ2epx6WoAMAMGilG/AblW7EXHV1hQs4A1UeDfu5J6bCwmytX99bpQ7bu7ddaWmD/ZpqV2Fhkmpqngn9KpunvXvblJZmy9N75ebmas+ePXrggQf00Y9+1AXxb3/728rJGXxrg7HnZsHkyW4ceeKJatqzx4Xu4k2bVLp9uypKSuxGyp00yVXBrRoea12SSjzPDfvss0NL0GfZo0EFHAAAh2ZqiAeEbsSU7bMtLy9RRoYtEx5YQQGBYSI64YQF+t3vVva9XlJSq46ObhfGh/qYF14o1kc/erqkbVq9epVmz7bnz5NuD/jDD6/TRRddpN/97ndu7/fs2bNdCD9Q6O4vr6BAh9k49ljXbM2WoJds2aIdW7e6ZejyPOWEAnhK6sAd+aPJesSUep4btgB+Zugs8FmBgNII4ACACWxPmxT0rCeK3zPBRMbycsRUY2O9GzRRQ39nnrnU7ev+4x9XuddvvvlhveUty92+7oaGVvX09C4hj/Te9x6jO+54VevWVai5uV0///lTuvDCFbYIW9ITevHFW3XyydY/wNO2bZtUVVWl/FE+wWxv94qjj9bbPvABfej66/Xuq65y+8GTk5PdEWR2FFlddbVbku4HW/i+U9LznueWoD/d06PtwaA6WIIOAJiAejypod3vWWCio9KNmKqrq1Zra7MKCqwP8/6sT9UIi5AYJ1JSknXrrVfqsstu1Q033OO6hj/99Bfc+woKrtfq1V/X0UfP3edjjjpqrj73uXN1/PE3KyMjRUuXTtOnPnWWe19tbbOWLLFmfTt0xRWz9Pa3f01ve9vJys21JezzbQf4qOeclZOjZUce6UZba6t2lpS40L190ybt3rnT7f22DuhWAU9LtzZosWWXKeyr3eV5etnzXO93W4JuzdgyqYADACbQEvNCOw4E8EnA4xBYxNCrrz6nJ564T/PmLRnw/ZMnSxdeGPsO0YgfVVWNeu21Mp188kJNnjy8KzAbN+5SRUWDzjrrkCH2gEeywGm7nxeGuqEPvt3hYHS0t6tixw4XwIs2btSeujr1dHe7kG5L1dMzRh/4R2taxFFkWQTw2MjLky699IA327ilSLfedpeWLrKLQ4ML5M9X0vzTxnCCADA+HTVdOmmO37PAREale5yzSpstezXV1dWuejhlysBHdcXquLBAYPBdDTRRw4wZk3TxxUeM6GMOPXSWG8Nn1xorQsOWs8+ICOCjX2phoXrR8uVunH7hhW7fd1lRkTsLvK6qyp1Vn5md7QJ4RqY/l96rbXieXvM82UFtFr5tH3gOARwAMM7QwRx+I3SPY/aH/ZFHHqnf/OY3OvPMM/XVr35VWVlZ+vnPf+7LfGxRRVXVTqWnDx4y2M+N2LMAXhkaz4dqwAtDI2/U956alqYFS5e6ccpb3uL2fZdt365t69e7c8CtKh4ZwAfr6h9NdTY8T2s8TwURFfBJBHAAwDg5qxvwE43Uxsif/vSnvmOwIoe9/ZlnntGKFStchfnHP/7xsO7vQx/60ID3t2PHDu3du1fXXHONZsyYoQULFugXv/jFgPdx++23uzOLTzrpJPf6jTfeqD/+8Y9as2aN/NDSsld79zYqI2Oo0M0f+fCb1YBfknSHpHskvS6pYUzuOTU1VfMWL9bpF1ygq667Th/4xCd0ztvfrsKpU9VYV+eWo1eVl6u1udldpPLDHklveJ7+FQzqXz09eiMY1B52IQEAElhbt9RmZ20CPqHSPUYuv/xyvetd7+p7vbm5Wcccc4wL2xdccIG++MUv6rLLLtOll17q3n7OOecMeX92nvBPf/rTvtdfeOEFfe5zn9PcuXN19dVXq6KiQi+++KIL4e9973tdBdvOIQ5rampyIfs73/mO0kMNnObPn6/rr79eH/jAB/Tss89q+nRrqxQ7DQ31am9v1dSpg5ezC6zMBsQNVwO2bgT27IyogNuC7NGxs73nLFjgxolnn63qXbtUvn27tq5fr5rKSvd6WkaGJhUUuL3gflTAG214ntZ7nnIjKuCTqYADABLMnnYpM/anegIOjdSi5Oabb3aB+NBDD3XLuzdu3Oj+aP7nP/+pu+66S3/5y19GdH8W3K36bQE7NzfXhfDjjjvOve/LX/6yNm/erPvvv7/v9hbA7W3PPfec28cd1t3drQttj2lFhR5++GEtXGgBIjbWr39NDzzwV9dEbaAAYVtb3/1umqghEeRFBHBbjj52gsGgC912FrgtQd+9a5daW1rcMnUL4Nm5ub4E8EhZEXvArUOE3/NJGDRSAwDfnDlfWu5fWyNMcFS6o6C9vV0/+9nP9NJLL+nb3/62q2qH/yg98cQT9Z//+Z8jur9XXnlFJSUlrkpeW1vr9mrPmzev7/3WKC3cLM3YcvN77rlHK1eudI3U7I/4SPfee6+rdh9xxBG66aabXPU7Fhoaal3X6MH+QM+1UhqQEJokrQ2NnIgAbqtHRhdA7SLZ9Nmz3Tju9NNVu3u3q4BbF/TK8nLVVlUpJTW1N4Dn5e1zUS1WbGvcFs9zwzaL2BFkFsLt8kMSARxAgrH6U0N1hQqm0956PGvq8HsGmMjY0x0Ftpfa9lHbfmtb5h1ZTc7Ly9OuXXZyrtwe7/z8/P1G/z3XFqKvvfbavs7jtsTcKuampaXFVc7PP/9897otOb/hhhv0wx/+0IXqtLQ0t480ctjnsAsBtlzdlsHHyu7dFUpNTRv0/bm5/LGORGQ/Q+sk2UqTv0paGeqKvu/FroNhF6imzpihY087Te+75hp98DOf0cWXXqrFy5e7BmwWxu1s8MY9exTs6ZEfrCHsNs/Tv4NB/SMY1EvBoDsXvIdFVABG4WefvUjPP/An9/Kz9/5WN1w4U9eelKoffvwsNdZa48uh9XR36e6f3aD/vHie+9j7b/mGO7rRvPDg/+n6cwrcv2bTS4+rrnJHlL8i+K2x3e8ZYCKj0h0Ft9xyi771rW+5l1NSUvr2VJuMjAzX3My89tprAzZLmjXrzaOP6uvrXcC2yrmx4P373/9eV1xxhe677z69/vrrrrL+wQ9+0L3/5JNP1qZNm1zgt33mtsTcAnZdne1L7WWdzG1/t1XOY6W7u0s1NbuHbKKWM/qTmgCf2c/2xtDICB1BZhfdZo/6GqcF8MnTprlx5IknqrG+3i1B375pk/vXhq14ycnPV+6kSfusfokVKyJs9zw3UiMq4HYgWwoVcADD9NLDf9XGFx7VCRdcqqI1K3X/LTfqozf9VTMWLNetX79cd//0S/rod+wi5+Ae+O23tWHVw7ruF4+ou7NDv/nKf7i/ud557U166s7/1ce+d6fu+9VXdcrbr1bR2lW65BPfjtnXB39Q6YafCN1jrKioyI1w5bmwsFA1NTV977fO41Z9NhZ8D8SWgp9xxhkqiOgwZvddVlamrVu36qKLLtKXvvQlV0EPC1fWrbna+vXrXcXbwn+YVdqtWh5LjY31amtrUV7e4E3UqHRjfLFL6ptDw37m7efdfjbnjPpXrwXw/MmT3TjihBPU1NDgQnfxpk3uOLKKkhK7kQvfefn5rmlbrFmT2BLPc8M+++xQAJ9pXdwJ4AAG0dJYr7t/+kVNn7/Mvb67bJs++NXfaMVJb3Gvn/qOD+vx235wwPt58V9/1vu/8BPNWnSoe/38D35Rz95ziwvd9jmWHX+2+7e+qlwF01hWPhEQuuEnQvcYu/POO/X2t7/dLeM2J5xwgltuHrZ69WrNnj17RPc3UEXaKuYWuu2P789//vODfvxf//pXF8zD7CpvZWWl5syZE/PO5R0dbUpPt5rXwNjTjfGr0xZhh4b9brCLXotC/46+laoF68OOPdaN5qYmF8B3bN2qki1bVFFaaj/4ygkFcNsPHmu2oLPU89yw+rsFbwvgFsTTCOAAItz10y/q6LPfra4O27winXbJh/d5/+7SLZo2d+kB76e5oVaFM97sf5OUlOyGycjOdWE+MztPrzz6N531vk+N+deB+NMVlFq7pCw6mMMH7OkeY4888ojOPvvsvtcvueQSrVq1Sk888YRrgPb973/fdQ8fjra2NnfGd+T9hVlzNFvCbo3QrKI9kN/97neuCdvHP/7xvrdZ1d06mEcuYY+FhoY6+7u/7z94AyF0Y2KwGnCxpCck/VnSY6EwbsF89HLy8rTi6KP11ve/Xx+6/nq95+qrdcxpp7nl5pVlZSotKlJddbW6Osfm842U7TzfaXsqPU/3BoN6uqdH24NBdbAHHJjwtrz6lDa//KTee933B3y/Vaafu/c3OvO9nzzgfc1bfqzWPNPb/8Z6Xrz40G1acVLvKsQTLrhMN112pI45973q7upQRhb72yYKqt3wC5XuMWQh2TqW//a3v+17mzU++8lPfqK3ve1tysnJcU3M/vSn3sYgB/L888+7ZeWLFllFbF9//vOf3R/RdozYQPP4n//5H/d5H3zwQU2bNs0F/jfeeMPtD1+yZElfJT5WamurhuyybMeFpaRQ8cJEYxF0R2jYz8fs0BL0BaE94aNj53sfcsQRbrS3tmrnjh2uAm77wHfv3OlON7AjyPIKCpQW0XsiVqzVnLWVtMZrL3ue635ux5DZXvBMKuDAhNLV0a6/3PwJXfFfv3aV6IH87X8+rUVHnqrDT3vrAe/vsq/8Ur+8/u3aseFl1ezcrvqqMn3kv29z77voQ1/RWf/xSa1//hHlTJqs7155vKbNWaJrbv4bRyBOgNA9g2ss8AGhewxlZmaqo2P/S2if/OQnXXXbmprZ/mwL38Nx3nnnqaqqasD3WdgeKHBbBdyq61bRfvrpp3XMMce4t1vIvu6661z4/vWvf61YsiXtVVUVSk8fPETQRA2wCFoeGs9ZS8WIAD7wapaRyMjK0pJDD3XjjIsuUsWOHa7qXbRhg6p37XJdfS2kWwBPzxh94B8pq3PvtuF5esXzNDUigGfzRzAw7v3r1pu04NATdMTpFw/4fus0vuW1p3Tj7XZU44HNPeQo3Xz/DlXt2Kw/fONKnXrJhzVl9punyWTmTNKu7euVN3mGDjn2LJWsf0mVJZv69oBjfKKDOfxC6I4Ra24WeXRYtFg1+e9//7trrBbZPM3YMnc/WAO1vXsblJExeHDIzuaPamDfCFoRGqtC53+HzwIf/RUqC9WLli934/QLLnD7vq0B27b161VXVeUuzmVmZ/cFcD8qP9Z+ssbz9JrnaXJoD7iNXAI4MC69/Ojtat5To8+f3dtwtbO9Va8+fqerVJ96yUd0xw8+q0/96H7lTbbfh8OTlJzs7sf2gX/mpw/u875d2zdo1uLDVV22VbMWHeY+d0vjmye9YHxieTn8Qugeh6xjejxpampQR0e7sgdZLmaodANDBfCq0HhBcjXgRaEA/uapBQcrNS1NC5YudePU885TZXn5mwG8utqdB+4CeH6+q5b7EcDtz+A6z9Maz1NBRACfRAAHxo0bfvecenp6z9E29/z0S1p4xMk6+eKrdNNlR+mCK7+s+Ycer/bWZvf+8D7sVruon5XrAvZA7v/NN1zn8vyp+/ayWf3UvbroQ/+pZ+6+RTUVxarfXa6s3MFPWMH4QOiGXwjdiLrm5iZ1dnYqNXXwPaPZ2TGdEpDAXA3YTrKVXA04XAF/81jBg2WdzecuWuTGyeeco6qdO3sD+IYNqq2q0u6KCqVnZmpSQYEL4n4E8D02PE9veJ675DAvFMALCOBAQiuYvu+pKulZOcrJn+K6izfVVbmzum2E/ebV3uaL159ToK//dbXmLjt6v/vc+tozKt+yRh//f3ft83bbTmPLy5NTUnX02e/Ujz95rqbOXaKZiw6L2teH+NBI6IZPAp5tuAWiaPXqF/Too3dr3rwlg97mvPOSNH06fzQDBy8/ogJuYXzsWMM12/ddHgrg9nJba6vSMjJcALe94H43H7Kal4VvC+GT4zWA5+VJAxwB2d/GLUW69ba7tHSRne0+uED+fCXNP20MJwgA499VR0kZlB0RYzzlEJPl5Qe6tEOlGxitBkmvh0ZeRAXceoKPjp2UMHPuXDeOP/NM1VRWamdJibauX++q31YFT0lL6wvgQ51UEC224HST57mRFbEE3Rbj+31BAAAQP5o7Cd2IPZ5yiLr6+uohjyizv4cHOWocwEFpkrQ2NHJCHdCtCm4NiEYXQC1QT589241jTztNtbt3a2dxsauA237wut27lZyS4pqw2ZnhfgTwVjvv1/PcyIgI4Hb5IYkADgATWmuX3zPAREToRlTZ7oX6+hqlpWUMeUZ3UhJ/CAPRqwGvD43MiAr4zNDZ4AfPKshTZ8xw4+hTTtGe2lqVFxeraONG1xHdlqNblTy3oEC5FsAHaXQUTXY6zDbPc8O6SswJBXC7/JBMAAeACYfQDT8QuhFVra3N7siwtLTBm6j5cCQwMEG12Y7h0LAfvPmhAD7bIuioA3jh1KluHHniiWqsr3cBfPvmza4Sbi9b1dsF8EmTXBiPNeufs93z3LC1N7NDAdwuP6QQwAFgQiB0ww+EbsSgc3nHkMeFEboBP1gNeEtopFkf8NAS9Dmj/k+DBfD8yZPdOOKEE9TU0OCCd/GWLSrdtk0VJSVuX4mF79z8fKWkxP4/RfY31w7Pc8M++ywL4KF/UwngADBuEbrhB0I3oqqlZa8L3UMdF5aRwR+4gL86JRWFhtWA54Yq4BbEB+/HMFx2xvehxx7rRnNTk2vCVrJlixu7duxwt8mZNMndzo4tizU7GbjM81Rm9X7Pc5Vvq4BbJTyNAA4A4wqhG34gdCPqlW4zVDMlKt0Yq/4BFRUNmjNn9OdVT2z210hxaCSHKt+LQkvRrSI+OtZcbflRR7nR2tzcG8C3bVPJ5s2qLCtT0POUk5vrGrGlpo3+841Uj6SdNjxPSZ6nGREBPIMADgAJj9ANPxC6EfVKtzT0eWGE7onjuuvu0C9+8VTf64sXT1VR0XcGvf23vvWAvv3tB/d7+1NPfUGlpfX6/Ofv1E9/+n5dffUpevzxTcrKSiN0j3kELQ2NpNDe74Whbuij/8G148UOOeIIN9pbW7Vzxw63/Hz7pk2q2rlTwZ4eZYcCeFr64KtloiUoaZcNz1PA81z3czsH3JqxZRLAASAhEbrhB0I3oqqxcY8CgaEbJhG6J45XXy3Vv/71GZ166mL3enLy0N2z//M/L9LnP39e3+ulpXU6//yf6Zhj5umGG+7RnXd+TF/96n0udK9aVaRvf/uSqH8NE5dF0PLQeC7U/XxRKICP/sy/jKwsLTn0UDdOv/BCVezYobLt21W0YYOqd+1ST3e3C+kWwNN9+KVhlw532/A8veJ57vzv8FFk2QRwAEgYbYRu+IDQjajas8eOCxu6QsWe7omhu7tHGzbs0plnLlVOzvBCU0ZGqhthX/7yPfr858/VpEmZqq9v0dlnL3P/lpfXU+GOeQR1NWBJq0Lnf4ePIrNzwUfHQvWi5cvdOO3887WrtFSl27dr2/r17hzwrs5OF9InFRa621rjtlirseF5et2Ww7e3a1ZPj46ZMUPJrXZKOAAgXvV4Unu3lEEKQgzxdEPU9PT0qKmpQampQ+/LpNI9MaxbV6Fg0NPRR3/H7b0+66yl+u1vr9S8eYXD+vhduxr0j3+sUUnJd93rubkZ2rZtt/LyMvW3v72iT33qrCh/BRg8gFeFxguSqwEvDFXB80Z977ave/7SpW6cet55qiwvdxVwF8Crq9XZ3q50C+AFBS6I+xLAe3q0vaFBC6+6SpOtf4V1Z7fR0BDzuQAAhrfEnNCNWOLphqix87k7OjqUkZE55O0I3RPDxo2VWrZsun7xi0s1ZUqOrr/+Ln3847fpkUc+N6yPv+WWZ3XZZSf0Vcnt5SOPvEnf+tbb1dHRPezqOWJSA5b0sqTJERXw0a9EsM7mcxctcuPkc85x+77t/O+t69ertqpK1RUVSsvMdAE8MzvblwCuKVN6xwkn2FKfNwN4XV3s5wIAGDR0Fw795ykwpgjdiJq2tlZ1d3cpJWXwapf9TexDfyT44IorTnIj7Fe/ulwLF35VTU1trlo9lJ6eoH73u+f05JPX973tK1+5SJ/85Fl65JH1mjw5R8cf/10tWTJNf/vbNf6ELQygLjRelZQfEcCnjPqek1NSNHvBAjdOOOsst+97ZyiA28s1lZVKTU93x5BZMzZfnhMFBb3j2GOlpiapsjL2cwAA7Idmaog1Qjeipr09HLpThqxyE5AmpmnTct1y88rKxgOG7qee2uKC9aGHztrn7ba3e/36XZoxI09nnXWIXnqpRJs2Ve53O8QDW2q9OjTyQg3YbAm69QQfneTkZM2cO9eN4844w1W9rQJuS9CrKirc67ZM3ZqwWQAf6gjDqMnL6x0AAN8RuhFrhG5ENXT39HQrOXno0I2J4YYb7nZdxy+//ET3+gsvFCspKaC5cw+8p/vOO1/Ve95zzH5vt8Zshx8+S1u3Vuuww2appqZZdXUtUZk/xlKTpDdCIzuiAm6nYo/uIpwF6mmzZrlx7GmnuX3f5dYFfeNG7SorU311tauSWwC3M8N9CeAAAF9ZIzUglgjdiJr29jZXxR6qkk3onjiOOmqOvv71f2r69Fz19Hj67Gfv0FVXnezO1rYl5pmZaUpNHfh4uUce2aA//elD+7393ntXu2PFbrnlGRUX17gu5vn5oz++CrFkF0nWh0ZmRAC3I8lGF4jtd8+U6dPdOPqUU7SnttZVwO0ccDsT3MK4VclzCwqUawE8eejjDQEA40Nnj98zwERD6EZUK90HwnFhE8cHP3iyNmyo1Hvf+xt3PvcHP3iSbr75Xe591hDtpz99v971rqP3+7jt22u0a1ejTjzRliPvewSZLS+3oP7Odx6tc8/9sZYsmarDDrOwhsTUZi33QsOaPSwIBfDZtoh81AG8cOpUN4488UQ11tdrZ0mJtm/e7MK3vWy3yc3Pd8PCOABgfCJ0I9YCnufZeS/AmHvuuUe0cuVjmj9/6aC3Wb48oGOPZXkngKHYsYPzQnvA54z59eKmhgbXhK14yxaVbtumvY2Nrstj7qRJLoAP1ZfCtLW0uPu46rrrNHna6Peob9xSpFtvu0tLF80f8naB/PlKmn/aqD8fAEw0c/Oktw7+5ykw5qh0I2r27m0acj+3OcDfsgBgNQlJRaGREgrgC0P/po763q3D+aHHHutGc1OTq3qXbN2qki1btGvHDncSuQVwu50dWwYASGxUuhFrRB5ETUtL0wErRIRuACNj3W+KQyM5VPm2AG5V4dGfP2jN1ZYfdZQbrS0trgK+o6hIxZs2qbKsTEHPU05urmvEZh3RAQCJpyvo9www0RB5ENVKd0rK0FUhQjeAg2elitLQSArt/V4Y2gs++i6NWdnZOuSII9xov/BCVZSWase2bdq+caN279ypnp4edwSZnQcOAEgcVLoRa0QeRIWdz93R0UboBhAjVrYoD43nQt3Pw53QR9/RPiMrS4tXrHDj9Asu0K7SUpUWFbmjyKwrekbm0GfNAwDiB6EbsUbkQdSOC7PgnZY2dLUpJYXu5QDGmu3C3hUaz0uaFmrCZgE8Z9T3np6RoYXLlrlx2vnnuwDe2dnpOqMDAOJfF6EbMUboRlRDd3Z27pC3o9INIPoBfHdovCBpakQFfNKo7932dc9fSgtcAEi0/zJY8E7ldEjECJEHUTuju7u7W8nJLC8HEE9qQuNlSYURFfACvycGAIhxMzVCN2KFyIMoVrq76V4OII7Vh8arkvIjKuBT/J4YACAG+7qzOAUSMULkQdRCdyAgBex/hsCRtwDiQ4Ok1aGRGwrfi0LL0ek9AQDjDc3UEEuEbkSF7ecejmSW9QCIO3slvREa2REV8BkEcAAYJ2imhlgidMPX0E2lG0B8a5G0PjQyQ2eAWwCfFTobHACQqHu6gVghdCMqqHQDGH/aJG0KjXRJ80NL0GfbbzO/JwcAGAHPWpgDMULoRtRC94F+mVngTkpiqSaARNQhaWtopEmaF6qAz+U/rQCQAMjciCX+MkBUdHV1uUZqQ6FzOYDxoVNSUWikhIL3olAQZw8NAMSjIKkbMUTsQVR0dLQrEBh6v2MS2yEBjDvdkkpCw5aczwlVwOeHlqQDAOIBy8sRS4RuREVnZ7uSkobe43igSjgAJDZrjVsaGkmh5muLQs3YMvyeHABMaFS6EUuEbkRFZ2eHkihlA0CItcndGRrPSZoZcRRZlt+TA4AJh8yNWCJ0Iyo6OzsJ3QAw6J96u0JjlaTpofBtVfAcvycHABMClW7EEqEbUUGlGwCGa3dovChpqiYX5mpKIb8/ASCa2NONWCJ0Y8x5nqeuLgvdnFsLACNTo+lTa/TVLxSotr5VxTtSVLwjWXsa+H0KAGOJzI1YInRjzAWDPeru7qbSDQCjMKUwqCmFnTrxWGlPQ0DFpSkqLklRbT0BHABGi+XliCVCN8acBe5gMEilGwDGSEG+p+Pyu3TE4V2q3hPQVgvgNT2uPRsAYORYXo5YInRjzHV3d7lqd0pKqt9TAQBfdCtZXUpxo/dlG0nqVlLo34C63O16/+2Sp2557t+97W2q3rNHqZmpCiZLnl2/TPHkpUgBe3mKJ03pktdcIm08RYFkftcCwEiRuRFLhG6MuZ6ecKWb5eUA4ldP0FNHj9TWLfdvR1Bq75E6g70vWwC2l3tDsdTe1aXWrk51BrvVkxRQIDVFqVmZSsnMVHJGqroDPep2/9cjz53RbaNj5BPLkJJmprh7iRTod7NAToe8wBZJh4/ugQCACYjl5YglQjei0kht4D8RAWDkuoOeC8YWiNu7e0OxC8eevey5f7u8cDgOqNs+JiD1hEdS77CqcTBJrmLcWz0OSHZtcNBCcf/fYemh0ct+03W60fu/vli2Rt6W5Qok8Z9zAADiFf+VBgCMiY4eT21doYqxhWOvt4Ls/g16vcF4sHAcDsbhkRwRjpMDUtpgn3ViX9wL5LXK87ZJWuH3VAAgoSRN7P98IMYI3QAwgQQ9zwViGxaI220pdWhJ9T6V49CS6u7IcGzBOPRvXzC26rH9lyQcjgftn8hfN1GzdLW8okMUoHklAAxbCrsgEUOEbgCI0yXVLhz3hMJxxF7j3oDsDVg1jqwcR1aN32zIZcupAyNYUo14FyholhcslpKW+j0VAEgYVLoRS4RuABiFLltSHRGOww25XCMub/D9xvuF44hg3LffeNAl1Ya/FhBh0evyShcrEKB0AwDDYYuzgFghdMM3nI+IWGrv3rdy3NGvahwZjrsH2W9sw+vfjIsl1YgDgamN8raXSqkL/Z4KACSEZK5RIoYI3fBNj52mAwyy39iF44j9xh0WiG2/sfqFY6saaxj7ja1yPOhvPMIxxoEFr8vbuUCBAM9nADgQKt2IJUI3fEPoHh/7jSMrx53D3W8c6A3FfZXjUKdqF47ZbwwclMCMOnkl5VLaPL+nAgBxj0o3YonQDV9Dt53pTVUmejp7BmnGFepSbcG4r1O1F3BhuKvf+cb77Td24Zj9xkBcmvu6tJvQDQAHQqUbsUTohu/BO4Vn4Yj2G1vl2ALxPsc49VtSvU84Zr8xMGEE5lTLK9+lQNosv6cCAHGNSjdiibgDX3V3J07o7gl6vdXifvuNXTh2FeM3K8e217h3z3Fvxbg7tN84smrcd4QT+40BjKWZr0t1hG4AGAqVbsRSgsQdjFfR2Ndt+43tCKeOQfYbd1g4DjXj6gvH/ZZU71c5TgkFY7sqyn5jAHEssGCXvMrdCqRN93sqABC3qHQjlgjd8FVju9Qc6F1abRVi16k6tN/YAnLvcup9m3H1BeNQM679wjH7jQFMdNNWSw0X+T0LAIhbVLoRS4Ru+OohC85ZA/3W4zchAByswOIyeS/UKZA22e+pAEBcotKNWOLphijyDniLgB26DAAYe4Wr/Z4BAMQtKt2IJUI3xlxSUrI7BsyOAzsgWzsOABh7S4vldTb4PQsAiEvprPdFDBG6MeaSk1OUlJSkYDB4wNsGuocRzAEAIxZIkrz8NX5PAwDiTmqSlESlGzFE6MaYS0npDd2eN4zQTaUbAKImcMg2eV17/Z4GAMQVqtyINUI3xlxysi0vH16lm+XlABA9gWRPXs5av6cBAHElPdnvGWCiIXQjKnu6bVDpBgD/BZZtltfV4vc0ACBuUOlGrBG6MeasiVpqaurw9nQTugEgqgKpQXmZ6/yeBgDEDSrdiDWu8yAquoI9Kq3cqV31terp6XajOxjs/ddeD3rq8XpU2DVdy0440u/pAsC4FlixQd7qoxRIyfR7KgDgOyrdiDWecoiKqr0NKq7b7fZ3W1O13iXn9m+SAklJSklNde9LTsrwe6oAMO4F0noUTNugQPB4v6cCAL7LIAEhxnjKISoWLFimtp6g5s+aP/QNp8ZqRgAwsQWWr5O37ggFktP9ngoA+Irl5Yg19nQjKjIzMofXvbw1FrMBAAQyu6TkTX5PAwB8x/JyxBqhG1GRlZGlnp6eA9+wLRazAQA4y9bK66GDJYCJjUo3Yo3QjahIT0uXJ+/ANwxKXvswbgcAGLVATocU2Oz3NADAV1S6EWuEbkRFWlqaOzpsWKh2A0Bsq93Bbr9nAQC+oZEaYo3QjahIS0kb/o3Z1w0AMRPIa5W8bX5PAwB8w/JyxBqhG1GRljr80O21srwcAGJq6Wp5wWH03QCAccbWYWal+j0LTDSEbkRFauoIfpuxvBwAYipQ0CwFi/2eBgDEXHaaNNwdkMBYIXQjqpVujg0DgDi16HV53jB+RwPAOJJNlRs+IHQjaqE7OSlZPcNYvui1sbwcAGItMLVR6t7h9zQAIKZyRtB2CBgrhG5E7Zzu1JRUdXcPo0MulW4A8MeC1fI8LnwCmFjLy4FYI3QjKrIys9y+7s6uzgPfmNANAL4IzKiTusr9ngYAxAyVbviB0I2oyM7MVnpa+vBCd5fkdVFpAQBfzH3d7xkAQMywpxt+IHQjKpKTk5Wbnauu7q7hfQAdzAHAF4E51fI6d/k9DQCICSrd8AOhG1FTkFcwvEq3YYk5APhnFtVuABMDe7rhB0I3oqZgUsGwK910MAcA/wTm75LXudvvaQBAVCUFpMwUv2eBiYjQjajJycpRIBAY3o2pdAOAv6at9nsGABD1/dzD/dMUGEuEbkQ1dA8boRsAfBVYXCavs9bvaQBA1LCfG34hdCOqx4aZYDB4wNuyvBwA4sDkNX7PAACihv3c8AuhG1GtdKelpnFWNwAkiiXF8jr3+D0LAIgKKt3wC6EbUZOdla3UlNThNVMjdAOA7wJJkpe/1u9pAEBU5Gf4PQNMVIRuRE12ZvbwK93tktfFEnMA8FvgkG3yupr8ngYAjLlJ6X7PABMVoRtRY4Hbqt3DPqubv/EAwHeBZE9eDtVuAOMPlW74hdCNqCrIG8FZ3Q1UugEgHgSWb5bX1eL3NABgzNj53Omc0Q2fELoRVQWTCoZf6W6M9mwAAMMRSPHkZa7zexoAMGaocsNPhG5EVV52njTMArbXSKUbAOJFYMUGed1tfk8DAMbEJEI3fEToRvTP6g4M88ZUugEgbgTSeuSlrfd7GgAwJqh0w0+EbkT9rG7jecOoYrdKXifVbgCIF4Hl6+X1dPg9DQAYNUI3/EToRlRZ9/KU5JRhN1Oj2g0A8SOQ2SUlb/R7GgAwaoRu+InQjfg5q5t93QAQf1asldczzAunABCHkgNSbprfs8BERuhGVOVk54wodKsh2jMCAIxEIKtTStrs9zQAYFRN1ALD7TEERAGhG1GVmZ6p/En5amsfXgdcKt0AEIcOWSsv2O33LADgoExK93sGmOgI3Yi6OdPmqKNzmI142NMNAHEnkGedLrf5PQ0AOCjs54bfCN2IuimFUxT0gsO7cbvktVPtBoC4s3S1vGCP37MAgBEryPR7BpjoCN2IusJJhe7fYHCYwZtqNwDEnUBBsxTc7vc0AGDEpmT5PQNMdIRuRF1hfqEy0jLU3tk+rNuzrxsA4tSi1fKGu3IJAOJAahJ7uuE/QjeiriCvQJkZmWpvH17opoM5AMSnwNRGqXuH39MAgGGbnEXncviP0I2oy0jPcNXutg46mANAwlv4ujyP39MAEgNLyxEPCN2IidnTZw87dLOnGwDiV2B6vdRV7vc0AGBYphK6EQcI3YiJKQVT5GmYlZFOyWuligIAcWvu637PAACGhUo34gGhGzHrYB5QQD3DPW6GajcAxK3AnGp5nRV+TwMAhpSSxBndiA+EbsSE7enOTM9Ue8cwO5g3UOkGgLg2i2o3gPg2OZMmaogPhG7ERGFeoetg3tY+zGZqtYRuAIhngfmV8jp3+z0NABjU1Gy/ZwD0InQjJlJTU92+7mE3U6uJ9owAAKM2bbXfMwCAQbGfG/GC0I2YdjDv6OgY3o3bJW8v1W4AiGeBxWXyOmv9ngYADIjQjXhB6EbMWKVbI9hX49UQugEg7k1e4/cMAGA/NFFDPCF0I7YdzAMB9fQMs4M5S8wBIP4tKZbXucfvWQDAfk3UkmiihjhB6EbMFEwqcB3Mh7uvm0o3AMS/QJLk5a/1exoAsA+aqCGeELoRM/l5+crKyBp2B3M1SV47wRsA4l3gkG3yupr8ngYA9JmR4/cMgDcRuhEzqSmpmjZl2vA7mBuWmANA3Aske/JyqHYDiB8zCd2II4RuxNTsabPV2dU57NuzxBwAEkNg+WZ5Xc1+TwMAXAO1zFS/ZwG8idCNmJpaOFWe57kxHIRuAEgMgRRPXtZ6v6cBACwtR9whdCOmZkydoYz0jOEvMa+XvG6CNwAkgsDyDfK6R7CFCACigKXliDeEbsTUtMnTNClnkppbhrkE0fJ2XbRnBQAYC4G0HnlpVLsB+Gtmrt8zAPZF6EbMm6ktmLNAzW3D3/fHEnMASByBFevkdXf4PQ0AE1RumpST5vcsgH0RuhFzc2fOVU9Pz7BvT+gGgMQRyOiWl7rR72kAmKDYz414ROhGzM2cOtNVvDs6h1kJqZG8IMEbABJFYPlaeT1dfk8DwATE0nLEI0I3Ym76lOnKyc5Rc+swl5h3S2qM9qwAAGMlkNUpJW32exoAJiCaqCEeEboRc1kZWZozfc7wQzdLzAEg8SxbIy9oV00BIDayUqVJGX7PAtgfoRu+WDhnoTq7Oof/ATXRnA0AYKwFctskb6vf0wAwgbCfG/GK0A3fzutOTkpWd8/wqiBeNZVuAEg4S63aPfzGmQAwGiwtR7widMO3ZmrZWdnDX2JuBZNmgjcAJJJAQbMU3O73NABMELPz/J4BMDBCN3yRl5OnaYXT1Nwygn3dlYRuAEg4i1+X5wX9ngWACXA+dz77uRGnCN3wRSAQ0KJ5i9Te0T7sj/EqCN0AkGgCU5qk7h1+TwPAODd3kt8zAAZH6IZvZk2dJQWkYHCYFZAqyesmeANAwllo1W5+fwOInnmEbsQxQjd8baZmx4e1trUO7wMsm1dGe1YAgLEWmF4vdZX5PQ0A41RyQJqV6/csgMERuuGbKQVTVJBXoL2te4f9MSwxB4AENe91v2cAYJyywJ1CqkEc4+kJ3yQlJWnh3IXDr3Rb6N7lsUQRABJQYHaNvM4Kv6cBYBxiPzfiHaEbvpozY448jSBIW9+1+mjPCgAQFbOodgMYe+znRrwjdMP387rT09LpYg4AE0BgfqW8zt1+TwPAODIpXcpL93sWwNAI3fDV9MnT3Zndza0jOK97J6EbABLW9NV+zwDAOMLSciQCQjd8lZqaqoVzFqqppWn4H9QgeS0EbwBIRIFFZfI6a/2eBoBxYm6e3zMADozQDd8tmbfEndU97PO6WWIOAIltMtVuAKNnHctnclQYEgChG75bMHuBcrNztbdlBEeH7SJ0A0DCWlIir3OP37MAkOA4KgyJgqcpfFeYX6jZ02ercW/j8D+oSvK6Cd4AkIgCSZJXsNbvaQBIcCwtR6IgdMN3gUBAKxavUHtX+/CPDrOV6JXRnhkAIFoCS7fK6xpBPw8AiBCQtLDA71kAw0PoRlyYP2u+MtIyODoMACaIQLLk5VLtBnBwpudIWal+zwIYHkI34sKs6bM0tWCqGpoaRrSve9iVcQBA3Aks2yyva/hHRgJA2CKq3EgghG7EhZTkFK1YskLNbSP448uK4nXRnBUAIJoCKZ68rHV+TwNAAi4tJ3QjkRC6EVddzJOTk9XV3TXsj6GLOQAktsDyjfK62/yeBoAEwtJyJBpCN+LG/NnzlZ+bP6Iu5t5OQjcAJLJAWo+8tPV+TwNAAqHKjURD6EbcyMrI0pL5S9TYPIKjwxokr4XgDQCJLLBinbzuDr+nASABsLQciYjQjbiyZN4SBYNBN4bL20HoBoBEFsjolpe6we9pAEgALC1HIiJ0I+72dedm52pvy95hf4xXQugGgEQXWP6GvJ7h9/QAMDFR5UYiInQjrhTmF2r29Nlq2Dv8o8PUJHl1BG8ASGSBrE4pabPf0wAQx1hajkRF6EZcCQQCWrF4hTq6OkZ0BrdXTOgGgIS3bI28YLffswAQp1hajkRF6EbcmT9rvjLSMtTWMfwjZLxST14PwRsAElkgt03ytvo9DQBxiio3EhWhG3Fn1vRZmlowVY1NI+hi3ilpVzRnBQCIiaVW7e7xexYA4gxLy5HICN2IOynJKVqxZIX2tg2/mZoJlgy/4zkAID4FCpql4Ha/pwEgzszMZWk5EhehG3HbxdzCd1f3CDrZVkheO0vMASDhLX5dnseFVABvWjbZ7xkAB4/Qjbg0f/Z85efmq3HvCJaYe717uwEAiS0wpUnqLvF7GgDiRFqytJCl5UhghG7EpayMLC1btGxkR4dxZjcAjB8LV4/oFAsA45ft5U4htSCB8fRF3DpsyWFKSkpSZ5d1SRumeslr4I80AEh0gen1UleZ39MAEAeWT/F7BsDoELoRtxbPW6xphdNU11A3oo/zdhC6AWBcmPe63zMA4LOCDGlatt+zAEaH0I24lZaapqNXHK29LXtHtMTQlph7QYI3ACS6wOwaeZ0Vfk8DgI8OoYEaxgFCN+LasoXLlJmRqdb21uF/UJuk3dGcFQAgZmZT7QYm8tncSwndGAcI3Yhrc2bM0dyZc1W7p3ZEH0dDNQAYHwLzKuV1Vvk9DQA+mDeJs7kxPhC6EdeskdpRy49SR1eHgsHhn9nqlXvyugjeADAuTF/t9wwA+GAZDdQwThC6kRBLzCflTFJj8wjO7O6RvDJCNwCMB4FF5fI6R7biCUBiy0zprXQD4wGhG3Fvcv5kLV2wVPWN9SP6OJaYA8A4MplqNzCR2F7uJNvUDYwDhG4khCOWHiF5Uld31/A/qFrymgneADAuLCmR17nH71kAiBG6lmM8IXQjISxZsESTCyaPvNq9ndANAONBIEnyCtb4PQ0AMWDnchdm+j0LYOwQupEQsjKydMQhR6ipuWlEH+cVefK6Cd4AMB4Elm6T1zmy/w4ASDyHT/N7BsDYInQjYaxYvEJpqWlq67CDuIepQ/J2ELoBYDwIJEte3lq/pwEgiuyIsEUFfs8CGFuEbiSM+bPna+bUmarbUzeij/M2E7oBYLwILNssr6vZ72kAiJIVU2ighvGH0I2EkZKcoqMPPVot7S3yvBEE6SbJqyR4A8B4EEjx5GWt83saAKLAwvahU/2eBTD2CN1IKMsWLFNuVq72tuwd0ccFNwejNicAQGwFlm+U1z2CrUYAEsLiAikz1e9ZAGOP0I2EMn3KdC2au0h1DSNbYq5KyWuk2g0A40EgrUde+nq/pwFgjB0x3e8ZANFB6EZCCQQCOnLZkerp6XFjJLwthG4AGC8Cy9fJ6+7wexoAxsj0bGlKlt+zAKKD0I2Es3TBUuXn5WtP054RfZxX4slrJ3gDwHgQyOiWl7rB72kAGCMcE4bxjNCNhJOXk6fDlhw24tCtnt5zuwEA40Ng+Rvyejr9ngaAUcpOlRZyTBjGMUI3EpJ1MU9PS1dLW8uIPs7b5snrIXgDwHgQyOqUkjb7PQ0Ao2QdyzkmDOMZoRsJacHsBVo8b7Gq66pH9oFtkldG6AaAceP/t3cfwHFe5/3vf+8ueu8AARDsJNh7l0SJVJdVbMs9zbn+TxKnOZM4yU1ukvE4ySRx4uSfxJkUJ7l/O/G911d2ZOdaY/XeKImkSIq9gkTvvezue+6cFwBNUZRELHbxbvl+ZjALLLHvPpRIcH/7nPOcFW/LuGG/qwAQpaAjreSYMKQ4QjeSUiAQ0Na1W+UaVxOhmS0tNCcI3QCQKpxC+27qKb/LABClJWVSTobfVQDxRehG0lq5ZKXqqutm3u3ulUwHwRsAUsbygzLuzE60AJAY1jJADWmA0I2klZWZpW1rt2lkbESRGb7Yck+4casLADC3nJJhyT3rdxkAZmh+kVTOMWFIA4RuJLW1K9aqoqRC3X3dM3vgZckM0e0GgJSx9ICM4Q1VIJlsqPG7AmBuELqR9MeHbVy9Ub39vTJmZiHanCR0A0CqcMoHpPB5v8sAcIOq86V5hX5XAcwNQjeS3oaVG7zwPTA0MKPHmbNGJkTwBoCUsejgjN+ABeCPjfP8rgCYO4RuJL15lfO8oWqdvZ0ze2B4MngDAFKDU90jhZr8LgPAhyjLlRqK/a4CmDuEbiQ9x3G0efVmZWRkaHRsdMbHh5kIwRsAUkbDAb8rAPAh2MuNdEPoRkpY0rBEi+oXqb27fWYPHJHMGUI3AKQKp65TZuKy32UAeB/F2dLiUr+rAOYWoRspIRgMeseHhSNhhcKhGT3WvGNkwgRvAEgZdQf9rgDAB3S5A47fVQBzi9CNlLFy6Upvf3dnzwz3do9J5hShGwBShdPQKjPR5ncZAK5RmCUtK/e7CmDuEbqRMnKzc7V13VYNjQzJdWd2Vqs5ZmQmCN4AkDKq6XYDiYYuN9IVoRspZe3ytSotLlVPf8/MHjjBud0AkEqcxZdkJrr8LgPAlIIsaTldbqQpQjdSSllxmTY0bvBC90zPavUmmY8TvAEgZZTT7QYSxfpqKUjyQJrijz5SzsZVG5WXm6fB4cGZPTAkmeOEbgBIGUvPy0zMcOUTgJjLz5QaK/yuAvAPoRspp76mXo2LGmc+UM12u08amVGCNwCkAicgmdK3/S4DSHuba+lyI73xxx8px3EcbVm7xTtGbHRsdGYPjkwOVQMApAZn2WmZiQG/ywDSVkkOe7kBQjdS0vKFy7Vs4TK1drbO+LHmtJEZIXgDQCpwgpIpotsN+GVbHRPLAUI3UpLtct+0+SYFggENjw7P7MGuZI4SugEgVTiNJ2RCQ36XAaSdqnxpYYnfVQD+I3QjpbvdjYsb1dbZNuPHmrNGZojgDQCpwAkambwjfpcBpGWXGwChGyksEAh43e7MzEwNDc+ww2Ekc4TQDQCpwmk8JhOe4ZwPAFGbXyTVFvpdBZAYCN1IaUsblmr1stVq64qi233ByPQTvAEgFThZEZnso36XAaQNutzATxC6kfKTzHdv2q2c7BwNDA3MvNvN3m4ASBnOyiMy4TG/ywBS3tIyqTzP7yqAxEHoRspbWLdQa5avUXt3u4yZWYg2F41ML8EbAFKBkx2WyTzmdxlASrOTyrfU+l0FkFgI3Uibbndebp76h/pn/Hj3sBuXugAAc89pPCwTmfC7DCBlrayQirL9rgJILIRupIX58+ZrQ+MGdXR3zLjbrWbJdNHtBoBU4ORNSIETfpcBpKTMgLRpnt9VAImH0I206Xbv3LhThfmF6hvom/Hj3bfcmYd1AEBiWvG2jBv2uwog5aytlnIz/a4CSDyEbqSNuuo6bVy1UZ29nTMP0N2SOU/oBoBU4BSOSuaU32UAKSU/U1pf7XcVQGIidCOt7Nq4S8WFxerp75nxY80hIxMieCP5jPSN6Pzr5zXcOzxnz2nf2Oq93DtnzwfM2PKDMm7E7yqAlLG9XsoM+l0FkJgI3Ugr1RXV2rJ2i7r7umfe7R7jCDEkn7f+37f0ewt/T9/6wrf0u/W/6309bahrSL+36PfUdaFrxteNhCL6ytqv6ORzJ72vX/1fr+o3Sn/Du7WOP3lc3Re6Y/g7AWLLKRmW3DN+lwGkhHkFk8eEAbg+QjfSzo71O1RaVOoF75kyJ43MAMEbyWG0f1Tf+eJ39Fsv/Jb+6Mgf6TPf+Iy+9+XvXQncf/+Rv486GD/+F4+r5WjLla+f/ftn9T+++z+8W+vMy2e09KalMfqdAHGy9KCM4YQKYDYcu5Jwvt9VAImN0I20U1Faoe3rt6u7v1uuO8MXW67kHuAFGpLD6MCoPvk3n1T9unrv64ZNDRrqHvI+/5dP/4u2fXZbVNdtP92uJ//ySZUvLL9y33DPsFbcusK77bnUo9L60hj9LoD4ccoHpPB5v8sAktqqSqk8z+8qgMRG6EZa2rpuqypKKryhajPWIpkWut1IfGXzy7T9c9uvLAd/6q+f0saPbvS+/ul/+Wnt/bW9UV33P3/hP3XX796l8gU/Cd05hTleGM8tytUb/9cb2vqZrTH6XQBxtugAp1MAUcrJkLbU+l0FkPgI3UhLZcVl2rlhp/oH+xWJYpCOd4RYhBdpSA6X3r6kL9d8We/8+B196m8/5d1Xsagiqmu9/O8ve8vW7/ytO991vw3ZX133VW38+EaFx8PKKciJSe1AvDnVvVKoye8ygKS0tVbKzvC7CiDxEbqRtuxAtaryKnV0d8z8wYOT+7uBZGCXl//6E7+uqmVV+vYXvh31dQY7B/Xo//6ofubffkaB4Lv/+bj7d+7W17u/7j3H4p2L9Sdb/sRbwk4HEUmh4YDfFQBJpzJPaozu/Vsg7RC6kbbs0WG7N+3W4PCgQqHQjB9vJ5mbYQIFEp/jOFqweYE+/78+r4PfP+gdIRaN737pu9r9v+3W/PXXn5iTW5zrDVdrO9mm5XuWe0eGtR5vnWX1QPw5dZ0yE5f9LgNIKrsb7L8vflcBJAdCN5Tue7sX1S9Sc0fzzB8cZqgaEtup50/pkS8/cuXrYFbQGzPrBKJ7lbT/O/v17N89qy+VfMn7OPPSGX3jI9/Qj//sx96vt7zToto1tRrpGVHt6lpVLq7UcPfcnQ0OzEod3W7gRi0vl6ry/a4CSB7swkBay83O1d6de/XtR7+t4dFh5efO8F+QS5ND1Zxa3upF4qleXq0X//lFb8n3mnvW6Af/xw+06s5V3rCzD5t6npWbpWBm8F33/8n5P3nX19/89De170v7tPru1d7Xtot+9+/eref/8Xl1nuv0ppjnlTDSFsnBaWiTaW6Tk1XjdylAQrPv326r87sKILnQ6UbaW7VkldauWKuWjpao9p+6b7gyYZaZI/EUzyvWLzzyC3rmfz6jr6z+iiZGJvT5b33+Qx9nB6Id+dGR99xfsbDiXR+ZOZkqqinygnUkHPGWl9ugvuHBDXrz/35TGdkZmrd6Xpx+d0Ac1Bz0uwIg4W2eJ+Vl+l0FkFwcw5QbQM3tzfrn/+eflRHM8M7xnilnjaPAOt7DAoBkZ179qJysSr/LABJSea700ZVSlLuUgLRFSgDsVr7qOu8Ise7+bkUiMz9CzBwzMgO8fwUASa+CbjdwPTZn71lI4AaiQegGpuzatEt1VXVq7Yxi2rI7ucwcAJDkllyQmejxuwog4ayrlioY0wFEhdANTCkqKNKt22/V+MS4xsbHZn6Bdsm9QPAGgGTmBCRT+rbfZQAJpThb2lzrdxVA8iJ0A1fZsHKDVixe4e3xjoY5YGQmWGYOAMnMWXZaZqLf7zKAhHHLAimD1ABEjb8+wFUyMzK1b+c+5WTnqG+gb+YXGJPMm4RuAEhmTlAyRXS7AWtVpTSv0O8qgORG6Aausah+kbat36b27vbohqpdMDJNBG8ASGZO40mZ0JDfZQC+ys/kTG4gFgjdwDUcx9GerXtUW10b3VC16bO7RwneAJCsnKCRyTvsdxmAr25eIGUF/a4CSH6EbuA6iguLtXfHXo2HxjU6NjrzC4xL7n6GqgFAMnMaj8mER/wuA/DF0jKpodjvKoDUQOgGPmCo2uplq72hasZE0bVultyzBG8ASFZOliuTfdTvMoA5l5Mh7ZrvdxVA6iB0A+8jI5ihO3bdoYL8AnX3dUd1DfOWkRlimTkAJCtn5VGZcBTHSAJJzAZuG7wBxAahG/gA9TX12rVpl3r6exQOh2d+gbDkvuZG1ykHAPjOyQ7LZB7zuwxgziwonlxaDiB2CN3Ah7hp001qqG1QS0dLdBfokMxJQjcAJCun8bBMZMLvMoC4s91tOzwNQGwRuoEPkZ+X753d7RpXQ8PRHR9j3jYy/QRvAEhGTt6EFDjhdxlA3N2yQMrL9LsKIPUQuoEbsGbZGm1atUnNnc2KuDM/u1sRyX3VlXEJ3gCQlFYclHGj2GYEJInGCmlhid9VAKmJ0A3cgEAgoDtvvlN1VXVqbmuO7iI9knmH0A0AycgpHLd7hfwuA4iL4mymlQPxROgGblBpUanuuvkuuXI1MDQQ1TXMUSPTQ/AGgKS0/JBMNKudgAQWcKS9i6QMUgEQN/z1AmZg7fK12rFuh1q7WhWORLHM0EwtM48QvAEg2Tglw5J7xu8ygJjaUitV5vtdBZDaCN3ADDiOo9t3364FtQt0ue1ydBfpnxysBgBIQssOyhjX7yqAmJhXIK2v9rsKIPURuoEZKswv1D233KNgMKjegd6ormFOGJkOgjcAJBunbEAKn/e7DGDWsoPSbYtsQ8HvSoDUR+gGorBi0Qrt3rRbnT2dCoVDUV3DW2YeIngDQNJZdIBuN5KePY+7IMvvKoD0QOgGolxmftv227SkYYmaWptkTBTheVgyBwjdAJBsnOpeKdzkdxlA1JaXS4tL/a4CSB+EbiBKebl5unfPvcrNzlV3X3dU1zBnjdzzdEsAIOk0HPC7AiAqRRwPBsw5QjcwC4vnL9YtW29RT3+PxifGo7qG2W9keul4A0AycWq7ZCaiHKgJ+Hg82G0Lpayg35UA6YXQDcySDd2Nixt1qe1SdMvMI5L7oiszQfAGgKRSR7cbyWV7nVRd4HcVQPohdAOzlJ2V7S0zt1PNO3o6orvI0NRgtWhCOwDAF05Dm8xEq99lADfE7uFey/FggC8I3UAMzJ83X7ftuE2Dw4MaGx+L7iLNknmH0A0ASaXmoN8VAB+qJEfas8DvKoD0RegGYmTXhl1avWy1mtqinGZu93cfNjKtBG8ASBbOossyE51+lwG8r8yAdMdiKZN93IBvCN1AjGRmZureW+5VeXG52jrbor6O+7IrM0zwBoCkUUG3G4nrlgVSaa7fVQDpjdANxFBNZY1u33W7RsZHNDI6Et1FJqYGq0UI3gCQDJxlF2QmevwuA3iPNVXSkjK/qwBA6AZibMvaLdqwcoMut19WxI1Ed5EeybxJ6AaAZGFK3/a7BOBdagqkHfV+VwHAInQDMZYRzNA9N9+j2qpaXWq9FPV1zFkj96wb09oAAPHhLDstM9HvdxmAJzfDaN+iyXO5AfiP0A3EQXlpue7fe7+yMrPU2RP9gB3zhpHpoeMNAInOCUqmiG43/OfIaN9iR/lZflcCYBqhG4iTxsWN3v7u/qF+DY0MRXcRd2p/9zjBGwASndN4UiYU5c97IEa21TmqLfS7CgBXI3QDcbR7825tW7tNze3NCoVD0V1kWHJfcaM+hgwAMDecoJHJO+x3GUhjC0uk9TV+VwHgWoRuIM77u++99V4tXbBUF1suRh+cWyVzlNANAInOaTwmE47y9ApgFspzjW5b6HcVAK6H0A3EWWF+oR66/SGVFpWqpaMl6uuYI0amheANAInMyXJlso/6XQbScHDaXUsdZQb9rgTA9RC6gTlQX1Ove/fcq1AkpL6Bvqiv477sygwRvAEgkTkrj8qEx/wuA2ki6BjdvdRRAYPTgIRF6AbmyMZVG7Vnyx519HRobDzKF2MhyX3elZkgeANAonKywzKZx/wuA2nB6LZFjirz/a4DwAchdANzxHEc7du1T2tXrFVTa5MibiS6C/VL7guuTITgDQCJyml8WyYy4XcZSHFbax0tLvW7CgAfhtANzKHsrGw9uPdB1VXXecE76sFqHZJ5zTDRHAASlJMXkgLH/S4DKWxpqauN8/yuAsCNIHQDc6y8tFwP7H1AOVk56uztjPo65qKROUToBoCEteKQjBv2uwqkoMrciPYs5GU8kCz42wr4YPmi5bp91+0aHB7U0MhQ1Ncxx43cU25MawMAxIZTOC7ppN9lIMXkBcO6Z3lQQV7FA0mDv66AT3Zv2q1t67apuaNZoXAo6uuYN43MJTreAJCQltlud5QzPIBrBBXRRxozlJPhdyUAZoLQDfgkGAx6x4gtW7BMF5ovzGp/tvuKK9NJ8AaAROOUDEvuGb/LQCowru5aGlBJjt+FAJgpQjfgo4K8Aj2470GVF5erub05+gtFpo4SGyB4A0DCWXZQxrAVCLOzs95VfbHjdxkAokDoBnxWX1Ove2+91ztCrHegN/oLTUjus67MKMEbABKJUzYghc/5XQaS2Jryca2tYU05kKwI3UAC2LByg/Zs26Ou3i4Njw5Hf6HhqY53iOANAAll8QG63YjKooJR7VqY7XcZAGaB0A0kAMdxdPvO27V17VZvmfn4hJ14G6UeyX3ZlXEJ3gCQKJyqPinc5HcZSDLVWcO6fXmu32UAmCVCN5AgMjMz9eDtD2rN8jXeYLVweBZnu7ZI5g1CNwAklIYDfleAJFKkQd2/Jl8O27iBpEfoBhJIXk6ePn7nx7V0wVKdbz4v141+KaI5a+QeYSkjACQKp7ZLZuKy32UgCWRHBvTxDQUKELiBlEDoBhJMSVGJPnH3JzSvct6sjxIzR4zcswRvAEgYdXS78cECoUE9vC5fmUESN5AqCN1AAqquqNbDdz+s4sJiXWq7NKtrmf1GppWl5gCQCJyGNpmJVr/LQKKaGNZDqzKVnxP0uxIAMUToBhLUovpFeuiOh5QRzFBbV1v0FzKS+6Ir003wBoCEUHPQ7wqQgExoVHctiaiiKMfvUgDEGKEbSGBrlq3Rfbfep7GJMfX090R/obDkPkPwBoBE4Cy6LDPR6XcZSCBueFy7qwe1oKrI71IAxAGhG0hw29dv944Ts6F7cHgw+guFCN4AkDAq6HZjkomEta6gU2sWVfldCoA4IXQDSXCG994de3XT5pvU2tmq0bHR6C9G8AaAhOAsuyAzMYsVTEiZwL0087J2ra73uxQAcUToBpJAMBjUvXvu1eY1m9XU1qSJ0ET0FyN4A0BCMGWH/C4BPnIjYTW457R3wwK/SwEQZ4RuIElkZ2Xrodsf0qolq7yjxCKRSPQXI3gDgO+cpWdkJvr9LgM+Be6a8ZO6e/syb0UbgNRG6AaSSEFegT5+18e1sG6hzjefl+vO4gxugjcA+MoJSqbobb/LgA+Bu3zoqO7f1UjgBtIEoRtIMuUl5V7wriqr0sXWizJmFqGZ4A0AvnIaT8qEZjEkE0kXuAt7Duqhm1d7W8cApAdCN5CE6mvq9bE7P6b83Hy1dLTM7mIEbwDwjRM0MnmH/S4DcxS4c9r36+O3rVNmZqbf5QCYQ4RuIEktW7hMD+x9QEZG7d3ts7sYwRsAfOM0HpcJj/hdBuIcuIPNr+rhvRuUk53tdzkA5hihG0hiG1Zu8Kaaj42PqaO7Y3YXI3gDgC+cLFcm+6jfZSCOgdu98KI+sXe9CvLz/C4HgA8I3UASswNYdm3cpftvu1+j46MEbwBIUs7KIzLhMb/LQBwC98TZZ73AXVJc5Hc5AHxC6AZSIXhvIngDQDJzsiMyme/4XQZiHLhHTz6lj9+6TpUVZX6XA8BHhG4gBRC8ASD5OY2HZSITfpeBGIiExjVy/HFvaFrdvGq/ywHgM0I3kCII3gCQ3Jy8kBQ87ncZmKXw+LBG3nlMD+/bqIb6Wr/LAZAACN1AColb8O4geAPAnFh+SCYS8rsKRCk00u8F7k/ctZ3ADeAKQjeQYuIWvJsI3gAQb07huOSc8rsMRGF8oFMj7/xIn7xnN4EbwLsQuoEUFPPg7UruS67cE26sSgQAvJ9lh2TciN9VYAZGe5o1dvzH+tRHbiVwA3gPQjeQomIevCWZA0buAVfG0PUGgHhxSoYl94zfZeAGDXec0/jJJ/WpB/YRuAFcF6EbSGFxCd4njMzLRiZC8AaAuFl2QMawuijRDVw+ptCZ5/Tph+4kcAN4X4RuIMXFJXg3mcl93uMEbwCIB6dsUAqf87sMfIC+8wfkXnxFn/7o3QRuAB+I0A2kgXgEb3VK7pOuzDDBGwDiYjHd7kTVfeIlqeWAPv2xewncAD4UoRtIE3EJ3gOS+4Qr00vwBoBYc6r6ZEJNfpeBq9g3QdoOPa6MnhMEbgA3jNANpJG4BO/RqY53K8EbAGLNWXDA7xIwxUQmdOmVR1QUatPnPvEAgRvADSN0A2kcvMcmxtTa2Tr7aeRhyX3OlXueZZAAEEtObZfMxGW/y0h77viQzj3zf6q2IKyf+sQDqq2p8rskAEmE0A2kcfB+6PaHvK8vtV2affA2knnVyD1K8AaAmKp/y+8K0lpksEOnn/qmlteX6nMP36+K8lK/SwKQZBzDgbtAWjt25pj+64n/Uu9ArxbWL1QwEJz1NZ2ljpwtjpyAE5MaASDdmVfvl5M1z+8y0s5E5xmde/l72rR2pT76kduVn5fnd0kAkhChG4AuNF/QIz9+RC0dLVpYt1CZGZmzv2idFNgdkJNB8AaA2TLn6+X03Ot3GWll9OKbunjwSe3aslEfufs25WRn+10SgCRF6Abgae9u94L36YuntbB2obKzYvDiolwK7AnIySF4A8BsmVcfkpPFXuJ4M25YgyeeUdvpg9qze5vu3nezMjMy/C4LQBIjdAO4on+wX99/4vs6fPKw6qrrlJ+bP/uL5kmBmwNyygneADAb5vRCOQN3+l1GSjOhUXUf+v/U33ZBt9+6S3tv3qFgcPbbrgCkN0I3gHcZGRvRfz/z33r97ddVWVapksKS2V80IG+Pd2ApsxsBYDbMqw/LySrzu4yUZEb71Lr/e5oY6dd9d+zRrm2bvMGjADBbhG4A7xEKh/TES0/o+f3PqyCvwAvfseAsnhqwxj5vAIiKOblEztA+v8tIOW5/s5pe+54yAkYP3r1Pm9avJnADiBlCN4Drcl1XL771oh5/8XEFnIBqq2pj8wKkdGq5eQEvZgBgpkxE0v5Pyckq9ruUlGCMq0jrYZ3d/2OVl5XooXtv16oVS/0uC0CKIXQDeF/2x8PB4wf1w6d/qNGxUTXMa1AgEIMl4llSYFdATi3BGwBmyn2nUYGxW/wuI+mZ8JjGz76g8++8qYUNdfr4/XepvrbG77IApCBCN4APder8KX3/ye+rq6fLO1IsVkNlnLWOnDUOS/gAYAZMxJHe+LSczEK/S0laZqRbg8ceV3PTBa1dtUIfve8OlZYU+V0WgBRF6AZwQy63XfaOFLvYctEL3lmZWbG5cK0U2BmQk03wBoAb5R5ZrcDEbr/LSEpu12l1HX1avX192rl1o+6781bl5nAGN4D4IXQDuGHdvd165PFHdOLcCc2vma/cnNzYXDh/ap93GcEbAG6EmQhIBz8rJyPP71KS6vxt99J+NR9/XRHX1e17dmnPrm3KyOBIMADxRegGMCODw4N69MlHdeDYAc2rnKfC/BgtbwxOHSu2hGPFAOBGuIfWKxDZ7ncZScGMDyp8/nldOHVMBfl5uv/uvdq4diXbmwDMCUI3gBkbnxjXY889ppcPvuwdKVZVVhWzFy7OUkfOZkdOkBdCAPBBzHhQOvQ5ORk5fpeS0Ez/ZY2fe0Hnz19QXW21PnbfnVq8cL7fZQFII4RuAFGJRCJ67dBrevylxzU2MaaGmoaYDVhTuRS4KSAnn+ANAB/EPbhZAXez32Uk7HFgpu2whi8e0KWWNjUuXayP3X+HqirK/S4NQJohdAOYldMXTusHT/9Aze3N3pFiOdkx6rhkTx0rNo/gDQDvx4xkSkc/JycYo+GWKcJMjMhtekW9zWfU1dOrzRvW6MF79nlLywFgrhG6Acxad1+3fvjMD3X4xGFVllWqtKg0Ztd21jlyVjlyAoRvALge89Z2OVrvdxkJw/Q1KXLpdTVfvuQNTLtl51bdcesuZWZm+l0agDRF6AYQExOhCT31ylN64Y0XFAwEVVddF7sBNZVTx4oVELwB4FpmKFs69lk5wfQOlSYSkml5SxPtJ3XxcovKSkp07517tGENA9MA+IvQDSBm7I+TQ8cPeUPWegZ6tKB2gTIzYvQiMENyNjkKLGW6OQBcy7yxS05gjdKVGe7ylpMP9barua1Ty5cs9JaT182r9rs0ACB0A4i9y22XvX3epy+eVl1VnTfhPGZqpcD2gJxcuhYAMM3050mnPiMnEEy/YWkdx+S2HlZHV7eGhke0Y/N63XPHLcrPY/82gMRA6AYQF0MjQ/rRcz/SG4ffUGFBoSpLK2O3vC9LcrY6Ciyg6w0A08z+PXKCK5QuzMSQ3KZXFRlo08XmVuXl5OjO23Zrx5YNsTtNAwBigNANIK7Hir184GVvr7c923v+vPnefu9YcRY4crY4crLpegOA6SmUzn1KjpP6b0i6vRdkLr+h0ZEhXWpu1fy6eXrgnn1auqjB79IA4D0I3QDi7tT5U95085gfK2blSIEdATm1BG8AMK/tlZO5VKnKRCZkLr8p03dBPX393nFgG9Y06v6796mspNjv8gDgugjdAOZEd2+3fvDMD3Tk5BFVlVWppKgkptd3ljjeoDUnk/ANIH2ZjhKp6eGU7HaboXa5Ta95y8ovt7TJvoC9dfc23XbTDmVlpffkdgCJjdANYM7YJeZPvvKkXnzzRWUGM1VbVRvbY1zyp44WqyJ4A0hf5rU75GQuUkp1t1sOyfSc0fj4hJqaW1RRXq6P3LlHa1et4DgwAAmP0A1gTtkfOQePHdSPnv+R+gb6tGDeAmVmxrZD4TQ6ctY7coK8EAOQfkxrhZyWjykVmP5muZf3y4RG1N3bp96+Aa1asVQfufNW1VRX+l0eANwQQjcAX48VO3PxjCpKK1RWXBbbJyie6nqXEbwBpB/z6j1ysuYrWZnwmEzzWzJ9FxUOR9TU3Kqc7Czt2b1Nt+zcynJyAEmF0A3ANyNjI3ru9ee8CeehcEjzq+crIyMjdk/gSM4aR85qR06A8A0gfZhL1XI6HlTSTiZvfkuKjGtwaFgt7Z1a1FCne++4VcsWL/C7PACYMUI3AF/ZH0GnLpzSj1/4sc43n1dNeY2KC2M8gbZMCmwPyCkleANIH+bV++VkzVOyMBPDci+/IQ22yHVdtbR1KBSOaPvmdbrztptUWJDvd4kAEBVCN4CEMDg86J3n/frbr8uRo7rqOgWDwdh2vVc4ctYy4RxAejDn6+X03KtEZ1+Kmu4zMq0HJTessbFxNTW3qaqyTHfddpM2rF2pQCD1prEDSB+EbgAJw/44Onr6qB5/8XFvz3ddVZ0K8gti+yS5UmBzQE4DwRtA6jOvPiQnq0qJyowPyr30ujTc4f0bYM/d7usf9KaS33fnHlVVlPtdIgDMGqEbQMKxU80ff+lxvXX0LWVmTB4tFvMuR+1U+C4kfANIXebMAjn9dynRGDcs03FcpuOYPRNMoXDYG5aWl5uj227arpu2b475yRYA4BdCN4CEZPfzHTp+SE+8/ITau9pVX12vvNy82D5JUJND1lZyvBiA1GVe/bicrMTpGJv+y3Jb3pImhr2v+/oH1N7VoyUL5uu+O2/V4oXJO3UdAK6H0A0goXX1dunHL/7YC+B52XmqqayR48Q4IBdKga0BOTUEbwCpx5xcImdon99lyIwNTIbtwVbv61AorEstbcrKzND2LRu075Ydys+L8ZurAJAACN0AEl44EtabR97Uk688qZ7+HjXUNCgnOyf2TzRfCmwMyCkgfANIHSYiaf8n5WSV+PT8IZn2ozJdJ+26cm/vdk9vv7p7+7RkYYPu3LtbyxYvjP0bqgCQIAjdAJJGW2ebHnvhMb1z+h0V5heqqqwq9i/S7JJzu9x8lSMngxeAAFKDe2yFAqN7/Dlzu+WgFB71vh6fmNDlljbl5eZq9/bNunnnFm8fNwCkMkI3gKQSCoX0yqFX9Oxrz3rHjM2vma/srOzYP1Ge5Gx0FFjAMTUAkp+JONIbn5aTWTg3zzfaK7f5TWm4c/JrY9TZ3av+gUEtX7JId++7SQsb6uekFgDwG6EbQFK61HpJjz3/mE6eP6niwmJVllbGZ2li1dSU81K63gCSm3tktQITu+P6HCY8LtN22Dt3W5p8iWnP3bZ7t4sKC3Tr7m3auXWjsrOz4loHACQSQjeApDU2PqZXDr6iF954Qf1D/d653vm5+bF/Ikdyljpy1jlysgnfAJKTCQWktz4jJzP2PyeNG5HpOSvTdkSKjE/eZ4zaOro0PDKqVSuW6K69N6u+tibmzw0AiY7QDSDptXS06OlXntbhU4eVEchQXXWdgsFg7J8oS5N7vZez3xtAcnIPrVcgsj1m17MvI03fRa+7rYmhK/ePjIzqcmu7ykpLtO+Wndq6aa0yMzJi9rwAkEwI3QBSQiQS0ZFTR/T0q0/rUtslVZZUqrS4ND5LznMkZ40jZwnnewNILmY8KB36nJyM2Q8vMwMtctvelkZ7r9wXDkfU0tahcCSidauW6869N6u6MnHOCAcAPxC6AaQUO1ztxTdf1GuHXtPw6LDqq+vjc7zY9LA1G74XO3IChG8AycE9uFkBd3PUjzfDXXJbD0nDHT+5zxh19/Spp69f8+tqdOtNO7R+9Yr4rDoCgCRD6AaQki62XNRTLz+l42ePe6G7prJGwUCcXvwVSM5aR84CwjeAxGdGM6Ujn5UTnNnJD2asX65dRt5/6V33Dw2PeN3t4qJC7dq2Ubu2bVJBfl6MqwaA5EXoBpCyQuGQDh476B0v1trZqsqySpUWxWnJuVUkBdYFpPmK33MAQAyYt7bL0fob+96JEZn2IzI9565MJLdCobCa29q9z9euXKG9t+xQbU1V3GoGgGRF6AaQ8voH+/XiWy9q/9v7NTwyrNrqWuXlxLELUzoZvp06gjeAxGSGsqVjttud+cHHf3Uck+k6ZQ/6/sn9xqijq0f9g4NaOL9ee2/eodWNSxUIBOaoegBILoRuAGmjqaVJz7z2jN4584631NweMZYRz2m6FVPhu4bwDSDxmDd2yQmsee/94QmZrpMynSckN/SuXxsYHPKOASsrLdbNO7Zo+5YNys2Z2TJ1AEg3hG4AaTnl/NnXn/VCeElhibfsPK7LwaukwPqAnErCN4DEYfrzpFOfkTM178KEx2Q6T3qBW274Xd87MRHyjgDLyAhq49pVuvWmbaqqYCo5ANwIQjeAtDQyOqLX3n5NL735knoHelVTUaOigqL4PmntVOe7jPANIDGY/bdI7gKvq226T78nbNs3Km1ne2R0TEsXL/DO3F6+ZCFzKwBgBgjdANJae3e7nnv9OR06fkihUMibcp6fmx/fJ62XAmsDckp50QrAP2bIkTlQIHM28K4925bruur09m0PecPRbtqxWZvWrVZ2dpZv9QJAsiJ0A0h79sfgmYtn9NKBl3Ti7Anvxea8ynnKzcmN7xPXSIHGgDSPaecA5o7pc2SOZMmcyZDMu3/2eOdt9/app7dfFWWl2rV9o7ZuXMcRYAAwC4RuAJhiw/bJ8yf18oGXder8Ke8+G77tOd9xVSQ5Kxw5ixw5GYRvAPFhOgNyD2dJF+0e7veG7f6BQW8quT1ve9umddq5daNKS+K87QYA0gChGwCuEY6EdeLcCW+/99mmswoGg96e7+ysOE/ozZKcpY6c5Y6cPMI3gNgwl4NyD2dKbdc/rWFwaNjbt52fl6uNa1dq9/bNqq6qmPM6ASBVEboB4H2EwiEdO3NML775oi5cvqDMjEyv852Z+f7n2saEIzkNjpxGR0454RvAzBlXMhcyZGzY7pmcTn6t0dExtbR3eD/TVjcu0y07t2h+3Ty2uwBAjBG6AeBDTIQmdPTUUS98N7U2eR1v2/m2ITzuKqXAioA3fM0J8EIYwAcL9Yd15olmlXVUqyL3+t3q8fEJtbZ3yshoxdJFumXnVm8yOWEbAOKD0A0AN2h8Ytybcv7yWy/rcvtl5eXkqbqiWhnB6y/ZjKn8qX3fix05WbwwBvATxjVSq+SeceVedvX0s28oN6NYN23b/p6wbZeRhyMRLVpQ74XtVSuWeFtoAADxQ+gGkLDs+bDTLwbHxsbU2tqqRYsW+V2WRsZGroTv1s5WFeQWqKq8am5euGZIzpKpfd+FhG8gnZlRI3POyJwx0vBP7j9zrkknjrdo5+ZtKi8p1dj4uNrau+QaVwsb6rVr20ataVwW/60yAAAPoRtAwvrc5z6n9evX67d/+7f1H//xH/ra176mt99+W4lieGRYB44d8Kadt3e1q7igWJVllQoEAnNTgD3vuzEgp4rwDaQT025kThuZS0a6zqu4cDiiZ55/U3mZJaqrqfMmky9eWK9dWzd5nW3CNgDMrTl6ZQgg0X3zm9/U/PnzlZeXp1tvvVXnzp3z7j969Ki2bt2q0tJSffnLX/ZevN2Ir371q6qurlZBQYEeeOABdXV1effbx//FX/yFli1bpoqKCv3yL/+yhoevatFMsc//yCOP6L777vO+/uxnP6tQKKRvfOMbShT5efm6ecvN+uJnv6gH9j6grKwsnWk6o/budq9LH3eXJfcpV5HHInJPuF7XC0BqMuPG+3se+e+I3KddmabrB25rPDSmkvJsjYdHvbD9M596SF/46U9q/ZpGAjcA+IBONwCdPXtWt912mx599FEvCH/lK1/R6dOn9eSTT6qxsVF33XWXF7h/7dd+TQ8//LA+//nPf+D1XnjhBX3xi1/U9773PW/J9a//+q+rvLxc3/rWt7xw/0d/9Ef6r//6LxUXF+unfuqnvOf49re//a5rPPjgg95j/u3f/u3KfU8//bR3/+OPP67du3cr0fQN9OmNI294H129Xd753lVlVfE/amyabXjXSoFFAalOcoJ0wIFkZ7qmuto2ZH/Ae3n25dzg8KA6ejqUlZmlpQuWat2yDdq4eoMyMtizDQB+InQD8DrK3/3ud70P6+WXX9YnPvEJ/cM//IN+/ud/XpcvX/Y64HZpt+1Mv/TSSx94vb/8y79UR0eH19G2/vM//9PrUL/yyiu65ZZbvOD8m7/5m96vPfbYY/r0pz+tgYGBK4+3AfxLX/qS12WfN2/eu679h3/4h/rrv/5rff/739cdd9yhRDQwNKCjp4/qjcNv6HLbZS8M2/BdkFcwd0VkSs4CR84iR04l4RtIJiZkZC5Mhm31fcj3GqPegV519XUpPydfq5at0rZ127S4fvHcbXUBAHygORi5CyDRrVq1Ss8884wOHTrkDSqzYdsGWhuyd+zY4QVua926dTp27NiHXm/16tX6p3/6J/3iL/6it7z8X//1X68EZLvMvKGh4cr32k741QPIXn/9df3SL/2S/vEf/9HrdIfD4feEbrt023bf7RsCf/M3f+M9RyIpKijSro27tGXNFp04d0JvHn1TZy6e8YaulZeUq7SoNP5H84TkDVfyBiwVajJ8L3TkFBDAgUTk9UC6NTkY7YKR3v2j7z1c11V3X7d6Bnq8eRK3bL5FW9ZuUX1NPUd/AUCCodMNwGMDsg3Klg3eNvz+2Z/9mTc1/Op91JWVlTp16pT27dt3Zd/31ewScruH++677/aWgVt2T/jzzz+v3Nxcbzm5ZQejWZ/5zGe8EG277Lbbbfd62266XVZ+8eLF69Zq93nbJe7f+c53vMcl+nE39sVxU0uTDrxzQIdPHVbfYJ/3IrmitGJujhu7WtVUAG9w5GTywhzwm+k2Mhenlo+PfPj3h8Ihb/vKwPCAyorLvDf3Nq/e7B1fCABITIRuANq/f78++tGPeku27f5quyz8iSee0N69e73hZV//+tevfK8dtvbaa695n9tfu1ZVVZW3ZNx2pO2ebrtH3E4ft4Haft3U1KR77rlHJSUlGhwc1JEjR7w94DfffLP3+PPnz3uhf2RkRBkZGaqpqfFq2bBhg/frf/AHf6De3l6vE56MOns6dfjEYa/7bQeuZWdme8eN2f3fcyooOfMnl5+rWnICBHBgrpieq4L28A18vzEaGhlSZ2+ndyZ3VUWVF7Y3rNzgBW8AQGIjdAPQb/zGb3h7//7qr/7K+9r+WLBLu3/nd37H21d99ZAzG5btkDXb8X4/NsDbwP6rv/qr3tf9/f3e42xYtrf2+idOnPDC+Pj4uBeqr8cG9KVLl3qPt11y6+d+7ue8bvjv//7vK5mNjI54+773H96vptYmuRHXO26sML9w7peG5mpy6bntgJcQvoG4Be2mqaA9dGOPCUfC3hLy/sF+5efma0nDEm1avUnLFy1Xbvbkz0QAQOJjTzcAb/nz9JFelu1AT3eaX3311Sv32y60DcllZWUfej07SG1aW1ubdzt9jJYNlUVFRXrqqae84Wrvxw5gs4PXpgO31dLS4k1aT3Z5uXnesKONqzZ6+73fPPKmTp4/qbauNq9zZT/mbAjSqGSOG+9DpZpcel5HAAdmy/ReFbQHb/xxw6PD3qoYG7rtNpS7brpLa5avUV11Hfu1ASAJEboBeEu7f/Znf1abNm3yzta2x3rZZd32iLA///M/17//+797e6j/9E//VLfffvuH7qG21/va176muro6LzDbYWe7du3yuufT/viP/9ibkL5x48brXuPgwYPe89kl71ezk9Tr6+uVKjIzMrVyyUo1Lm70Jp0fPH5Qh44f8s77ttPObffbfs+c6Z0KCm8bKV+T4bvOjl/nCDLgRpi+q4L2Tw5l+FD2Tcme/h5v5oPdbmK72navtv3ZkJ+XH8+SAQBxxvJyAN5ybxuCbdhubW3VmjVrvInjNhD/8Ic/9Iad2fBsO6/PPfecN+38g9huuF06bo8isx30nTt3esHd7tW2zpw5o82bN+udd955T4C2tdjHfeELX/DOC7dHh1l2mbsd4PbJT37SG7BmA32qsi+8j5w64h05Zjvfsg3o4lKVFJb4dwSQfYt23lQIr3Xk5BDAgWmm/6qg3T+zx46OjXpna49PjHunG6xrXKd1y9epobaBI78AIEUQugF8KLs8/K233vKOD7u6Wx0PX/3qV/V3f/d3+tu//Vvv/O5pNsQ/+uij+pVf+RWvA58ORsdHvaXnR08d9Zae2/O/szKzvBfmdn+nr8tMK37SBWcZOtKNCRupUzJtRqZl5kHbbsGxZ2v39vcqMzNT8+fN9waj2VUv9shBAEBqIXQDSCj2XG67p7y0tNTvUhKu+33q/Cm9feJtb/CaHcRml5/bAJ6dle1vcSxDR4rzXir1ToXs1snALXfm1xkbH/MmkNs31EoLS7V2+VqtbVyrRXWLEv7oQwBA9AjdAJBEbIespaNFJ8+d9PZ/22PH7OTzkqISlRaV+v/CnWXoSBFm2HghW3aHh70dj/6NRLtP234EA0HV1dRpy+otWrVslfd3FgCQ+gjdAJCkJkITOnfpnI6fOa6jZ456S1Uzghne5HO7RDUhphzbZejVjpxKZ/LzrASoCbgOEzJS+1Q3u21mQ9CuZaeO9w1MBm3L/p1cuXilF7TtgLQ5HY4IAPAdoRsAUoDd7336wmkdPnlY5y+f1+DwoPJy8rzl57k5CXSeb4kmA3jl5K2TTwiHP4xrpB55y8W9kG1PTZzFKyI7fdzu054O2nb1iQ3aKxav0OL6xUwgB4A0RugGgBRif6R3dHd4g9fs0WN2Kfp4aFzFBcVety3hOmx5klP1kxCu4slz3IFYM5HJgWemaypkt0sKze6aNmh7S8cH+mRkvBMGbMhuXNSoxQ2LvbkLAAAQugEgRdklrk0tTTp+9rjXAe/u6/b2hBfmF3rhwJ4FnHDsewLTXXAbwssZzIYou9gDkume6mb3TA5Ci2b42fWCdv9gv3oHe+UadzJoL1rhnae9eP5i7+8XAABXI3QDQBoYGRvx9n+fazqn4+eOe/u/7bnAdum5DQ22I5eQHWZ7THHZVAivmtoXnp2AdcI33suYwWsCdo9Nx7F7jog7GbRtV9u+cWVXjixbuMwL2naPNsd8AQA+CKEbANJMKBTSpbZLuth8UcfOHlNbZ5uGR4e9IWzFhcVegLCfJ6z8qb3hxVPL0e054UV0xNOFGTLvDdizXCb+fkF7YHDA26dtP7d/N5YuWOqdpb20YSlBGwBwwwjdAJDGpveAX2i+4J0Dfr75vNfRswrzCr2gkZDL0K9l83bBNWG8eCqMBwjjSbtEfFhS32S4vhKwx+P0fMZ4K0LsUEL7JpRll4rbgG2Dtu1o2+FoAADMFKEbAHCFDRwXWy7q/KXz3jL0nv4eTUxMJP4y9A9anl441Q2fDuI2N+UTxhNmuJnNt3Z5+NDUMvHByVvv/ji/QrFbLOykf/vn3nazc7NzVVpcquWLlmtB7QI11DZwljYAYNYI3QCAD12G/s6Zd9Te1f6uZeh2X2swGFRSsmUX/aQr7k1Rz3W8W+/zDAJ5TIP10PsE65H4B+trh6DZkN0/1O9N9c/KyPKWidsBaPajvqZe1RXVib29AgCQdAjdAICZL0O/fF4DwwPeUKnsrGyvA26Xo2dmJtiRZNGyv42864Tx6c9zJSeHYO4tAZ+YWvI99eEF6qFrgrVf9Rmj4ZFh78+qfcPIrtKwS8brquu8QWg2ZNvPbYcbAIB4IXQDAGbMLse93HZZrZ2t3lR0ezs0POQdU2a7hDaEF+QXKCcrJ7mWo8906fp0AM+7JpjnTAX3qz6SYTm7CZl3B+jxqa/Hrvl6+sMG7gR6FWFf0tgl4/bPpw3a9uu83DxVllZ6S8bnz5uv+TXzve52yv65BAAkHEI3AGDWhkaG1NbV5k1Ct8vR7b5wu4zXBiAbbrwQnlegvJw8BQI2raah4HuDuDKmlrLbX7v2I+PdX9v/jl5n2UydN33t7fXuu/bXrrr/el3qWJxjPZfsSgs7/Mx2sUdGRxSKhJSdme0NPLOTxhfWLfRCdmVZZfr+uQMA+I7QDQCIuYnQhLcH3HbAm9uadfbSWe+M49GxUS/w2cFs00E8afeFY84D9uj4qBeubcgOh8Pe1Hr7Ro5dVWG72A3zGrwl47VVtcrKzPK7ZAAAPIRuAMCcDLDq6uvyOuEtHS0623RWXT1dGhod8sKUDUh2T3hubm5qL0nHDbEvTcbGx7xwbT/sUD/jGG/vtbcnu6rOC9dV5VVeF7uspIzhZwCAhEXoBgDMOftPjz0P3HbCbRC3g9maO5q9TrgNW1YwEPRClu2K27PC7cA2wnjqmd6HPb1E3H5uZWdPDuibVznP62JXlVV5Ibu8tFyZGSkysA8AkBYI3QCAhGD35vb09ah3oNc7H7yjq8PrittwbpcVT4cx29GcDuI2lNsuOWE88dmXG3bbgf3/aD/smyv22C57v31DJT833zuuy56NPR2wbRebZeIAgGRH6AYAJCz7T5TtftoQ7n309aitu02t7a3edGobxu3SY7u3NzOYeaUzbm8zMjII4z4G67GJMY2Pj3vB2hvcJuMdKWe3D9j/P3ZJuHcudnm1F7Aryio4ugsAkJII3QCApGP/6bLT0a8O43Zwm+2M20nqtotqjy+bZrul9sMuS776cyZaRx+svU71dNc6NCbHOF6wtv9tbbDOyclRRWmF17UuLS5VcUGxigqLVFxY7O3LZg82ACBdELoBACnDDmWzZzRPh3HvvOahAfX293rL1m3X3AbGUDjk3U7zurDBdwdy7zYz09tbnuodc/tSwP63s29U2P829tZOB7/61t4//d/B/rexS8LtqgIbrG23urSodDJUFxR7wZrJ9AAATCJ0AwDShg3athNuw7e9HR6ZnI5tu+Z9A33qGejR4NDgZDAPTQbziBuZPCPbGK87a4OkDeLvuZ363HbPp+/zI6zbOu3HtcHZnmF95etI2AvZ0/XZ77f12iX59vc4fWv3zduutP0oKijyzr+2gfrqYM1qAQAAPhihGwCAa443mz6qyvsYGb4S0O1Z4/bWLq22+8knJia8AGuDuX3cldupzy0v2JrJW9tRt7zbqX99rw6+0xxNfu+NhPYrj7PfOvVpwAkomBH0grPt4NsQbbvT+Xn5XlAuyi/yPveG0U0NpZseTDf9uf1gCTgAALNH6AYAIEr2n9DppepXd8enP2zwtt1lG8KnO8zTn3u34bAmwhNeSLYB1wk43ue2e2wDt/d5MDB56wTe9evehzP1fVPfb7vV07/mLf++6sg1u2Q+1ZfJAwCQiAjdAAAAAADECRuxAAAAAACIE0I3AAAAAABxQugGAAAAACBOCN0AAAAAAMQJoRsAAAAAgDghdAMAAAAAECeEbgAAAAAA4oTQDQAAAABAnBC6AQAAAACIE0I3AAAAAABxQugGAAAAACBOCN0AAAAAAMQJoRsAAAAAgDghdAMAAAAAECeEbgAAAAAA4oTQDQAAAABAnBC6AQAAAACIE0I3AAAAAABxQugGAAAAACBOCN0AAAAAAMQJoRsAAAAAgDghdAMAAAAAoPj4/wFmP7mKUwm7vgAAAABJRU5ErkJggg=="}, "metadata": {}, "output_type": "display_data"}], "execution_count": 103}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:03:04.486792Z", "start_time": "2025-06-21T02:03:04.328177Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 4. 按班级分组，绘制两个班的平均成绩对比图\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 获取所有班级\n", "all_classes = merged_data['班级'].unique()\n", "class_avg_scores = []\n", "\n", "# 计算每个班级的平均分\n", "for cls in all_classes:\n", "    cls_students = merged_data[merged_data['班级'] == cls]\n", "    cls_avg = cls_students[score_columns].mean().mean()\n", "    class_avg_scores.append({'班级': cls, '平均分': cls_avg})\n", "\n", "# 创建班级平均分数据框\n", "class_avg_df = pd.DataFrame(class_avg_scores)\n", "\n", "# 绘制班级平均分对比条形图\n", "colors = ['#66B2FF' if cls != my_class else '#FF9900' for cls in class_avg_df['班级']]\n", "bars = plt.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "plt.title('不同班级平均成绩对比')\n", "plt.xlabel('班级')\n", "plt.ylabel('平均分')\n", "plt.ylim(0, 100)\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级平均成绩对比图.png')\n", "plt.show()\n"], "id": "94c8694323463428", "outputs": [{"data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 104}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T02:03:09.026530Z", "start_time": "2025-06-21T02:03:07.375378Z"}}, "cell_type": "code", "source": ["# 李洋依202456248 \n", "# 5. 将所有图表整合到一张完整的图纸中\n", "plt.figure(figsize=(20, 15))\n", "gs = GridSpec(3, 2, figure=plt.gcf())\n", "\n", "# 1. 绘制班级成绩Top10的柱状图\n", "ax1 = plt.subplot(gs[0, 0])\n", "\n", "# 确保使用字符串类型的学号作为x轴标签\n", "top_students_ids = [str(int(id)) for id in top_students['学号']]\n", "bars = ax1.bar(top_students_ids, top_students['平均分'], color='skyblue')\n", "\n", "ax1.set_title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "ax1.set_xlabel('学号')\n", "ax1.set_ylabel('平均分')\n", "ax1.set_ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "ax1.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "\n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）\n", "ax2 = plt.subplot(gs[0, 1])\n", "ax2.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "ax2.set_title(f'学号 {my_student_id} 的成绩趋势图')\n", "ax2.set_xlabel('作业序号')\n", "ax2.set_ylabel('分数')\n", "ax2.set_xticks(range(len(score_columns)))\n", "ax2.set_xticklabels([f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "ax2.grid(True)\n", "ax2.set_ylim(0, 120)  # 设置y轴范围\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    ax2.text(i, score + 2, f'{score:.1f}', ha='center')\n", "\n", "# 3. 绘制班级平均分对比条形图（放在左侧）\n", "ax3 = plt.subplot(gs[1, 0])\n", "\n", "# 绘制班级平均分对比条形图\n", "bars = ax3.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "ax3.set_title('不同班级平均成绩对比')\n", "ax3.set_xlabel('班级')\n", "ax3.set_ylabel('平均分')\n", "ax3.set_ylim(0, 100)\n", "ax3.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "# 4. 绘制本班级不同分数段比例图（饼图）（放在右侧）\n", "ax4 = plt.subplot(gs[1, 1])\n", "wedges, texts, autotexts = ax4.pie(\n", "    ordered_counts, \n", "    labels=ordered_ranges, \n", "    autopct='%1.1f%%', \n", "    startangle=90, \n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=True\n", ")\n", "ax4.set_title(f'班级 {my_class} 不同分数段学生比例')\n", "ax4.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "plt.setp(autotexts, size=10, weight=\"bold\")\n", "\n", "# 添加总标题\n", "plt.suptitle('学生成绩分析报告', fontsize=20, fontweight='bold')\n", "\n", "# 调整子图布局\n", "plt.tight_layout(rect=[0, 0, 1, 0.96])  # 为顶部标题留出空间\n", "\n", "# 保存整合后的图表\n", "plt.savefig('学生成绩分析报告.png', dpi=300, bbox_inches='tight')\n", "plt.show() "], "id": "98d40cf209512030", "outputs": [{"data": {"text/plain": ["<Figure size 2000x1500 with 4 Axes>"], "image/png": "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****************************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**************************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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 105}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "255f0a537a69483c"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}