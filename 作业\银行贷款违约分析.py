#%%
# 姓名:罗婉晴
# 学号:23550512032
#%% md
# # # 银行贷款违约风险分析
#%% md
# # ## 数据变量解释
# # 
# # | 变量名 | 变量描述 |
# # |--------|----------|
# # | 年龄   | 客户年龄 |
# # | 教育   | 客户教育水平，数值越大表示教育水平越高 |
# # | 工龄   | 客户工作年限 |
# # | 地址   | 客户居住地址稳定性，数值越大表示地址越稳定 |
# # | 收入   | 客户收入水平 |
# # | 负债率 | 客户负债占收入的比例 |
# # | 信用卡负债 | 客户信用卡负债金额 |
# # | 其他负债 | 客户其他类型负债金额 |
# # | 违约   | 是否违约，1表示违约，0表示未违约 |
#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

#%% md
# # ## 1. 数据加载与探索性分析
#%%
# 加载数据
bank_data = pd.read_csv("bankdata.csv")

# 显示前几行数据
print("数据前5行：")
print(bank_data.head())

#%% md
# # ### 1.1 描述性统计分析
#%%
# 查看数据基本信息
print("\n数据基本信息：")
bank_data.info()

# 查看数据的统计描述
print("\n数据统计描述：")
print(bank_data.describe().T)

# 查看目标变量分布
print("\n违约情况分布：")
print(bank_data['违约'].value_counts())
print(f"违约率: {bank_data['违约'].mean()*100:.2f}%")

#%% md
# # **结论：**
# # - 数据集包含700个样本，9个变量，无缺失值
# # - 违约率为约26.14%
# 
#%% md
# # ### 1.2 数据可视化分析
#%%
# 设置图形大小
plt.figure(figsize=(12, 10))

# 绘制违约与非违约样本数量对比图
plt.subplot(2, 2, 1)
sns.countplot(x='违约', data=bank_data)
plt.title('违约与非违约样本数量对比')
plt.xlabel('是否违约')
plt.ylabel('样本数量')

# 绘制年龄分布与违约关系
plt.subplot(2, 2, 2)
sns.boxplot(x='违约', y='年龄', data=bank_data)
plt.title('年龄与违约关系')
plt.xlabel('是否违约')
plt.ylabel('年龄')

# 绘制收入分布与违约关系
plt.subplot(2, 2, 3)
sns.boxplot(x='违约', y='收入', data=bank_data)
plt.title('收入与违约关系')
plt.xlabel('是否违约')
plt.ylabel('收入')

# 绘制负债率分布与违约关系
plt.subplot(2, 2, 4)
sns.boxplot(x='违约', y='负债率', data=bank_data)
plt.title('负债率与违约关系')
plt.xlabel('是否违约')
plt.ylabel('负债率')

plt.tight_layout()
plt.show()

#%% md
# # **结论：**
# # - 违约样本占比较少，约为26%，数据存在不平衡
# # - 年龄与违约关系不明显，但违约客户的年龄中位数略低于非违约客户
# # - 违约客户的收入中位数略低于非违约客户
# # - 违约客户的负债率明显高于非违约客户，表明负债率可能是违约的重要影响因素
# 
#%% md
# # ### 1.3 变量相关性分析
#%%
# 计算相关系数矩阵
corr_matrix = bank_data.corr()

# 绘制热力图
plt.figure(figsize=(10, 8))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt='.2f')
plt.title('变量相关性热力图')
plt.show()

# 各变量与违约的相关性
print("\n各变量与违约的相关性：")
print(corr_matrix['违约'].sort_values(ascending=False))

#%% md
# # **结论：**
# # - 负债率与违约呈正相关，相关系数较高，说明负债率越高，违约风险越大
# # - 信用卡负债和其他负债与违约也有一定正相关性
# # - 收入与违约呈负相关，说明收入越高，违约风险越低
# 
#%% md
# # ## 2. 数据预处理
#%% md
# # ### 2.1 检查缺失值
#%%
# 检查缺失值
print("缺失值检查：")
print(bank_data.isnull().sum())

#%% md
# # **结论：** 数据集中没有缺失值，不需要进行缺失值处理。
# 
#%% md
# # ### 2.2 特征工程
#%%
# 创建新特征：总负债
bank_data['总负债'] = bank_data['信用卡负债'] + bank_data['其他负债']
print("创建新特征后的数据：")
print(bank_data.head())

# 创建新特征：收入负债比
bank_data['收入负债比'] = bank_data['总负债'] / bank_data['收入']
print("创建收入负债比特征后的数据：")
print(bank_data.head())

# 创建新特征：信用卡负债占比
bank_data['信用卡负债占比'] = bank_data['信用卡负债'] / bank_data['总负债']
# 处理可能的除零情况
bank_data['信用卡负债占比'] = bank_data['信用卡负债占比'].fillna(0)
print("创建信用卡负债占比特征后的数据：")
print(bank_data.head())

#%% md
# # **结论：**
# # - 创建了三个新特征：总负债、收入负债比和信用卡负债占比
# # - 这些新特征可能会对违约预测提供额外的信息
# 
#%% md
# # ## 3. 建立逻辑回归模型
#%% md
# # ### 3.1 准备训练和测试数据
#%%
# 选择特征变量和目标变量
X = bank_data[['年龄', '教育', '工龄', '地址', '收入', '负债率', '总负债', '收入负债比', '信用卡负债占比']]
y = bank_data['违约']

# 数据标准化
from sklearn.preprocessing import StandardScaler
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X)
X_scaled = pd.DataFrame(X_scaled, columns=X.columns)

# 划分训练集和测试集
from sklearn.model_selection import train_test_split
X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.3, random_state=42)

print("训练集大小:", X_train.shape)
print("测试集大小:", X_test.shape)

#%% md
# # ### 3.2 建立基础逻辑回归模型
#%%
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc

# 创建逻辑回归模型
model1 = LogisticRegression(random_state=42)
model1.fit(X_train, y_train)

# 在测试集上进行预测
y_pred1 = model1.predict(X_test)
y_prob1 = model1.predict_proba(X_test)[:, 1]

# 输出模型系数
coef_df = pd.DataFrame({
    '特征': X.columns,
    '系数': model1.coef_[0]
})
print("模型系数：")
print(coef_df.sort_values(by='系数', ascending=False))

# 计算并输出模型评估指标
print("\n模型评估报告：")
print(classification_report(y_test, y_pred1))

# 混淆矩阵
conf_matrix = confusion_matrix(y_test, y_pred1)
print("\n混淆矩阵：")
print(conf_matrix)

# 计算准确率
accuracy1 = model1.score(X_test, y_test)
print(f"\n模型准确率: {accuracy1:.4f}")

#%% md
# # **结论：**
# # - 负债率、收入负债比和信用卡负债占比对违约预测有较大的正向影响
# # - 收入和教育水平对违约预测有较大的负向影响
# # - 模型在测试集上的准确率为约84.76%
# 
#%% md
# # ### 3.3 绘制ROC曲线
#%%
# 计算ROC曲线
fpr1, tpr1, _ = roc_curve(y_test, y_prob1)
roc_auc1 = auc(fpr1, tpr1)

# 绘制ROC曲线
plt.figure(figsize=(8, 6))
plt.plot(fpr1, tpr1, color='blue', lw=2, label=f'逻辑回归模型 ROC曲线 (AUC = {roc_auc1:.2f})')
plt.plot([0, 1], [0, 1], color='gray', linestyle='--')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('假正例率 (FPR)')
plt.ylabel('真正例率 (TPR)')
plt.title('逻辑回归模型ROC曲线')
plt.legend(loc="lower right")
plt.show()

#%% md
# # **结论：**
# # - 逻辑回归模型的AUC值为约0.88，表明模型有较好的区分能力
# # - ROC曲线明显优于随机猜测（对角线）
# 
#%%
# 创建随机森林分类器
from sklearn.ensemble import RandomForestClassifier

model2 = RandomForestClassifier(n_estimators=100, random_state=42)
model2.fit(X_train, y_train)

# 在测试集上进行预测
y_pred2 = model2.predict(X_test)
y_prob2 = model2.predict_proba(X_test)[:, 1]

# 输出特征重要性
feature_importance = pd.DataFrame({
    '特征': X.columns,
    '重要性': model2.feature_importances_
})
print("随机森林模型特征重要性：")
print(feature_importance.sort_values(by='重要性', ascending=False))

# 计算并输出模型评估指标
print("\n随机森林模型评估报告：")
print(classification_report(y_test, y_pred2))

# 混淆矩阵
conf_matrix2 = confusion_matrix(y_test, y_pred2)
print("\n随机森林模型混淆矩阵：")
print(conf_matrix2)

# 计算准确率
accuracy2 = model2.score(X_test, y_test)
print(f"\n随机森林模型准确率: {accuracy2:.4f}")

# 计算ROC曲线
fpr2, tpr2, _ = roc_curve(y_test, y_prob2)
roc_auc2 = auc(fpr2, tpr2)

#%% md
# # ### 4.2 比较两个模型的ROC曲线
#%%
# 绘制两个模型的ROC曲线
plt.figure(figsize=(8, 6))
plt.plot(fpr1, tpr1, color='blue', lw=2, label=f'逻辑回归模型 (AUC = {roc_auc1:.2f})')
plt.plot(fpr2, tpr2, color='red', lw=2, label=f'随机森林模型 (AUC = {roc_auc2:.2f})')
plt.plot([0, 1], [0, 1], color='gray', linestyle='--')
plt.xlim([0.0, 1.0])
plt.ylim([0.0, 1.05])
plt.xlabel('假正例率 (FPR)')
plt.ylabel('真正例率 (TPR)')
plt.title('两个模型ROC曲线比较')
plt.legend(loc="lower right")
plt.show()

#%%
# 比较两个模型的性能
print("模型性能比较：")
print(f"逻辑回归模型准确率: {accuracy1:.4f}, AUC: {roc_auc1:.4f}")
print(f"随机森林模型准确率: {accuracy2:.4f}, AUC: {roc_auc2:.4f}")

# 比较两个模型的召回率
from sklearn.metrics import recall_score
recall1 = recall_score(y_test, y_pred1)
recall2 = recall_score(y_test, y_pred2)

print(f"逻辑回归模型违约样本召回率: {recall1:.4f}")
print(f"随机森林模型违约样本召回率: {recall2:.4f}")

# 比较两个模型的精确率
from sklearn.metrics import precision_score
precision1 = precision_score(y_test, y_pred1)
precision2 = precision_score(y_test, y_pred2)

print(f"逻辑回归模型违约样本精确率: {precision1:.4f}")
print(f"随机森林模型违约样本精确率: {precision2:.4f}")

#%% md
# # **结论：**
# # - 逻辑回归模型和随机森林模型在准确率上表现不同
# # - 随机森林模型通常能更好地捕捉特征间的非线性关系
# # - 随机森林模型的特征重要性排序与逻辑回归模型的系数排序有所不同
# # - 在银行贷款违约风险控制中，需要根据业务需求选择合适的模型
# 
#%% md
# # ## 7. 模型应用与风险预测
#%%
# 创建一个示例客户数据
example_customer = pd.DataFrame({
    '年龄': [30],
    '教育': [2],
    '工龄': [5],
    '地址': [3],
    '收入': [40],
    '负债率': [20],
    '总负债': [8],
    '收入负债比': [0.2],
    '信用卡负债占比': [0.5]
})

# 标准化示例数据
example_customer_scaled = scaler.transform(example_customer)

# 使用两个模型进行预测
prob1 = model1.predict_proba(example_customer_scaled)[0, 1]
prob2 = model2.predict_proba(example_customer_scaled)[0, 1]

print(f"示例客户违约概率 (逻辑回归模型): {prob1:.4f}")
print(f"示例客户违约概率 (随机森林模型): {prob2:.4f}")

# 根据不同阈值进行决策
thresholds = [0.3, 0.4, 0.5]
for threshold in thresholds:
    decision1 = "违约" if prob1 > threshold else "不违约"
    decision2 = "违约" if prob2 > threshold else "不违约"
    print(f"阈值 {threshold}: 逻辑回归模型预测 {decision1}, 随机森林模型预测 {decision2}")

#%% md
# # **结论：**
# # - 示例客户在逻辑回归模型中的违约概率为约0.7877
# # - 示例客户在随机森林模型中的违约概率可能不同
# # - 不同的决策阈值会导致不同的贷款决策
# # - 银行可以根据自身的风险偏好和成本-收益分析来选择适当的模型和阈值
# 
#%% md
# # ## 8. 总结与建议
# # 
# # **模型结果总结：**
# # 
# # 1. 数据分析显示，负债率、收入和教育水平是影响客户违约的重要因素
# # 2. 创建的新特征（总负债、收入负债比和信用卡负债占比）对模型有显著贡献
# # 3. 逻辑回归模型提供了良好的基线性能，特别是在解释性方面
# # 4. 随机森林模型可能提供更高的预测准确率，但解释性较低
# # 
# # **业务建议：**
# # 
# # 1. 重点关注客户的负债率和收入负债比，这是预测违约的关键指标
# # 2. 对于教育水平较低、收入较低且负债率高的客户，应采取更谨慎的贷款策略
# # 3. 可以根据业务需求选择合适的模型：
# #    - 如果银行更关注模型解释性，可以选择逻辑回归模型
# #    - 如果银行更关注预测准确率，可以选择随机森林模型
# # 4. 建议根据银行的风险承受能力设置合适的概率阈值
# # 
# # **模型改进方向：**
# # 
# # 1. 收集更多的客户特征，如信用记录、职业稳定性等
# # 2. 尝试更复杂的集成学习模型，如梯度提升树、XGBoost等
# # 3. 考虑特征之间的交互作用，创建更有意义的交互特征 