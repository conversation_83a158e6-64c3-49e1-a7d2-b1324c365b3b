{"cells": [{"cell_type": "code", "id": "c6a7547e43b0071d", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:55:58.729540Z", "start_time": "2025-06-21T00:55:58.717260Z"}}, "source": ["# 胡灏迪202456242\n", "import pandas as pd\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "# 忽略所有警告信息\n", "warnings.filterwarnings('ignore')\n", "# 设置中文字体，避免中文显示乱码\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  \n", "plt.rcParams['axes.unicode_minus'] = False  \n"], "outputs": [], "execution_count": 6}, {"cell_type": "code", "id": "d9933cbba03ea020", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:00.181366Z", "start_time": "2025-06-21T00:55:59.957623Z"}}, "source": ["# 胡灏迪202456242\n", "# 读取学生信息表\n", "student_info = pd.read_excel('2024审计学、财务管理学生名单.xlsx')\n", "print(\"学生信息表的形状：\", student_info.shape)\n", "print(\"学生信息表的前5行：\")\n", "print(student_info.head())\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学生信息表的形状： (84, 7)\n", "学生信息表的前5行：\n", "       学号/工号   姓名    院系        专业            班级   学校代码      学校\n", "0  202455004   单婧  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n", "1  202455005  丁昱楠  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n", "2  202455008   郭晟  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n", "3  202455017   李优  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n", "4  202455024   潘璠  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n"]}], "execution_count": 7}, {"cell_type": "code", "id": "ec9110d6c5e4639c", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:00.975092Z", "start_time": "2025-06-21T00:56:00.931477Z"}}, "source": ["# 胡灏迪202456242\n", "# 读取作业完成情况表\n", "homework_scores = pd.read_excel('2024审计学、财务管理平时作业成绩统计.xlsx')\n", "print(\"作业完成情况表的形状：\", homework_scores.shape)\n", "print(\"作业完成情况表的前5行：\")\n", "print(homework_scores.head())\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["作业完成情况表的形状： (84, 12)\n", "作业完成情况表的前5行：\n", "       学号/工号            班级  第二章Python程序基本语法课后作业  第三章高级数据类型课后作业  \\\n", "0  202455004  2024审计学（注会班）                 90.0          100.0   \n", "1  202455005  2024审计学（注会班）                 70.5           85.5   \n", "2  202455008  2024审计学（注会班）                 87.0           92.8   \n", "3  202455017  2024审计学（注会班）                 79.5           85.4   \n", "4  202455024  2024审计学（注会班）                 84.0           85.5   \n", "\n", "   第四章Python条件语句课后作业  第五章Python循环语句课后作业  第六章函数课后作业  第七章Pandas数据结构课后作业  \\\n", "0               63.0              100.0       97.0               98.0   \n", "1               64.0               63.0       91.0               70.0   \n", "2               78.0               64.0       73.0               80.0   \n", "3               68.0               83.0       80.0               60.0   \n", "4               73.0               56.0       97.0               80.0   \n", "\n", "   第八章数据的增删改查课后作业  第九章数据清洗课后作业  第十章数据统计分析课后作业  第十一章数据可视化课后作业  \n", "0            77.5         65.0           70.0           50.0  \n", "1            70.5         85.0           90.0           88.0  \n", "2            67.5         85.0            NaN            NaN  \n", "3            70.5         80.0           65.0           75.0  \n", "4            75.5         95.0           85.0          100.0  \n"]}], "execution_count": 8}, {"cell_type": "code", "id": "f64ce64550a3a02", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:01.725502Z", "start_time": "2025-06-21T00:56:01.708788Z"}}, "source": ["# 胡灏迪202456242\n", "# \"学号/工号 班级\"列作为合并键\n", "merged_data = pd.merge(student_info, homework_scores, on=['学号/工号','班级'], how='inner')\n"], "outputs": [], "execution_count": 9}, {"cell_type": "code", "id": "45e0c55b5a6b9a45", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:03.047015Z", "start_time": "2025-06-21T00:56:03.031970Z"}}, "source": ["# 胡灏迪202456242\n", "# 查看合并后的数据\n", "print(\"合并后的数据表的形状：\", merged_data.shape)\n", "print(\"合并后的数据表的前5行：\")\n", "print(merged_data.head())\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["合并后的数据表的形状： (84, 17)\n", "合并后的数据表的前5行：\n", "       学号/工号   姓名    院系        专业            班级   学校代码      学校  \\\n", "0  202455004   单婧  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院   \n", "1  202455005  丁昱楠  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院   \n", "2  202455008   郭晟  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院   \n", "3  202455017   李优  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院   \n", "4  202455024   潘璠  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院   \n", "\n", "   第二章Python程序基本语法课后作业  第三章高级数据类型课后作业  第四章Python条件语句课后作业  第五章Python循环语句课后作业  \\\n", "0                 90.0          100.0               63.0              100.0   \n", "1                 70.5           85.5               64.0               63.0   \n", "2                 87.0           92.8               78.0               64.0   \n", "3                 79.5           85.4               68.0               83.0   \n", "4                 84.0           85.5               73.0               56.0   \n", "\n", "   第六章函数课后作业  第七章Pandas数据结构课后作业  第八章数据的增删改查课后作业  第九章数据清洗课后作业  第十章数据统计分析课后作业  \\\n", "0       97.0               98.0            77.5         65.0           70.0   \n", "1       91.0               70.0            70.5         85.0           90.0   \n", "2       73.0               80.0            67.5         85.0            NaN   \n", "3       80.0               60.0            70.5         80.0           65.0   \n", "4       97.0               80.0            75.5         95.0           85.0   \n", "\n", "   第十一章数据可视化课后作业  \n", "0           50.0  \n", "1           88.0  \n", "2            NaN  \n", "3           75.0  \n", "4          100.0  \n"]}], "execution_count": 10}, {"cell_type": "markdown", "id": "6661e3ab4ebf2c91", "metadata": {}, "source": ["# ## 数据清洗"]}, {"cell_type": "code", "id": "312e46d82e7ec7fa", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:06.053690Z", "start_time": "2025-06-21T00:56:06.045142Z"}}, "source": ["# 胡灏迪202456242\n", "# 检查重复行\n", "duplicate_rows = merged_data.duplicated()\n", "print(\"是否存在重复行：\", duplicate_rows.any())\n", "if duplicate_rows.any():\n", "    print(\"重复行数量：\", duplicate_rows.sum())\n", "    print(\"重复行索引：\", merged_data[duplicate_rows].index.tolist())\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在重复行： False\n"]}], "execution_count": 11}, {"cell_type": "code", "id": "6be95e5e35a5937f", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:09.275676Z", "start_time": "2025-06-21T00:56:09.247941Z"}}, "source": ["# 胡灏迪202456242\n", "# 检查空值\n", "null_values = merged_data.isnull()\n", "print(\"是否存在空值：\", null_values.any().any())\n", "if null_values.any().any():\n", "    print(\"每列的空值数量：\")\n", "    print(null_values.sum())\n", "    \n", "    # 查看哪些行包含空值\n", "    rows_with_nulls = merged_data[merged_data.isnull().any(axis=1)]\n", "    print(\"包含空值的行数：\", len(rows_with_nulls))\n", "    if len(rows_with_nulls) > 0:\n", "        print(\"包含空值的行：\")\n", "        print(rows_with_nulls)\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在空值： True\n", "每列的空值数量：\n", "学号/工号                  0\n", "姓名                     0\n", "院系                     0\n", "专业                     0\n", "班级                     0\n", "学校代码                   0\n", "学校                     0\n", "第二章Python程序基本语法课后作业    2\n", "第三章高级数据类型课后作业          2\n", "第四章Python条件语句课后作业      2\n", "第五章Python循环语句课后作业      2\n", "第六章函数课后作业              6\n", "第七章Pandas数据结构课后作业      3\n", "第八章数据的增删改查课后作业         2\n", "第九章数据清洗课后作业            3\n", "第十章数据统计分析课后作业          2\n", "第十一章数据可视化课后作业          2\n", "dtype: int64\n", "包含空值的行数： 11\n", "包含空值的行：\n", "        学号/工号   姓名    院系                 专业                     班级   学校代码  \\\n", "2   202455008   郭晟  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "9   202455079  闫雨欣  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "14  202455156  王楚涵  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "21  202456057  宋昊辰  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "22  202456091  李欣恬  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "40  202456251  彭佳韵  会计学院  财务管理（正保大数据智能产业学院）  2024财务管理（正保大数据智能产业学院）  10562   \n", "45  202456276   程煜  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "65  202456419   吴晓  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "66  202456450  钱文彬  会计学院  财务管理（正保大数据智能产业学院）  2024财务管理（正保大数据智能产业学院）  10562   \n", "73  202457141  霍佳琪  会计学院  财务管理（正保大数据智能产业学院）  2024财务管理（正保大数据智能产业学院）  10562   \n", "81  202457199  朱思静  会计学院  财务管理（正保大数据智能产业学院）  2024财务管理（正保大数据智能产业学院）  10562   \n", "\n", "        学校  第二章Python程序基本语法课后作业  第三章高级数据类型课后作业  第四章Python条件语句课后作业  \\\n", "2   大连财经学院                 87.0           92.8               78.0   \n", "9   大连财经学院                100.0          100.0               85.0   \n", "14  大连财经学院                 67.0           78.3               80.0   \n", "21  大连财经学院                 65.5           78.1               73.0   \n", "22  大连财经学院                  NaN            NaN                NaN   \n", "40  大连财经学院                 96.0           58.2               70.0   \n", "45  大连财经学院                 71.0          100.0               69.0   \n", "65  大连财经学院                 86.0           78.3               80.0   \n", "66  大连财经学院                  NaN            NaN                NaN   \n", "73  大连财经学院                 19.5           27.3               26.0   \n", "81  大连财经学院                 93.5          100.0               80.0   \n", "\n", "    第五章Python循环语句课后作业  第六章函数课后作业  第七章Pandas数据结构课后作业  第八章数据的增删改查课后作业  \\\n", "2                64.0       73.0               80.0            67.5   \n", "9                89.0       76.0              100.0            92.5   \n", "14               73.0        NaN                NaN            63.0   \n", "21               79.0        NaN                NaN            75.5   \n", "22               90.0       82.0               90.0            72.5   \n", "40               58.0       85.0               80.0            72.5   \n", "45               59.0        NaN               90.0            67.5   \n", "65               67.0        NaN               80.0            65.5   \n", "66                NaN        NaN                NaN             NaN   \n", "73                NaN       97.0              100.0            52.5   \n", "81               73.0        NaN              100.0             NaN   \n", "\n", "    第九章数据清洗课后作业  第十章数据统计分析课后作业  第十一章数据可视化课后作业  \n", "2          85.0            NaN            NaN  \n", "9           NaN           60.0           70.0  \n", "14         80.0           60.0           60.0  \n", "21         90.0           60.0           60.0  \n", "22         90.0           70.0           90.0  \n", "40          NaN           80.0           88.0  \n", "45         75.0           90.0           88.0  \n", "65         80.0           50.0           88.0  \n", "66          NaN            NaN            NaN  \n", "73         40.0           90.0           75.0  \n", "81         90.0          100.0           88.0  \n"]}], "execution_count": 12}, {"cell_type": "code", "id": "a137880c59c99273", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:09.761823Z", "start_time": "2025-06-21T00:56:09.745528Z"}}, "source": ["# 胡灏迪202456242\n", "#空值填充为0分\n", "merged_data = merged_data.fillna(0)\n"], "outputs": [], "execution_count": 13}, {"cell_type": "code", "id": "5c105f9e5d6c578", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:10.650302Z", "start_time": "2025-06-21T00:56:10.632720Z"}}, "source": ["# 胡灏迪202456242\n", "# 验证是否还有空值\n", "print(\"填充后是否还有空值：\", merged_data.isnull().any().any())\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["填充后是否还有空值： False\n"]}], "execution_count": 14}, {"cell_type": "code", "id": "8a3dff612a7a8375", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:11.356207Z", "start_time": "2025-06-21T00:56:11.328460Z"}}, "source": ["# 胡灏迪202456242\n", "# 分数列应该在0-100之间\n", "numeric_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns\n", "for col in numeric_columns:\n", "    if col not in ['学号/工号','学校代码']: \n", "        min_val = merged_data[col].min()\n", "        max_val = merged_data[col].max()\n", "        print(f\"列 '{col}' 的值范围: {min_val} - {max_val}\")\n", "        \n", "        # 检查是否有超出合理范围的值\n", "        if min_val < 0 or max_val > 100:\n", "            print(f\"警告: 列 '{col}' 中存在可能的异常值!\")\n", "            # 显示异常值\n", "            abnormal_values = merged_data[(merged_data[col] < 0) | (merged_data[col] > 100)]\n", "            if len(abnormal_values) > 0:\n", "                print(\"异常值所在行：\")\n", "                print(abnormal_values)\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["列 '第二章Python程序基本语法课后作业' 的值范围: 0.0 - 100.0\n", "列 '第三章高级数据类型课后作业' 的值范围: 0.0 - 100.0\n", "列 '第四章Python条件语句课后作业' 的值范围: 0.0 - 85.0\n", "列 '第五章Python循环语句课后作业' 的值范围: 0.0 - 100.0\n", "列 '第六章函数课后作业' 的值范围: 0.0 - 100.0\n", "列 '第七章Pandas数据结构课后作业' 的值范围: 0.0 - 100.0\n", "列 '第八章数据的增删改查课后作业' 的值范围: 0.0 - 95.5\n", "列 '第九章数据清洗课后作业' 的值范围: 0.0 - 100.0\n", "列 '第十章数据统计分析课后作业' 的值范围: 0.0 - 100.0\n", "列 '第十一章数据可视化课后作业' 的值范围: 0.0 - 100.0\n"]}], "execution_count": 15}, {"cell_type": "code", "id": "a3623665cdf8ddb2", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:14.573583Z", "start_time": "2025-06-21T00:56:14.527694Z"}}, "source": ["# 胡灏迪202456242 \n", "# 保存清洗后的数据\n", "merged_data.to_excel('清洗后的综合数据表.xlsx', index=False)\n", "print(\"数据清洗完成，已保存到'清洗后的综合数据表.xlsx'\") \n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据清洗完成，已保存到'清洗后的综合数据表.xlsx'\n"]}], "execution_count": 16}, {"cell_type": "markdown", "id": "a559d1f82502569d", "metadata": {}, "source": ["# # （二）数据分析\n", "# 利用Pandas库对整合后的数据进行统计分析\n"]}, {"cell_type": "markdown", "id": "e49476216b8667fd", "metadata": {}, "source": ["# ## 1. 个人学习成绩分析\n", "# ### （1）计算本人的作业平均成绩、最高成绩、最低成绩"]}, {"cell_type": "code", "id": "f19d4a5e6be2f10d", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:24.024327Z", "start_time": "2025-06-21T00:56:24.001528Z"}}, "source": ["# 胡灏迪202456242\n", "my_student_id = 202456242\n", "my_data = merged_data[merged_data['学号/工号'] == my_student_id]\n", "\n", "if len(my_data) == 0:\n", "    print(f\"未找到学号为 {my_student_id} 的学生数据\")\n", "else:\n", "    print(f\"学号为 {my_student_id} 的学生信息：\")\n", "    print(my_data[['学号/工号', '姓名']].iloc[0])\n", "    \n", "    print(\"\\n所有列名：\")\n", "    print(merged_data.columns.tolist())\n", "    \n", "    score_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns.tolist()\n", "    score_columns = [col for col in score_columns if col not in ['学号/工号', '学校代码']]\n", "    \n", "    print(\"\\n作业成绩列：\")\n", "    print(score_columns)\n", "    \n", "    my_scores = my_data[score_columns]\n", "    print(\"\\n本人各次作业成绩：\")\n", "    print(my_scores)\n", "    \n", "    # 计算平均成绩、最高成绩、最低成绩\n", "    avg_score = my_scores.mean(axis=1).iloc[0]\n", "    max_score = my_scores.max(axis=1).iloc[0]\n", "    min_score = my_scores.min(axis=1).iloc[0]\n", "    \n", "    print(f\"\\n本人成绩统计：\")\n", "    print(f\"平均成绩: {avg_score:.2f}\")\n", "    print(f\"最高成绩: {max_score:.2f}\")\n", "    print(f\"最低成绩: {min_score:.2f}\")\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学号为 202456242 的学生信息：\n", "学号/工号    202456242\n", "姓名             胡灏迪\n", "Name: 39, dtype: object\n", "\n", "所有列名：\n", "['学号/工号', '姓名', '院系', '专业', '班级', '学校代码', '学校', '第二章Python程序基本语法课后作业', '第三章高级数据类型课后作业', '第四章Python条件语句课后作业', '第五章Python循环语句课后作业', '第六章函数课后作业', '第七章Pandas数据结构课后作业', '第八章数据的增删改查课后作业', '第九章数据清洗课后作业', '第十章数据统计分析课后作业', '第十一章数据可视化课后作业']\n", "\n", "作业成绩列：\n", "['第二章Python程序基本语法课后作业', '第三章高级数据类型课后作业', '第四章Python条件语句课后作业', '第五章Python循环语句课后作业', '第六章函数课后作业', '第七章Pandas数据结构课后作业', '第八章数据的增删改查课后作业', '第九章数据清洗课后作业', '第十章数据统计分析课后作业', '第十一章数据可视化课后作业']\n", "\n", "本人各次作业成绩：\n", "    第二章Python程序基本语法课后作业  第三章高级数据类型课后作业  第四章Python条件语句课后作业  第五章Python循环语句课后作业  \\\n", "39                 62.0           92.8               68.0               69.0   \n", "\n", "    第六章函数课后作业  第七章Pandas数据结构课后作业  第八章数据的增删改查课后作业  第九章数据清洗课后作业  第十章数据统计分析课后作业  \\\n", "39       94.0               70.0            70.0         85.0           70.0   \n", "\n", "    第十一章数据可视化课后作业  \n", "39           98.0  \n", "\n", "本人成绩统计：\n", "平均成绩: 77.88\n", "最高成绩: 98.00\n", "最低成绩: 62.00\n"]}], "execution_count": 17}, {"cell_type": "markdown", "id": "80688b90f159d360", "metadata": {}, "source": ["# ## 2. 班级学习数据分析\n", "# ### （1）分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少"]}, {"cell_type": "code", "id": "fa6a62e79c5640f", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:41.521026Z", "start_time": "2025-06-21T00:56:41.503276Z"}}, "source": ["# 胡灏迪202456242\n", "my_class = my_data['班级'].iloc[0]\n", "print(f\"学生 {my_data['姓名'].iloc[0]} 所在的班级是: {my_class}\")\n", "\n", "# 筛选出同一班级的学生\n", "class_students = merged_data[merged_data['班级'] == my_class]\n", "print(f\"班级 {my_class} 共有 {len(class_students)} 名学生\")\n", "# 计算每个学生的平均分\n", "student_avg_scores = class_students[score_columns].mean(axis=1)\n", "class_students['平均分'] = student_avg_scores\n", "merged_data.loc[class_students.index, '平均分'] = student_avg_scores\n", "\n", "# 查看班级平均分的统计信息\n", "class_avg = student_avg_scores.mean()\n", "class_max = student_avg_scores.max()\n", "class_min = student_avg_scores.min()\n", "\n", "print(\"班级成绩统计：\")\n", "print(f\"班级平均分: {class_avg:.2f}\")\n", "print(f\"班级最高成绩: {class_max:.2f}\")\n", "print(f\"班级最低成绩: {class_min:.2f}\")\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学生 胡灏迪 所在的班级是: 2024审计学（注会班）\n", "班级 2024审计学（注会班） 共有 44 名学生\n", "班级成绩统计：\n", "班级平均分: 80.88\n", "班级最高成绩: 91.05\n", "班级最低成绩: 56.13\n"]}], "execution_count": 18}, {"cell_type": "markdown", "id": "c3cb6fad70938e47", "metadata": {}, "source": ["# ### （2）查找成绩优秀或需要辅导的学生（top10和tail10）"]}, {"cell_type": "code", "id": "669b76a6a9b510c9", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:56:49.819393Z", "start_time": "2025-06-21T00:56:49.796764Z"}}, "source": ["# 胡灏迪202456242\n", "# 按平均分排序，找出班级内成绩最好的前10名学生\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "print(f\"班级 {my_class} 成绩优秀的前{top_n}名学生：\")\n", "print(top_students[['学号/工号', '姓名', '平均分']])\n", "\n", "# 找出班级内成绩最差的后10名学生\n", "bottom_students = class_students.sort_values(by='平均分').head(top_n)\n", "print(f\"\\n班级 {my_class} 需要辅导的后{top_n}名学生：\")\n", "print(bottom_students[['学号/工号', '姓名', '平均分']])\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024审计学（注会班） 成绩优秀的前10名学生：\n", "        学号/工号   姓名    平均分\n", "13  202455092   韩月  91.05\n", "11  202455088  赵曼如  89.43\n", "32  202456152  杨宇婷  89.15\n", "8   202455063  刘添祺  88.92\n", "24  202456096   卢婷  88.70\n", "6   202455053  胡耀天  88.60\n", "29  202456136  刘彦伶  87.55\n", "26  202456104  吴紫睿  87.42\n", "49  202456305  张艺菲  87.25\n", "57  202456364  李昊宇  87.13\n", "\n", "班级 2024审计学（注会班） 需要辅导的后10名学生：\n", "        学号/工号   姓名    平均分\n", "14  202455156  王楚涵  56.13\n", "21  202456057  宋昊辰  58.11\n", "22  202456091  李欣恬  58.45\n", "2   202455008   郭晟  62.73\n", "65  202456419   吴晓  67.48\n", "45  202456276   程煜  70.95\n", "53  202456323  黄思源  72.77\n", "3   202455017   李优  74.64\n", "9   202455079  闫雨欣  77.25\n", "1   202455005  丁昱楠  77.75\n"]}], "execution_count": 19}, {"cell_type": "markdown", "id": "7a40366d939dce65", "metadata": {}, "source": ["# ### （3）分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布"]}, {"cell_type": "code", "id": "1719e542c4be86b9", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:57:05.259793Z", "start_time": "2025-06-21T00:57:05.245626Z"}}, "source": ["# 胡灏迪202456242\n", "# 定义分数段\n", "def get_score_range(score):\n", "    if score < 60:\n", "        return '60分以下'\n", "    elif 60 <= score < 70:\n", "        return '60-69分'\n", "    elif 70 <= score < 80:\n", "        return '70-79分'\n", "    elif 80 <= score < 90:\n", "        return '80-89分'\n", "    else:\n", "        return '90分以上'\n", "\n", "# 应用分数段函数到班级学生\n", "class_students['分数段'] = class_students['平均分'].apply(get_score_range)\n", "merged_data.loc[class_students.index, '分数段'] = class_students['分数段']\n", "\n", "# 统计班级内各分数段的学生人数\n", "score_range_counts = class_students['分数段'].value_counts().sort_index()\n", "print(f\"班级 {my_class} 各分数段学生人数分布：\")\n", "print(score_range_counts)\n", "\n", "# 计算班级内各分数段的百分比\n", "score_range_percentage = score_range_counts / len(class_students) * 100\n", "print(f\"\\n班级 {my_class} 各分数段学生人数百分比：\")\n", "for range_name, percentage in score_range_percentage.items():\n", "    print(f\"{range_name}: {percentage:.2f}%\")\n"], "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024审计学（注会班） 各分数段学生人数分布：\n", "分数段\n", "60-69分     2\n", "60分以下      3\n", "70-79分     8\n", "80-89分    30\n", "90分以上      1\n", "Name: count, dtype: int64\n", "\n", "班级 2024审计学（注会班） 各分数段学生人数百分比：\n", "60-69分: 4.55%\n", "60分以下: 6.82%\n", "70-79分: 18.18%\n", "80-89分: 68.18%\n", "90分以上: 2.27%\n"]}], "execution_count": 20}, {"cell_type": "markdown", "id": "ba7dec94f6471e5", "metadata": {}, "source": ["# ## 3. 数据可视化\n", "# 使用matplotlib库对分析结果进行可视化展示"]}, {"cell_type": "code", "id": "bb448d2c39d4943c", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:58:01.486120Z", "start_time": "2025-06-21T00:58:01.468390Z"}}, "source": ["# 胡灏迪202456242\n", "from matplotlib.gridspec import GridSpec"], "outputs": [], "execution_count": 26}, {"cell_type": "code", "id": "57770321f96d4be5", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:58:02.963693Z", "start_time": "2025-06-21T00:58:02.153258Z"}}, "source": ["# 胡灏迪202456242\n", "# 1. 绘制班级成绩Top10的柱状图\n", "plt.figure(figsize=(12, 6))\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "bars = plt.bar(top_students['姓名'], top_students['平均分'], color='skyblue')\n", "plt.title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "plt.xlabel('学生姓名')\n", "plt.ylabel('平均分')\n", "plt.ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.xticks(rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "            \n", "plt.tight_layout()\n", "plt.savefig('班级成绩Top10柱状图.png')\n", "plt.show()"], "outputs": [{"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 27}, {"cell_type": "code", "id": "55079fd3c855ee9e", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:58:04.111804Z", "start_time": "2025-06-21T00:58:03.812283Z"}}, "source": ["# 胡灏迪202456242\n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）\n", "plt.figure(figsize=(12, 6))\n", "my_scores_values = my_scores.iloc[0].values\n", "plt.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "plt.title(f'学号 {my_student_id} ({my_data[\"姓名\"].iloc[0]}) 的成绩趋势图')\n", "plt.xlabel('作业序号')\n", "plt.ylabel('分数')\n", "plt.xticks(range(len(score_columns)), [f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "plt.grid(True)\n", "plt.ylim(0, 100)  # 设置y轴范围为0-100\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    plt.text(i, score + 2, f'{score:.1f}', ha='center')\n", "    \n", "plt.tight_layout()\n", "plt.savefig('个人成绩趋势图.png')\n", "plt.show()\n"], "outputs": [{"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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*********************************************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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 28}, {"cell_type": "code", "id": "7f2db12c1c49afef", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:58:05.052724Z", "start_time": "2025-06-21T00:58:04.868755Z"}}, "source": ["# 胡灏迪202456242\n", "# 3. 绘制本班级不同分数段比例图（饼图）\n", "plt.figure(figsize=(10, 8))\n", "# 确保分数段是按照正确顺序排列的\n", "ordered_ranges = ['60分以下', '60-69分', '70-79分', '80-89分', '90分以上']\n", "ordered_counts = [score_range_counts.get(range_name, 0) for range_name in ordered_ranges]\n", "\n", "# 定义饼图颜色\n", "colors = ['#FF9999', '#FFCC99', '#FFFF99', '#99FF99', '#99CCFF']\n", "explode = (0.1, 0, 0, 0, 0)  # 突出显示60分以下的部分\n", "\n", "wedges, texts, autotexts = plt.pie(\n", "    ordered_counts, \n", "    labels=ordered_ranges, \n", "    autopct='%1.1f%%', \n", "    startangle=90, \n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=True\n", ")\n", "plt.title(f'班级 {my_class} 不同分数段学生比例')\n", "plt.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "plt.setp(autotexts, size=10, weight=\"bold\")\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级分数段比例饼图.png')\n", "plt.show()\n"], "outputs": [{"data": {"text/plain": ["<Figure size 1000x800 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 29}, {"cell_type": "code", "id": "2737a1a31704509c", "metadata": {"ExecuteTime": {"end_time": "2025-06-21T00:58:06.001789Z", "start_time": "2025-06-21T00:58:05.770689Z"}}, "source": ["# 胡灏迪202456242\n", "# 4. 按班级分组，绘制两个班的平均成绩对比图\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 获取所有班级\n", "all_classes = merged_data['班级'].unique()\n", "class_avg_scores = []\n", "\n", "# 计算每个班级的平均分\n", "for cls in all_classes:\n", "    cls_students = merged_data[merged_data['班级'] == cls]\n", "    cls_avg = cls_students[score_columns].mean().mean()\n", "    class_avg_scores.append({'班级': cls, '平均分': cls_avg})\n", "\n", "# 创建班级平均分数据框\n", "class_avg_df = pd.DataFrame(class_avg_scores)\n", "\n", "# 绘制班级平均分对比条形图\n", "colors = ['#66B2FF' if cls != my_class else '#FF9900' for cls in class_avg_df['班级']]\n", "bars = plt.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "plt.title('不同班级平均成绩对比')\n", "plt.xlabel('班级')\n", "plt.ylabel('平均分')\n", "plt.ylim(0, 100)\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级平均成绩对比图.png')\n", "plt.show()\n"], "outputs": [{"data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 30}, {"cell_type": "code", "id": "32c4486b397053e8", "metadata": {"scrolled": true, "ExecuteTime": {"end_time": "2025-06-21T00:58:08.710785Z", "start_time": "2025-06-21T00:58:06.806879Z"}}, "source": ["# 胡灏迪202456242\n", "# 5. 将所有图表整合到一张完整的图纸中\n", "plt.figure(figsize=(20, 15))\n", "gs = GridSpec(3, 2, figure=plt.gcf())\n", "\n", "# 1. 绘制班级成绩Top10的柱状图\n", "ax1 = plt.subplot(gs[0, 0])\n", "bars = ax1.bar(top_students['姓名'], top_students['平均分'], color='skyblue')\n", "ax1.set_title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "ax1.set_xlabel('学生姓名')\n", "ax1.set_ylabel('平均分')\n", "ax1.set_ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "ax1.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "\n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）\n", "ax2 = plt.subplot(gs[0, 1])\n", "ax2.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "ax2.set_title(f'学号 {my_student_id} ({my_data[\"姓名\"].iloc[0]}) 的成绩趋势图')\n", "ax2.set_xlabel('作业序号')\n", "ax2.set_ylabel('分数')\n", "ax2.set_xticks(range(len(score_columns)))\n", "ax2.set_xticklabels([f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "ax2.grid(True)\n", "ax2.set_ylim(0, 100)  # 设置y轴范围为0-100\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    ax2.text(i, score + 2, f'{score:.1f}', ha='center')\n", "\n", "# 3. 绘制本班级不同分数段比例图（饼图）\n", "ax3 = plt.subplot(gs[1, 0])\n", "wedges, texts, autotexts = ax3.pie(\n", "    ordered_counts, \n", "    labels=ordered_ranges, \n", "    autopct='%1.1f%%', \n", "    startangle=90, \n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=True\n", ")\n", "ax3.set_title(f'班级 {my_class} 不同分数段学生比例')\n", "ax3.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "plt.setp(autotexts, size=10, weight=\"bold\")\n", "\n", "# 4. 按班级分组，绘制两个班的平均成绩对比图\n", "ax4 = plt.subplot(gs[1, 1])\n", "bars = ax4.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "ax4.set_title('不同班级平均成绩对比')\n", "ax4.set_xlabel('班级')\n", "ax4.set_ylabel('平均分')\n", "ax4.set_ylim(0, 100)\n", "ax4.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "\n", "# 添加总标题\n", "plt.suptitle('学生成绩分析报告', fontsize=20, fontweight='bold')\n", "\n", "# 调整子图布局\n", "plt.tight_layout(rect=[0, 0, 1, 0.96])  # 为顶部标题留出空间\n", "\n", "# 保存整合后的图表\n", "plt.savefig('学生成绩分析报告.png', dpi=300, bbox_inches='tight')\n", "plt.show() "], "outputs": [{"data": {"text/plain": ["<Figure size 2000x1500 with 4 Axes>"], "image/png": "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***************************************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**************************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***************************+Xyt+HIEAAAAAAB2iaQ4AASI3G2Zdtlwt7vun+ZIlcvrKlcoO2ygNGuh9Mgn0svzpJP/KXVLkkal120vKpeqa5u/b3uRdNI/pBt/Lo3rI5VU1C31Zs6Tzp0gJcdIG/PqFvM6lB1Y+4HOyPtNq/bNbDRavNqK0Lak41vcr7KyQjW1TWvZ53j672ekwN7LKWvdwzIAEKpuvfVW/eY3rfucBwAAAAAAzZEUB4AAkbt1syKiovzriSE+Stw47SDp5uOlRz+VLpwhFZRJ/7lcivTUbR91p/T+jy2XXd9aJN36jpTw24alnkmkd06QjhwiLc2qW44aopB3TNU/dVTBQ3vcLydmuCo8Sf71bUlTJY+32X7FhUWyXD/9ZdTvGzG4jaIF9rKqRAhXewCA3Vm0aJEefPBBHXDAAU6HAgAAAABA0IpwOgAAQF2Z6rztWfJ6Y0J+PnHrqYbXLpd069S6pSUb7mm5/bdH1y278vtj6/5MiJZ+uEVh5Yzym5UX0U9L4nddTt0kujPjD9HAwv/a69URydKg86WM6U32Ky4plM9q+vzc1ojh7RQ5sOcS6t0TnI4CADrW6tWrNXXqVP3yl79URkaGHnjgAfXq1Uvp6en2Yl5HRHBZDwAAAADAnjBSHAACQHlpscrLihQV3ZAUjw+DkeJoex7V6tfFF6hX+bzd7pcdd5CqXdENDaN+L0uuJvuUlpbIHRXbpC0rilFqcAbzigMINx9++KEOO+wwHXvssXryySf197//XYsXL9bTTz+tSy65RCNHjlRaWpr+/e9/Ox0qAAAAAAABj6Q4AASA4sJ8VVWUK6rxSHGS4thH0SrVVQUnKbl6wy738bmjlBU/vqEheagquvysyT7lJikenehfr1K0dkQxpzicQVIcQLjIysrSL37xC1100UW69957NWPGDP9o8BdffFGff/65Vq5cqZKSEt1333266667nA4ZAAAAAICAR1IcAAJASVG+qip3Soq7SIpj3yVru36bd5wiawp3uU9W/ARZroaSq6X9LmmyvaKiUpHRDXOP56tHO0UL7FkOc4oDCBMJCQmaMGGC1qxZYyfG67nMvDM7OeSQQzR8OFObAAAAAACwJyTFASAAFBXUzR/udjf8s5yg0JxTHB2nh7VKV+aeKJevpsXtVZ4ElXQ7omE9ZZyyKtLs15bPp4rKSkXHNYwUz3P36YCogZYVVzkdAQB0XFL8lltuUXx8fJN2y7JUWNj0YbcDDzxQr7zySgdHCAAAAABA8CEpDgABoLgwz77RWS9S5fK6KhyNCaFhmPW1zso6a5fbC3udpEa/evq+YJj9Z0VFhWpqLcXEN4wUz/FQOh3OKap0OgIA6DivvfaacnNzm7V36tRJvXv31qWXXqqMjAxHYgMAAAAAIBiRFAeAAJC/PUuRUV7/eoyYPBdt5yjPLP0s6/oWt1XHpWtzbTf/+urSdBX5UlVcXChLHsXFJ/u3bY8Y1CHxAi2pqJGqap2OAgDan8/n0z//+U8NHDhQd999t/2gmnl40pRP3759u1566SX79dixYzVr1iynwwUAAAAAICiQFAcAh5mbnHnbs5rMJ+5VuaMxIfScoYc1sfj5FrctrR7YaM2lTb7BKikpVq3cik1M8W/JjmTOUjiL0eIAwoGZTueLL77Q22+/rbfeekujRo3SV199ZX9njIuL0+GHH66nnnpKb775pi644AJ7XwAAAAAAsHskxQHAYeWlxSovLVJUdKy/LUpljsaE0ON2WTq36HKNcX3bbNs2Xyet39Gwbsmt0pJieaLiFRkZ6W/PihrZUeECLSomKQ4gjBxxxBGaO3euzj//fJ100kl64YUXFB0d7d9+/PHH64EHHtAll1yiykr+gQQAAAAAYHdIigOAw4oL81VVWS5v45HiLkaKo+153VX6VfZJ6h+xodm2j9Y2XS8vLZUnOtG/XqkYFUT27YgwgV1ipDiAcOPxeHTrrbfaJdOvuOIKbdy4scl2M7f4kUceqdpa5pcAAAAAAGB3SIoDgMNKiwtUVVmhSG/DyJ8oyqejncRa+bom/3iluAubtC/MlvIrG0aFl1dUKDKmISmer54dGifQEkaKAwhXJ598spYtW6Y+ffo0S5pPnz5dsbENFYcAAAAAAEBzJMUBwGGVFaZUumXPH1mPpDjaU0zpSt1adaoiVeVvsyTNyUn+acVSZVWVouOS/Nvz3L2dCBVogpHiAMLZzglxAAAAAADQehF7sS8AoB2Y0uk785IURzuLy/lMt/W8VLf6ZvjbFuUnqFdEgsorylVTayk2viEpnuPp71CkQIOihuc4ACAsHHfccYqJiWm2mLnFzZ+pqak666yz7D8BAAAAAMCuMVIcABxWVVFuj9JtjJHi6Ahdtryga6Pu8K/XWG4V9vi5SooL5XN5FJfYcIN9W8Rgh6IEGjBSHEC4WbRokU444QRNmjRJI0aMUHp6uhISEmRZlnbs2KF//vOfuvzyy50OEwAAAACAgMdIcQBwWKUZKW41TYt7XSTF0TGGbvyzftmvn2ZWnG+vF3Y/VlFr3pTPcis24ady6pK2Rg53MEqgThkjxQGEmcTERF122WX+9S+++EK5ubk6/fTT7fX33nuPpDgAAAAAAK3ASHEAcFh5abFcjeYTNxgpjo502MZL9DPvF/br2shEFaQdIndUvCIjo/z7ZEWNcjBCoE7tzmU1ACDEuVwu/+vXX39dXq9X1157rTZs2GC3JScn68QTT3QwQgAAAAAAggNJcQBwWHlpkTwRkU3aSIqjI7l8VToz6xQNcy221yv6nyJPdMMo8QrFqjCyl4MRAnVqfU5HAADtz5RG/9Of/qQtW7b42/74xz/qlltuUXx8vO6//36dd9558vl8OvTQQ/Xkk086Gi8AAAAAAMGApDgAOKystFgRzZLiTJyLjuWqKtBv8k9UcsUqKSFd3gFH+bftUA9HYwPq1ZAUBxAGzFzhy5cv16hRo5STk6N///vfmjhxoubOnauRI0fq3HPPtZPjJkkOAAAAAABah6Q4AARA+XSPJ2KnVmoEo+NFVWTqmu1HKrIqTxFDGkqx5rr7OBoXUI/y6QDCQWpqqmbNmqXs7Gw988wzeuyxx3T99ddryZIl/n0effRR5efnOxonAAAAAADBZOcsDACgA9XW1qqyorRZ+XTAKT0js3XRuoP1zOAMlVV3VkL1FuV4BjgdFmBjpDiAcBIVFaXTTjtNhYWFSklJUWRk3ffFsrIyrVixQk899ZTTIQIAAAAAEDRIigOAg6oqy1VbUyPPTzc5gUAwNmmtSredo7dTHlHCji3aFjHE6ZAAG0lxAOHG5XLpsssuU3V1tf3aiI2N1XXXXacJEyaoZ8+eTocIAAAAAEBQICkOAA6qqihXTU21oqJjnQ4FaOJwvaGC8iFa6TlIWyO7Oh0OYKslKQ4gTBx99NF28tskwi3L0i9+8Ysm26uqqvTmm2/qmmuucSxGAAAAAACCCUlxAHBQbW2NLJ9Pbrfb6VCAZk6suEcVnj/r+6hfOx0KYGNOcQDh4swzz1R0dLSdEP/www/tMuqNjRgxQq+++ipJcQAAAAAAWomkOAAAaKZUifrW+yutizpSRZHpTocD2CifDiBcmJLp9S6++GKde+65/jnFjeLiYj3wwANatmyZnSAHAAAAAAC7R1IcAADYMj2j9d/yE7Qi/mSVpo6V5eJrAgIL5dMBhKM5c+YoIqLhMzknJ0edO3fWSy+9pD59+jgaGwAAAAAAwYK73QAAhKkay6MFUSdrdvVxWhtzpHzJg5wOCdgtl8vpCACg4x188MH+15WVlerdu7fKy8t1zjnnOBoXAAAAAADBhKQ4AABhJE89Ncd7nn6Mnqot3oNU7YlzOiSg1aI8TkcAAO2vqqrKToQvWLBAt956q10+vX///vY2U0K98ahxAAAAAADQOlxNAwAQ4pZ7jtQ87zlaFX2U8qL6Sy4yiwhO0XxzBRAGTOJ7/fr12rJli2bPnq2HHnpIV111lf7yl78oPj6+ydziAAAAAACgdbi1CABAiClTouZGnaPF0b/QBu9EVUSkOB0S0CYYKQ4gHLhcLrndbqWnp9tJ8W+++UZXX321Xn31VS1evFiWZTkdIgAAAAAAQYekOAAEoFr7n+dKp8NAENnkHqk53vO1Ivrn2uYdLp+LUWQIPV6S4gDC0OTJkzV//nx9++23SktLs5PmAAAAAABg75AUB4AAVKVoxajU6TAQwKqtSP0QdYoWeE/V2ujDVRLZ3emQgHbn5ZsrgDBRVlam6667rln7rFmzVF5e7t9WW1uriooKPfXUUw5ECQAAAABA8ODWIgAEoCrFOB0CAlCuq5fmRJ2vH6OnaEv0Qapx83uC8EL5dADhZFdl0k17/Tbzp8/n6+DIAAAAAAAIPiTFASAAkRRHvaWeY/R99Fla5T1K+VH9JJfb6ZAAx0TzzRVAmIiNjdUjjzziXzejwfPz89WjRw/NmDGjyTYAAAAAALBn3FoEAKe1MC1klRXTYjtCX4mSNNd7rhZ7p2mjd6IqI5KdDgkIGIwUBxCO3nvvPV199dVKSkqy5xYHAAAAAAB7j6Q4ADgoMsortydCtbU1TdorGSkeVja4D9Qc73nKiD5W273D5HNFOh0SEJC8JMUBhAlTFr26ulqXXHKJXn/9dV1//fX2POIRERFyuXhyEgAAAACAvUVSHAAcFOWNUYRJitc0TYpTPj20VVlR+iHqVC3wnqp10YepJLKb0yEBQcHLN1cAYcAkwysrKxUZGalx48bpD3/4g4YOHep0WAAAAAAABDVuLQKAg6K80fJERqm6qqJJOyPFQ892V197NPhS7xRtiR6jWne00yEBQScuyukIAKD9mWT41q1b7dembHpjVVVV9vziAAAAAABg75AUBwAHmfKXMbHxqigradLOSPHg57OkpZE/1/fes7Q6+kjtiOwrudxOhwUEtVSeJQEQJhITE1tsj4qK0qJFizo8HgAAAAAAgh1JcQBwWGxcgnK3bm7SVmWRFA9GJUrRHO8vtdh7kjZFH6xKT5LTIQEhJYV/GgFAgwcPdjoEAAAAAACCDklxAHBYTFyiamqqm7RVKtaxeLB31rvH2onwjOhjtd07VJaLj1agvZAUBwAAAAAAALAvuHMPAA6LjU+Ur7amSRtzigeuKitK86PO0ILoU7TOe6hKI7s6HRIQFiLcUgJzigMAAAAAAADYByTFAcBh3ug4WTu1lSlRPsslt2vnLXDCdlc/feP9lZZ5T1BW9GjVupnYGOhoydGSy+V0FAAAAAAAAACCEUlxAHCYN7r5qHBLHpUqWQna4UhM4c5nST9GnqDvvWdqdfSRKojsLbncTocFhLUUnkUBAAAAAAAAsI9IigOAw6K8MXKp+fDHEqWSFO9Aheqk77znarF3mjZFj1eVJ9HpkAA0wnziAAAAAAAAAPYVSXEACICkuCxLlmXJ1ag2cLGVqu6utY7GFurWuA/Wd9HnKMN7jHK8Q2S5+FgEAlUqSXEAAAAAAAAA+4i7/wDgsOiYOLk8Hvlqa+WJaPhnuVipjsYViiqtaH3vPUMLvadonXeyyiK7OB0SgL2YUxwAAAAAAAAA9gVJcQBwWHxSqrzeGFVVlismIqHJSHHsv2zXAM3x/krLoo9Xtne0at1ep0MCsA8YKQ4AAAAAAABgX5EUBwCHJSSlKio6RpUVZYqJa5QUV5qjcQWrGsutHyOnaL73DK2OPkKFkb0kl9vpsADspy5xTkcAAAAAAAAAIFiRFAcAh0V5o+3EeN72LU3aS5Qsn+WS22U5FluwKFRnfes9Tz9Gn6RN3vGq9sQ7HRKANhThJikOAAAAAAAAYN8xdA4AAkBa13S7fHpjljwqVbJjMQW6VZ5D9GLs3/SnlOW6qVu23kp9WGtjjyIhDoQgkxD38K0VANrd8ccfrxkzZtivv/zySw0bNkydOnXSww8/7HRoAAAAAADsF24vAkAASEnrptqammbtxWJe8XoVVoxmR12oxxLe1u865+ivXb7V10lXKyd6mCyXx+nwALSjHg0zSwAA2snMmTP1v//9z36dk5OjadOm6ZxzztGcOXPsbZ9//rnTIQIAAAAAsM8onw4AAcCUT3e5XM3ai61UybVW4SrLNVjfes/X8ujjtdU7UrVur9MhAXAASXEAaF/5+fn6/e9/ryFDhtjrJgneo0cP3XrrrfZ31Ntuu03PPvusjjrqKKdDBQAAAABgn5AUB4AAkJCUJpfbY48W90Q0/NNcpM4KJzWWW4sjT9QP3jO0JvpwFUb1djokAAGApDgAtC+TED/llFNUXl43nc/ixYvtBHj9Q5sTJkzQzTffvMv3V1ZW2ku9oqIi+8+amhp7Mdxut734fD57qVffXltbK8uy9tju8XjsuOqP27jdMPu3pj0iIsI+bl17lN3msicwqpZPbvka3S5paPfYiz9Ge88ae1/zqqG91l5qFSlLDQ++mn3Ne3Zu96hGLvlU81McDe3V9qRKtc3aq+yozHGa9ElVsuwztxQ7faJP9Ik+hVmfdvq8MZ8d5jNhV59Dgfn5tPvYA7JPVt3vQ2M+V4Rk1f1NNXDJ5/LIZfns34U9tZt3Wy73Ltvdljm2tcd2+/fZ5ZLbatqn+t/zZrHvqp0+0Sf6RJ/CqE81NYH/+bTzZ9WukBQHgAAQn5QqrzdGlRVlio1P9LfnWT0V6grU1R4N/mP0VG32jmNOcADN9EpyOgIACF2mLPqnn36qZcuW6eqrr/YntYcPH+7fJzExUVlZWbs8xr333qvbb7+9WfvChQsVFxdnv+7cubMGDBig9evX2+XZ66Wnp9vLqlWrVFhY6G/v37+/unTpoqVLl/qT9cbQoUOVnJxsH7vxDZlRo0YpKipK8+fPbxLDuHHjVFVVpSVLljS5iTJ+/Hj7fBkZGVLyDXZ7TG2uRhc/pdyoUVoXO9W/f1L1Og0rfUVZ0ZOVGX2Yv71z5SINKH9f62OOU473wIY+VXyl9IrZWhV3ugoj+zf0qex9dalapKUJF6vc06mhTyWvKLlmnRYm/Va1roZE0KiipxTlK9L8n+Lz96ngQVW5E7Uk8bKGPllVGl/4oAoj+ioj/hx/O32iT/SJPoVtn3JztW7duoY+JSVp2LBh9udZZmZmQ58C+fOpvk8xMRo9enRQ9ClaVRphLWmSXFnoGq9EFWqQ1dCnCsVomWu00pSrPlZDn4qUpNWuYequLHW3GvqU6+qsjRqg3lqvTlZDn7Jd6cpSugZYq+xz1Nvo6q9cddEwa6mi1dCn1a6hKlKyRlsLmyR8lrlGqUpRGmM17dNC1zhF0Sf6RJ/oU5j3aeHCwP98Ki0tVWu4rMbpdQCAI6oqKzT9wWvt18lpXf3t5jnoM9z3KcLVuiedgkWG51DN856tldFHKy9qEHOCA9il2EjpkeOdjgIAQlNFRYV9Y/uRRx7R1KlTdeGFF+rII4/Uhx9+qMmTJ+uaa66x9zM3GqKjo1VdbUbwtW6keK9evZSXl2cn1AN+JN5zcaE/YpI+0Sf6RJ/CrU+X1Do+aq2+PZxGil/+buiPmKRP9Ik+0adw69PjUwL/88lcg6alpdlJ9fpr0JYwUhwAAkCUN1qJSWnK3d7w5JRhLuV2qLs6a7OCWbkVp3nes7TQe4o2RB+i8og0p0MCECR67fp7LABgP9155532E/8mId5Yampqkyf3i4uL7RFhu+L1eu1lZ+bGiVkaq7+xsbP6G+ytbd/5uPvSbm6k1LVXNY3RTvU0bWuc9GnebpI+LcRuJ3da326SO61vt1psNzfMWmqnT/Rpd+30iT6FZJ928Xmzt+3Ofj4FYZ9cJqHSUnvd39TOTALHJHL2t90klFqy6/aW+9Ri7Ltqp0/0iT791E6fQr1PERGB//m0q8+qZu9p1V4AgHaX1jVdWzatbtaea/VUZ1fwJcUzXcM0x3u+lkcfp63eA+Rz7/pGKgDsSm9KpwNAu3n55Zft5LcpPWeUlZXp9ddft19PmjTJv58pS9ezZ+hP6wMAAAAACF0kxQEgQHTqmi5fbfMy6blWuoJBjeXRwqhp+sF7utZ4D1dxVHDEDSCwMZ84ALSfr776qknJueuvv14TJ060y6ib8ueffPKJjjjiCD3wwAM67rjjHI0VAAAAAID9QVIcAAJopLjb7VFtTY08jcp95Clwk8v56mGPBv8xeooyvWNV7ambjxEA2sqAVKcjAIDQlZ7e9HtmfHy8OnXqZC9mnvEpU6bYbWYk+YwZMxyLEwAAAACA/UVSHAACaKR4TFyCysuKFZ+Y4m8vU5LKrATFuooVCFZ4jtA879laGf0z5UUNkHYxTwkA7K+0GKlTrNNRAED4aJz4vvzyy+3R4RkZGTrssMPs5DgAAAAAAMGKpDgABIiE5DQlpXbWjtxtTZLiRp56KlYZjsRVZsXrO+85WuQ9WRuiJ6oigmGbADrG4DSnIwCA8NavXz97AQAAAAAg2JEUB4AA4XK5lN53qLI3rW22LdfqqV6ujkuKb3IfoLne87TC+3Nt9Y6Qzx3VYecGgHqDOzkdAQAAAAAAAIBQQFIcAAJI5+59ZFk+WZZlJ8nr5VntO694jeXRgqiT9YP3dK2NPlzFkT3a9XwA0BpDGCkOAAAAAAAAoA2QFAeAAJtXPDIqWlWVFfJGx/jb89RDPsstt8vXZucyJdm/9f5KS6OnKDP6INW4mbgXQGDNJ57GP0sAAAAAAAAA2gBJcQAIIGldeio2PlHlpUVNkuK1irKT2J21eb+Ov8xzlOZ5z9aq6J8pP6q/5HK3QdQA0PYonQ4AAAAAAACgrZAUB4AA4o2JVZcefbRh9Y9KTuvaZFu2NUCdXXuXFC9TouZEnavF0b/QRu/BqohIaeOIAaB9UDodAAAAAAAAQFshKQ4AAaZH78FavfT7Zu1Z1kCN0hd7fP9G9yjN8Z6njOifa5t3uHyuyHaKFADaz2CS4gAAAAAAAADaCElxAAjAecVdLrd8Pp/c7oby5vnqoQorVtGusib7V1uR+iHqFC3wnqa10YepJLK7A1EDQNvpFMt84gAAAAAAAADaDklxAAgwnbr1UkxcvD2veFxCcqMtLm21+quva6lyXb31bdT5Who9RVuix6jG3TD/OAAEu6HMJw4AAAAAAACgDZEUB4AAk9Kpm1I791TO1k07JcWluZ4z9GTKW9oR1U9yNYwiB4BQcmA3pyMAAAAAAAAAEErIqABAgHG5XBowbIwqy0ubbav0eUmIAwhp0RHSsM5ORwEAAAAAAAAglJBVAYAA1L33ILk9Eaqurmq6obJY3uI1ToUFAO1uVFcpgm+oAAAAAAAAANoQ5dMBIAB1Tx+ghKRUlRTmy+NNUGZ2rrJyCpRbInkPmKPUgwc7HSIAtIsx3Z2OAAAAAAAAAECoISkOAAEoOjZOSV0H6O3/fq6Smij5XF7FxHdW176d1SXVo1ynAwSAdhDlkQ6gdDoAAAAAAACANkZSHAACVFrfMaqN36BBPbqre48e8nq9/m1F1QWqikx2ND4AaGsjOktRfDsFAAAAAAAA0MaYsREAAtSYAw9UvwGDlNqpc5OEuJFYsdGxuACgvRxE6XQAAAAAAAAA7YCkOAAEqC6dUpXevYt2FBY325ZUvl6yLEfiAoD2EOGWRnZ1OgoAAAAAAAAAoYikOAAEKJfLpZFDB6q8olLWTgnwyNoyxVZtdyw2AGhrwzpJMZFORwEAAAAAAAAgFJEUB4AA1r9PT0VHe+3E+M4SzWhxAAgRYyidDgAAAAAAAKCdkBQHgADWq0dXdeucprwdhc22JVRskstX40hcANCWojzMJw4AAAAAAACg/ZAUB4AAFhHh0UEjh6q0rLxZCXWPVaOEykzHYgOAtjKuB6XTAQAAAAAAALQfkuIAEOCGD+6nuNgYFZeWNduWVLbOkZgAtK2SnM0KZ4f1cToCAAAAAAAAAKGMpDgABLj07l3sucVz8wqabYut2qqI2ubJcgD77rsZN+m/d57kX8/4aLpmXtxLz54eq3f/eKSKtrbuYZQV/31aL17QXc+cEql3/3CEyvKz7fbMhR/phV920oLX77bXCzJXKnvZbIWr9ESpf4rTUQAAAAAAAAAIZSTFASDAuVwuu4R6dXW1an2+ptskJZZvcCw2INTkrV+i5R8+oUn/95i9XpS9Vj+8eoeOu+VtnfXPDCV2G6AvHr1wj8fZuvxrzZ95q4669kWd88x6e/qDuc9fb29b8b9ndPhVz2jlR9Pt9fXfvqn+k09XuDq0t9MRAAAAAAAAAAh1JMUBIAgMG9hXyUmJ2lFQ1GxbUvl6R2ICQo3l8+mrf1yqkdOuVWK3/nZb7rqF6jpkojoNOEjxnXtryDEXq2jrmj0eqzBrtQ678imlH3iM4jula8gxF9nHMipL8pXWb7QsWaqpLJPL7ZYn0qtwFOWRJqY7HQUAAAAAAACAUEdSHACCQEpyog4YOqDFpLi3plAxVdsdiQsIJcv/+6TyN/6o+C59teG7d1RbXaWUXsO1Zclnyl23SFWlhfYo8p6jj93jsUwSvO/Ek/3rBVtWKqn7IPt1ZEyCygvq/p9d+9Vr6n/oWQpXY3tIMZFORwEAAAAAAAAg1JEUB4AgMWrYQLuUelV1dbNtKaUrHYkJCBXV5SX64eU/K6Fbf5XkbNSP7zyid24+VAld+9mlzWf9boxmnJOsbRlzNPHih/bq2BXF+Vrx36c07ITL7fUBh56ld/94uHqPnaLibeuV2K2fwtVhlE4HAAAAAAAA0AEiOuIkAID9N7h/b3XplKq8/EJ179qpybaEis2KrClWdUSCY/EBwWz9nFmqrijVmXd/rujETvLV1uiNq0dq6XuPa+O8d3Xyg3OVnD5Ui2Y9oP/ePkUn/3We/ZBKa3zz5JXqOmySeo89wV4feMQ56j1uigoyM1SSs0nv/elou/34W99ThDdG4aJngjQg1ekoAAAAAAAAAIQDRooDQJCIjvbqoJFDVFhc0mybSc0xWhzYd6W5mfbc4SYhbrg9EUrtO0plO7I14LCz1WXIwYqKS9L48+5S0da1ylu/uFXHXfXpC8r68XMdcfVzTdrNsTb98KE8UdH2Oc1i9gsnh/ZxOgIAAAAAAAAA4YKkOAAEkeFD+ism2quS0vJm25LL18ntq3IkLiDYxXVKV01V0/+vSrZv1NJ3HlV5Yd3830Z1ebFqKstk+Wr3eMyc1fP1zdNX6+gbXlVsStcm2yqK8uSNT1FlaYGSew6xl4riPIWLmAjpkHSnowAAAAAAAAAQLkiKA0AQ6derhwb1661tOc2TZ26rRsllaxyJCwh2vcdN1Y7Ny7X8wydVkpuppe/+TXkbFuvIa/+lDXNmacnbj2jNly/ro7tPVkxKN6X1HWW/r6qsSL6a6mbHKy/Yrv/edZJGn3qjOg8cZ89ZbpZ6a76cqYFHnCtvXLKKt2+0F/M6XBzZT4qJdDoKAAAAAAAAAOGCpDgABBG3262JY0fKZ1mqrGqeiLNLqFt7HsEKoKnoxDSdcNsHWvXZC3rt8sH68d3HdMyNr2vQkefpwNNutkeMf/HYhfbI7p//4T9yR9RldN+4ZpQ2zX+/2fHWzH5F5Tu2av7MW/X8WQn+pZ5JpMckdVb3A47Ujk1L7aXHyKMUDqI80jH9nI4CAAAAAAAAQDhxWZZlOR0EAKD1TDL84admantuvnr37NZse1byJBXF9HUkNgDYk6P7SWce4HQUAID2VFRUpKSkJBUWFioxMVEB72mX0xEAANrapdzydsJl7zodAQCgrT11kkLmGpSR4gAQZLxRkZo8frTKyytVW9t8VHhqyQpH4gKAPYlwS8cOcDoKAAAAAAAAAOGGpDgABKHRIwapc6cU5eQVNNsWXbNDMZXbHIkLAHZnYrqUEuN0FAAAAAAAAADCDUlxAAhCSQnxOvigA1RQVKyWZsHoVLLMkbgAYFfcLun4gU5HAQAAAAAAACAckRQHgCA1btQwOzm+o7C42ba4qq2KrdzqSFwA0JKxPaTOcU5HAQAAAAAAACAckRQHgCDVrUuaRo8YrJy8HS1u71y8uMNjAoCWuCSdwChxAAAAAAAAAA4hKQ4AQezgMSMU7Y1SSWlZs20x1XmKr9jsSFwA0NiorlLPRKejAAAAAAAAABCuSIoDQBDr36enBg/oo605ebseLW75OjwuAGg8SvwXQ52OAgAAAAAAAEA4IykOAEHM7Xbr8Ilj5HF7VFJa3my7t6ZIieUbHIkNAIzJvRklDgAAAAAAAMBZJMUBIMgNH9RPBwztr6xtOS1u71yyRC6rtsPjAoDoCEaJAwAAAAAAAHAeSXEACIHR4j+bPN6eW7ywuKTZ9sjaMiWXrXEkNgDh7biBUqLX6SgAAAAAAAAAhDuS4gAQAgb0TdeYA4Yoe1uuLMtqtj2tZKncvmpHYgMQnlJjpGP7Ox0FAAAAAAAAAJAUB4CQ4HK5dOSksUqIj9OOwuJm2yN8lUopzXAkNgDh6ZRhUqTH6SgAAAAAAAAAgKQ4AISM3j27acKY4dqWk7+L0eLLFVHTvLw6ALS1finShJ5ORwEAAAAAAAAAdUiKA0AIOfzgg5SSnKDc/IJm29yqVbei+Y7EBSC8nDHc6QgAAP/f3n2AyVXV/x//zs72vtlssimb3gOppBESIAQCIaGLBmsEFbugqPhHFAsKIhYERUUQRX/SpSodBEIJBJIQAuk9m2x2s73O3P/zOeFuZndnS0Ky9f16nsvu3rkzc+6dydzhfO73HAAAAAAAcBChOAB0I7l9su344ya4UDwcDje5PbV6p6VWbeuQtgHoGab2Nxveq6NbAQAAAAAAAAAHEYoDQDdzwvRJ1ic7y/ILCqPe3rd4ucWEa9u9XQC6v/igZ+eN7ehWAAAAAAAAAEBDhOIA0M1kZ2XY3FlTrLikzGpr65rcHheutN5lqzqkbQC6t7NHB6x3cke3AgAAAAAAAAAaIhQHgG5aLT58yEDbujM/6u1Z5e9ZQm30SnIAOBzDsjybN6yjWwEAAAAAAAAATRGKA0A3lJyUaGecfLwFYwJWUlrW5PaAeZZb/LqZ13TecQA4VMGAZ5+eGLCYQEe3BAAAAAAAAACaIhQHgG7qmDHDbdrk8bYzv8DC4abhd1LtPsus2NAhbQPQvSwaHbDctI5uBQAAAAAAAABERygOAN1UIBCwBSfOtD69e9muPQVRt8kpfcuCocp2bxuA7qN/asgWDO/oVgAAAAAAAABA8wjFAaAby8nOstNOnGHl5VVWVV3T5PagV3tgGHUAOAwx5tlnpwQtyDdKAAAAAAAAAJ0YXZgA0M3NmHKMjRs9zLZu322e5zW5Pa16u2WWr+uQtgHo2k4faZaX0dGtAAAAAAAAAICWEYoDQDcXHxdnC0853pKTE61wf0nUbfqUvGnxdcXt3jYAXVff5JCdOSrQ0c0AAAAAAAAAgFYRigNADzB88ECbM2OS7SkosrpQqMntMRay/kUvW8BrehsANBYwz5ZOCVos3yQBAAAAAAAAdAF0ZQJADzHvhGk2eGCubduRH/X2xLoi6126st3bBaDrOXuMZ0OzOroVAAAAAAAAANA2hOIA0EOkp6bY4lPnWExMwIqaGUa9V/m7lly9u93bBqDrGJ1VY6eP4CskAAAAAAAAgK6DHk0A6EGOHTvCTjp+quUXFFpNTW2T2zU7cL/9yywmXN0h7QPQuaUEa+wL0+MtwFTiAAAAAAAAALoQQnEA6EECgYAtOGmWjRkxxDZv32We5zXZJi5caf32v9oh7QPQeQW8sH1petBS4ju6JQAAAAAAAABwaAjFAaCHSUpMsPMWnmwZaam2e8++qNukVW+3jIr17d42AJ3XgiGVNqJ3sKObAQAAAAAAAACHjFAcAHqgwQP72Rnzjrey8gorr6iMuk3f4jcssSZ6aA6gZxmSVGrnTkjp6GYAAAAAAAAAwGEhFAeAHmr2tIk2bfJ427Yz30LhcJPbYyxkA4pesGAoemgOoGdIskr72tzUjm4GAAAAAAAAABw2QnEA6KFiY4N21mlzLa9/X9u2Y3fUbTS/+MCiFyzghdq9fQA6nv7tH5hHPNDRTQEAHCX//ve/bdiwYRYbG2uTJk2yd999161fvXq1TZs2zbKysuyKK64wz/M6uqkAAAAAABw2QnEA6MGyszJcMB4IBKxof0nUbZJq91nf4tfavW0AOt6ZQyttVN/4jm4GAOAo2bBhgy1dutR+/vOf244dO2zUqFF2ySWXWHV1tS1evNimTp1qy5cvtzVr1tgdd9zR0c0FAAAAAOCwEYoDQA937NgRduKsqZZfUGhV1TVRt8ms3GRZZWvbvW0AOs4x6UW2+FiGTQeA7kxV4QrEL7zwQuvbt6998YtftBUrVtjjjz9uxcXFduONN9rw4cPt2muvtdtuu62jmwsAAAAAwGGLPfy7AgC6A1WJn3HyLNu9d5+9tfo9GzE0z2KDwSbb9SldYTWx6Vae2L9D2gmg/eQE9tmX5vTq6GYAAI6yRYsWNfj7vffes5EjR9rbb79tM2fOtOTkZLd+woQJrlq8Oaos1+IrKTkwAlFdXZ1bJCYmxi3hcNgtPn99KBRqMER7c+uDwaD7/uo/buR60fZtWa/h4vW4B9YfGBUlYJ4FrdbCFmPhiO6Sg+uDbqlvo9uyzm2r3w6uD7klZHHm2cEpSLSt7tN4fdDqLGBhq/ugHQfX15qZZ6Em63Uha8A9ToN9shrz3DNHazv7xD6xT+xTD9unRucbnTt0TmjuPNQ5z08tt71T7pN34P0QKRyINfMOvFIHBSwcCFrAC7v3QmvrdW8vENPs+hg37Z/X6nr3fg4ELMZruE/++7xJ25tbzz6xT+wT+9SD9qmurvOfnxqfq5pDKA4AsMTEBLtw8XwrLim1TVt32Ighee7EEkn/Y9l//0u2pfcCF44D6J4S64rtW6elWTCGecQBoCepqamxX/7yl3b55Zfb+vXrbejQoU06NYqKitwc44397Gc/s2uuuabJelWdp6SkuN9zcnJc1fmmTZts79699dsMHDjQLe+//76rTvdpnvM+ffq4uc0rKyvr148ZM8YyMzPdY0d2yCi4j4+Pd8O9RzruuOPcvq1cubJ+nfZF86Xr+dauXWuWeYVbnxQqsImlt1pB/ATbmHxm/fYZtRttbPk/bWfibNueOKd+fU71Wza88lHblLTA9iZMOrhPVf+zgVUv2PspF1hx3LCD+1TxqPWpectWp33WKoO9D+5T2T8ts26jrcj4uoUCB4OgCSW3Wny4xJZ/0L76fdr/C6uJSbeV6V84uE9ejU0r/oUVxw6xtalL6tezT+wT+8Q+9dh9KiiwjRs3HtynjAwbO3as7dy507Zv335wnzrz+cnfp6QkmzhxYpfYp0SrsfHeygbhyorANEu3YhvpHdynKkuydwITLdsKbLB3cJ9KLMPWBcZaP9tp/byD+1QQyLEtNtwG2Sbr7R3cp12BgbbTBtpw7333HL4tgWFWYH1srLfaEu3gPq0LjLESy7SJ3ooGgc87gQlWY/E22Wu4TysCx1k8+8Q+sU/sUw/fpxUrOv/5qby83Noi4EXG6wCAHm3jlh3253886IZRz+vfN+o21cF029L7NAvHMM8w0N3E1FXYZdNrbNSAzI5uCgCgnV155ZVu2PTXX3/drrrqKqutrXXDp/vy8vLslVdesQEDBrSpUlzb79u3z9LT0zt/Jd5fUrp/xST7xD6xT+xTT9unS0IdXrXmr+9JleKXPtz9KybZJ/aJfWKfeto+3bSw85+f9P+g2dnZLlT3/x80GkJxAEADy99eY3+793FLTUmy3r2iB2Nl8f1se6+5ZoGmw6wD6KJCNfbRIfk2b1JeR7cEANDOnnnmGTvnnHNc6D1u3Di77rrr3NX4f/vb3+q30ZX469atc1f3t0YdEqoOaK1DotP4I6OjAEC383m6vDvCFx7u6BYAAI60Wxdbp9fW/wc9eEkfAABmNnXCWFtw0kwr3F9i5RUHhyWJlFqzy/rvX2bmRV7VBqDLCofspKwtBOIA0ANp6LolS5bYzTff7AJx0dB4y5Yta7CNKsF79erVgS0FAAAAAODwEYoDABrQ0COnnjjDZk091rbu2G01tRq+rKn0qq2WW/x6u7cPwJF3THCdfXTO8I5uBgCgnWletkWLFtnZZ59t5557rpWVlbllzpw57kr722+/3W137bXX2vz58+uHSwUAAAAAoKs5OOEKAAAfiIuNtXPPONlVi7+7bpONGjbIzd/RWGblBgvFxNne9Ckd0k4AH97AmrX2hbOGR/03DgDo3p544glbs2aNW/70pz81qAz/85//7CrIr7jiCneOeO655zq0rQAAAAAAfBiE4gCAqNJSk+2jZ51qf7zrAdu0dacNGzzAVZE3ll2+1sKBeNuXdkyHtBPA4csuX2PfWDTE4uPirDsKhUL1VY1VVVW2a9cuGzp0aEc3CwA6DVWIe170OVeHDBliGzZssDfeeMNmzpxp2dnZ7d4+AAAAAACOFEqCAADN6te3twvGU1OS3VDqzXWa5pSttKzy99q9fQAOX3rxKvvG/H7uApju6lOf+pRdf/317vd7773XzjnnnI5uEgB0Kbm5uXbmmWcSiAMAAAAAujxCcQBAi8aMGGIfO+c0N6T6zt17m92uT8kbll6xsV3bBuDwJO972756co716Z11xB5Tw+zm5eVZcnKynXTSSbZx44HPg9WrV9u0adMsKyvLDcHb3MU1jf34xz+2vn37Wmpqqp111llWUFDg1uv+CrpHjhxpvXv3ti9/+ctWXl7e5P56fgXhCnPkoosustraWrv55puP2D4DAAAAAAAA6BoIxQEArZo0fpRdsOgUC4XDtnvPvqjbaGD1fsWvWmrVtnZvH4C2S9izwj53fLoNGpB7xB5Tw+v+6Ec/sn//+9+2du1aGz58uH3mM5+x6upqW7x4sU2dOtWWL1/u5qy94447Wn28F154wf71r3+5n2+99ZYbBv3yyy93t9122232m9/8xu666y576aWX7LXXXrNLL720yWNcdtll9vGPf9zGjx/v/tZ8uDfddJN95zvfcfcDAAAAAAAA0HMQigMA2mT65PF27hknW2VVte3dVxR1m4B5NqDoJUup2tnu7QPQutj8N+0TU+Jt3KgjO6/2ihUr3HyzU6ZMsUGDBtlnP/tZW79+vT3++ONWXFxsN954owvKr732Whdqt0ZB98KFC2306NE2YsQIV+Wtx5M777zTBeTTp093t19zzTUujI/0t7/9zV588UX76U9/2mD9Kaec4u57+umn25NPPnlEjwEAAAAAAACAzotQHADQJoFAwObMmGSLTj3BSkrLrbCoOPp2FraBRS9YauXWdm8jgObF5q+wj08M2rRJ4474Y48bN86eeeYZV9WtEPyWW26xU0891d5++20XlmtIdZkwYYKrFm+NqrsfeOABNwT6nj17XJCuxxMNo67g3RcMBt3ie/XVV+2LX/yiqybXHLh1dXUNlquvvtq+9rWv2YIFC+ySSy6xsrKyI348AAAAAAAAAHQuhOIAgEMKxufPmW6nnzzL9hUV2/6S0ujbWdgG7H/J0is2tXsbATQVu+ctu2hCjM06boL7d3w0QvELLrjAJk+ebJmZmbZs2TK74YYbrKSkxIYOPViVrudWgF1UVOSqyrVt4+Whhx6yM844w1WWa9G84gquv/vd77rH0P0iK8M1HLsfmOv5NP/4t7/9bbvqqqssISHB4uLimiwK6++55x7XjqSkpCN+PAAAAAAAAAB0LrEd3QAAQNeieXnPmHe8VdfU2pMvvGrBmBhLS02JOpR6v+JlFuPV2f6UkR3SVgBmwfwVtmRCwI6fdnQCcX+484cfftheeeUVGzNmjF1//fVu+PN58+a5YDpSYmKiVVRUuO1ra2ubPFafPn3s3nvvta1bt7qq8t69e7uQ+xOf+ITdd999bgh2heazZ8+20tJSW7VqlZt7XNLT010bFMR/61vfstjYWMvNzbUnnnjCJk2a5Lb5/ve/78Lw888/3y0AAAAAAAAAuj9CcQDAIVOl51kL5lpNba09//KbNqBfjKWmNK22VPyWW/K6xXi1Vph65IdsBtACz7PYHS/ax6ak2uxpE49aIC7//Oc/7WMf+5jNmDHD/f2Tn/zEfv/737vq8dWrVzfYVkF2fHy85eTkNPt4d911lxsCfezYse7vX//6166KfP/+/W7odD3m2rVrXViu0HvOnDn19/Ur0zVku4J1VZlrOHYF5LJr1y4bOZILdQAAAAAAAICehFAcAHBY4mJj7byF86y2ts5efv1t65+bE7ViXPqUvmXBcJXtTZus8ZPbva1AjxMOWdym/9iFM/vbCdMnHdVA3D1dOOzm+o4MvlUNriBaQ6n7Nm3aZNXV1darV69WH09zift2797tfoZCIfdT+6Oq8KeeespefvnlFsP1uXPnNhgifefOnXbyyScf5p4CAAAAAAAA6IoIxQEAhy0hPs4+evZpFhcXa88ve9Nyw55lpKdG3Ta7fK3FhqtsV8ZMs0BMu7cV6DFC1Rb3/oP2kTkjbc6Mox+Iiyq1P/3pT7v5vjUH+J///GdXwf21r33NrrvuOrv99ttt6dKlbujz+fPnu9EmWnu8X/ziFzZgwAAXaKtS/Pjjj7fs7Oz6bVSN/pGPfMTNYx7NihUr3PPdf//9DdZv377dBg4ceIT2HAAAAAAAAEBXQCgOAPjQwfgFi05xlePPvPi6hb2wZWWkR902o3KzBcM1tiPzBPNiOAUBR1qgptRi195nF8w71ubOnNwugbhobu53333XhdcanvyYY46xBx54wOLi4lxAvmTJErviiissJibGnnvuuVYf76tf/apt27bNfvzjH7sK9FmzZrlg3bd+/Xr7xz/+Ye+8806T+3qe5+Ykv+SSS9z9Tz31VLdeQ66///77btG85wAAAAAAAAB6joCnnkMAAD6kurqQPfr0i/bEc69YZkaa9e6V2ey2VbFZtr3XXKsLRh9uHcChC1TstYR1D9i586baibOmtFsg3hYa/vyNN96wmTNnNqj2PhoUhN90003229/+1s1z7tP84w8++KB95StfcRXsAICjp6SkxDIyMqy4uNhNd9Hp/bHznDMBAEfI5+ny7ghfeLijWwAAONJuXWzd5v9BCcUBAEeM5gF+4vlX7NGnX7LkxETL7dN8+FUXk2g7sk6wyvg+7dpGoDsKFG+x9G3/sY+cMceOmzi2UwXi7a2urs7NaZ6VldXRTQGAHotQHADQ4QjFOwShOAB0P7d2o1CcSV0BAEeMhkZecNIsu3DxfKutq7OtO3a7oYyj0fzig/Y9YxkV69u9nUC3sme19d39pH32Iwts2qRxPToQl9jYWAJxAAAAAAAAAA0QigMAjigFcnNmTLaPn3e6xcfH2aZtO5sNxgMWtn7Fr1nf4tfNvFC7txXo0ryw2YYnbWjlG3bJkkU2ZsSQjm4RAAAAAAAAAHRKhOIAgKNi6oSx9umPLLKs9HRbt3GbG9K4OVkV62xQ4bMWDFW1axuBripYV27eyr/b+NR9dvGSsyxvQG5HNwkAAAAAAAAAOi1CcQDAUTNu1FC75ONn27DB/W39pu1WUdl86J1cs8eG7PuPJdQWtmsbga4mvny7hZb/yWYOz7SlHzvLcrIZKhwAAAAAAAAAWkIoDgA4qgYNyLXPffxcO27SONu2Y7cVFZc0u21cqMIGFzxpaZVb2rWNQJfgeZa0d7nVvfV3O2XmeLvovNMtLTW5o1sFAAAAAAAAAJ1ebEc3AADQ/WVmpNmnPnKm5WRn2tMvvu4qxvv3zXHzjzcWYyEbsP8lK6rJtz1pU8yL4VQFxISrLXb9o+YVb7LFp86xBSfNtGAw2NHNAgAAAAAAAIAugUpxAEC7SIiPs7MXnGhLzlngwrwNW3ZYKBRqdvusivU2pIDh1IH46gILv3GbZYX22qcuONPOmHc8gTgAAAAAAAAAHALK7wAA7UaV4ccfN8FyemXa3Q8/Zes2bbOhef0tISE+6vYJoRIbUvBf25s2wQpTxpoFuJYLPYjnWXLxGitf9YiNHzXYLlh0ig3IzenoVgEAAAAAAABAl0O6AABodyOHDbLPf+JcmzBupG3attNKSsua3TZgnvUpfdsGFT5jsaHydm0n0FHi6sosaf39VvvuozZv9mS75KKzCcQBAAAAAAAA4DARigMAOkROdpZ99mNn2cmzj7O9+/bb9p355nles9sn1+yxoXsfs7TKze3aTqBdeZ6ll71noeW3WmLlLjfdwIWL51tKclJHtwwAAAAAAAAAuiyGTwcAdJikxAQX+A0akGuPPfWivb9xqw0e2M8SmxlOPejV2oD9L1tx9U7LTz/OwjHRtwO6oti6Msva84LtWb/CRgzJc8OlDxs8oKObBQAAAAAAAABdHqE4AKBDxcTEuHnGh+T1swcff85WvrveemWmW+9emW4O8mgyKje7yvHd6dOsPJHQEF1fRvk6C2x6xgqLi+z44ybauWecZBnpqR3dLAAAAAAAAADoFhg+HQDQKfTvm2MXX3SOCwNrautsw5btVldX1+z2caEKyyt63gYUvsBc4+iyYuvKLTf/v1b61n3mhartvDPn2SfOP4NAHAAAAAAAAACOICrFAQCdRkJ8nC04aZYNzRtg//7vc7Zu03brn9vbMtKaDwjTqrdbyt5dVpB6rBWmjDELcL0XugAvbBkV6y2w+VnLLyq08aOG2eLT5tiQvP4d3TIAAAAAAAAA6HYIxQEAnc6o4YPs0k9dYI8+/aK99Prbtr+41PL693VDrUcT44WsT+lbllG5yQ2pXpnQp93bDLRVUnW+Ze171XZvWmNpKcl23sKTbe7MKZaYEN/RTQMAAAAAAACAbolQHADQKaWlJttHzzrVRg7Ns4ef+J+9v3GrDcjNsbTUlGbvk1BXbIMLn7LipKG2J22yhYKJ7dpmoCVxdWWWU/KmVe1Yabv2l9g4Vx0+14YOojocAAAAAAAAAI4mQnEAQKcVCARs6oSxltc/1x5/5iVbvvJdKygstoH9+1hcbPOnMFWMp1Ztt71pk2x/8gg9ULu2G4gUE661XmXvWErRKtu+fYelpiTbuQvn2YmzqA4HAAAAAAAAgPZAKA4A6PT69M6yT16w0CaOH2X/fXaZbdi83XplZlhOdqYLzqMJerWWW/K6ZVass71pE608cUC7txs9nOdZeuUmyyl5ywr37rSt+0ts7KhhdhbV4QAAAAAAAADQrgjFAQBdguYTnzR+lBtO/fllb9pzy95wQ6oP7NfXUpKbHyY9sW6/5RU9b5VxvV04XpHQt13bjZ4psabA+pa8YbWFW21D/l7LykinOhwAAAAAAAAAOgihOACgS0lJTrKFp8y2Y8cMt8effdlWrllvsbFBG9Cvj8UGg83eL6m2wAYVPm3l8bkuHK+Kz27XdqNnSKgttN6lqy2uZKNt37XHvTdnT5top86dYf369u7o5gEAAAAAAABAj0QoDgDokvIG5NrFS862N1autSeef8XWbdzmhlnvlZne7JDqklKz21L27bbShIFWkDbBquMy27Xd6L6V4b3LVltSxTbbmV9glVXVNmbEEBeGjx05pMX3JAAAAAAAAADg6CIUBwB0WcFg0KZPHm+jhw+2p1983V5+/W03pHq/Pr0tPS2lxfumVW+31OrtVpI0xApSj7Xa2LR2aze6j6SaPa4yPLl6lxUU7rdthcU2sF8fu+DMeXbcpHEWHxfX0U0EAAAAAAAAgB6PUBwA0OVlpKfaeQtPtinHjrbnXn7T3nrnfcvfu8/69c2x1JSkZu+n2t2Mys2WXrnFShIHWVHKGIZVR5skV++27LLVllKzx0rLK+y93XstIy3FFs0/wc0brvckAAAAAAAAAKBzIBQHAHQbQ/L626cv7GezN02wZ19ebqvXbrTdewusf98cS05KbPZ+AfMso2qLWyriertwvDRxoFkgpl3bj84vpWqnC8OTawusrLzS1u3Za7HBWJs55RibP2e65fXv29FNBAAAAAAAAAA0QigOAOhWNHfzyGGDbPiQgfbu+s323Etv2LvrNukGG5ibYwkJ8S3eX2Fn8v4XrSaYYkXJo604eZiFY1q+D7q3mHCNZVRussyKdZZQV+Iqw9flF1hcXKxNGDvS5sycbGNHDLGYGC6iAAAAAAAAAIDOiFAcANAtKaAcP2qYjR422Fa/t8GefWm5rdu0zWJjgzagb47Fx7c813N8qNz6lr5pvctWWnHScCtKGW21sQyJ3ZMk1uxzQbiG14+xkJWUltuWPfssISHOJh8z2oXho4YNIgwHAAAAAAAAgE6OUBwA0K0pBJ80fpSNGzXU3n5nnRtWffPWnRaMDVpuTnaLw6pL0KuzXhXvWVbFe1aWOND2J42w8oRchlbvpgJenQvBFYYn1Raa53lWUlZu+Xv2WWJigk2bNM5OmD7JRg7Lc6MSAAAAAAAAAAA6P0JxAECPEB8X5wLNY8eOsJVr1tmyN1bZhs3brbauznKysywzPbXFkFO3pFVtd0tdTKKVJA624uShVh3Xq133A0dHfF2xZZavt4zKjRb0al0Yvr+kzPILCt2FEzOmHGsnzJhkwwcPIAwHAAAAAAAAgC6GUBwA0KMkJsTb9MnjbeqEMfb+xq322op3bNXaDbZ7b4H1ysiw3tmZFmxlOOzYcJWrHtdSHZtuJUlDrThpiNUFU9ptP/DhBUOVll611VWGJ9UWuHV1dXW2a99+Ky4ts7SUZJs9baJbhg7qTxgOAAAAAAAAAF0UoTgAoEcKBoM2duRQGzNiiO3ML7Dlb6+x11e8Y+s2bnWVwX1zsi2hlXnHJaGuxHJK37bepW9bRXwfF5CXJuZZOCa+XfYDhyYmXG1pVdtcEJ5ck+9GAFBVeGlFpe0pKLJQKGR9c3rZ3FlT3LD7A3JzCMMBAAAAAAAAoIsjFAcA9GgKPBV8Dsg90U6aNdVWvPOeLVu+yrbu2O1u690rwzLSWh5a3T2OmaXU7HFL3+LlVp7Qz8oS+1t5Qn+rCya32/6gqdhQhaW6oe+3WXLNHguY59YrAC8oKrai/SXuQoixI4a4UQTGjx7W6lzzAAAAAAAAAICug1AcAIAPZKSnumB81tQJtnrtBnv9rTW2YfM2y99TaImJ8da7V6alJCe1+jgxFrK06u1ukarYTBeOKySvjOttFmh5eHZ8SJ5nCXVFllK924XhGho98pKGisoqy99baDW1te41Pf3k411V+OCBuRbTytD5AAAAAAAAAICuh1AcAIBGNGy65hyfcuxoF56+u36zvfH2u7Z9V75t27nH0lOTLbtXZpuGV5fEuv1uyS5fY6FA3IEq8gRVkfezULD1kB2ti68rtuTqfDckekp1vgW9mga3V9fU2L7CYispK3fzymuO8BmTx9sxY0dYeipzwQMAAAAAAABAd0YoDgBAMzRkem6fbLfMnTHJtmzfbWve32hvrn7Ptu3Mt3AobFmZadYrM93NUd4WQa/W0qu2ukWDeFfF9XJzkVfG57gqckLytokNlbtKcIXgCsPjwpVNtqmpqbXC/SVWXFpmscGg9dFc4TMnu7nkFYq39TUDAAAAAAAAAHRthOIAALSBAtRhgwe4Zf7cGbZ+0zZbtXa9rXp3vW3YssM05Xh6aqplZqRaQnx8mx5TQ3on1Ra6xcrXunU1wdT6gFyBeXVcpnmBnh3exoRrLKG2yFXbJ9QWWnLNXosPlUXdtqq6xs0RropwvWYaHn3GlGNs7MghNmzwwDZX9wMAAAAAAAAAug9CcQAADpGG3z5mzHC3LDxltq3buNXe37jN3lu/2Xbs2uvmqk5OSrTM9DRLTUk6pHmqFfbGV5ZZRuUm97dnMVYVl+kCci01selWE0y3UDDRuqPYUMWBALy2yM0LnlhbaHGh8gZzgkcKh8NWVl7pqsE1V3hcbKzlZGfZrOMm2Kjhg2z4oAGWmJjQznsBAAAAAAAAAOhMCMUBAPgQMtJS7biJ49yiKuWt23fbpm077Z33NtjO/ALbvafAYoJBy0hLsYz0VIuPO7RK5YCFD1aTR9Dc5C4gj02zmmBa/e+1wTQLx3TiamjPs2C4ygXdB5cKi68rcSF4bLi6lbt7Vl5RZSWlZVZWXuGGoE9NSbbcnGwbP3qYjRw6yAbn9aMiHAAAAAAAAABQj1AcAIAjWEGu6mQtp504w/YUFNqmbbsOVJJv2GJbd+RbKBSy+Pg4S0tJdot+19zlh0pzkyfV7nNLY7UxSVYbTLVQMMFCgQSrCya6n9H+9gIf/qtAwAtZTLjWYrwPlnCd+xkMVzcJvzUXeIyF2/zYCsErq6qtpLTcSsvKLRz2LDk50bIz092w6EPy+tmgAblumPRDqcgHAAAAAAAAAPQchOIAABwFCrr75mS7ZeaUY9zQ3lu277JtO/Ntw+Yd7qcqyTXUusLclOQkS01OtOTkJIsNfrg5xOPClW6x2ta3DVvQQjHxarAbql216Z77XUH9gcUu4S+oAAAmhElEQVQLxBz4W+G9Zw3Cb4XzqmY/EhSA19TUWnlllVVUVLowXCF4YmK8ZWWk28Txo9yc7nn9+1puTi83ZzgAAAAAAAAAAK0hFAcAoB1ojvGxI4e6xU40F/juzN9rO3fvta07dtvmrTttf2mZ5e8tsrAXdoGv7pOUEO/mxNZw4EejEjrGVOVdae1Nc4FXVtVYRWWlu2CgurrGDYWu4eVTkhMtt09vG5LX3/r1zXYheP/cHDdfOAAAAAAAAAAAh4reZQAAOkBSYoINHzzQLX5IvK+o2PILCm3P3kI31PqOXflWWl5pJWXlVl1d60LjQMCz+DgF5fGWlJDgfipIPpwh2I8mVX1rqPjqmlq3qAK8uqbG/a7bAhZwbVeF/IiheTZ4YD/rk51lOR8sqSlJnW6fAAAAAAAAAABdE6E4AACdgKrA/UDYRg936xQel5VX2v7iEisqLnVL4f5i25W/z81Xrgrrwv0lVltbd2Ckc89zwXlsbNBig7EWFxu0uLjYA3/HxlpcMGixcbEWEwi4wLmtobPaEf4g5A6FwgeWcMjCobDVffC7bqupqbPaujrdQ01x1e6qcE+IPxB+a+7vPr2zLDMjzc0Brn3VT83FDgAAAAAAAADA0UIoDgBAJ6XQOi012S15A3Ib3KYQuqS0vD4s1zDkGpJdQXlJWYWVlJZZaVmF+1uheUVFldWGQlZXV+fm6VbQXf88mjdcs4b7IbkrSf/gp7vlQGgf1BLUEvzg96D7OyE+0ZKSEty83717ZVhmepprc3pq6oGfaSluKHgqvwEAAAAAAAAAHYFQHACALkiBdFZmultaovBcYbmWKoXmVdWu0lvzlnsfhOOqAtfw7frd/R0+sE6/q9pcw7Or4jw+Ps79nvDBT/9vBeME3gAAAAAAAACAzopQHACAbh6ep6YkuwUAAAAAAAAAgJ4opqMbAAAAAAAAAAAAAADA0UIoDgAAAAAAAAAAAADotgjFAQAAAAAAAAAAAADdFqE4AAAAAAAAAAAAAKDbIhQHAAAAAAAAAAAAAHRbhOIAAAAAAAAAAAAAgG6LUBwAAAAAAAAAAAAA0G0RigMAAAAAgCZWr15t06ZNs6ysLLviiivM87yObhIAAAAAAIeFUBwAAAAAADRQXV1tixcvtqlTp9ry5cttzZo1dscdd3R0swAAAAAAOCyxh3c3AAAAAADQXT3++ONWXFxsN954oyUnJ9u1115rX/7yl23p0qVRA3QtPt1PCgsLra6uzv0eExPjlnA47Bafvz4UCjWoRG9ufTAYtEAgUP+4ketF27dlfWxsrHtct74yzq0LmGdBq7OwBSwc0V1ycH2MhS14sI1uTcit028H14fcbSGLNc8CEevrLMa8Juv12HqOOjvQjoPraw+0vY3rY63WPW4oatvZJ/aJfWKfetg+7d/f4Hyjc4fOCc2dhzrl+amVtnfGfaopP/B+iBQOxJp5XqP1AQsHghbwDrz7Wluve3uBmGbXx3h6bK/V9e79HAhYjNdwn/z3eZO2N7eefWKf2Cf2qQftU2Fh5z8/lZSUHDgOrYxuFvAY/wwAAAAAAES45ppr7NVXX7XHHnvM/a2ug+zsbBd0N/bDH/7QbQ8AAAAAQEfZtm2bDRw4sNnbqRQHAAAAAAAN6Er7oUOHNrnav6ioyM0xHunKK6+0yy+/vP5vXfWv8Fwhuu4HoHP8m87Ly3Mdhenp6R3dHAAAuizOqUDno4u4S0tLrX///i1uRygOAAAAAAAa0PB4CQkJDdYlJiZaRUVFk1Bc2zXeNjMzs13aCeDQqPOeDnwAAD48zqlA55KRkdHqNgcnfwEAAAAAADCzXr162d69exus05X38fHxHdYmAAAAAAAOF6E4AAAAAABoYNq0abZs2bL6vzdt2mTV1dUuLAcAAAAAoKshFAcAAAAAAA3MnTvXzZd4++23u7+vvfZamz9/vptXHEDXoykOfvCDHzSZ6gAAABwazqlA1xXwNPs4AAAAAABAhIceesiWLFliSUlJFhMTY88995yNGzeuo5sFAAAAAMAhIxQHAAAAAABR7d6929544w2bOXOmZWdnd3RzAAAAAAA4LITiAAAAAAAAAAAAAIBuiznFAQAAAAAAAAAAAADdFqE4AAAAAAAAcAS98MIL9tWvfrXdnq+urq7B33v37rVQKHTYj1dbW9vi44umVli+fPkhPe5TTz1lf/nLXw6rTQUFBRYOhw+p3QDQ073yyitWXl7e5u11/igqKmqyfsuWLR/qvHK4NNBxtHPQ4brnnnts3bp11pkVFhZaWVnZId/vtddes127dh3y/Y7E6/rWW2/ZF7/4RWsvjd8TmvLpUO77pS99yYqLi5u9nQG2uy9CcQAAAAAAAOAI6tOnj91yyy22Zs2aFre7//777ec//3nU27Kzs23t2rVRb1Mgfeutt9YHwZMmTbJHHnmk/nZ1TJ9wwgmH1dF9zTXX2FVXXVX/9/79+2306NH24IMPNthu+/bttmDBgjYH4/v27bOLL77YfvzjH0cNXFqzaNEie+yxx5q9XbedffbZVllZeciPDQCdUV5envXq1ct69+7tloSEhAaf9fKFL3zBfvvb30a9vz5r9dn5xz/+sc3P+Z3vfMfOOussdxGSziHV1dXup84pv/71r902CgyrqqqafQydM/r3729DhgyxQCBggwcPdot+P5TPaLXhkksusT/96U/169QW7ZdC45YWbRPtub797W/b+++/b0888YQlJSW5NvpLv379bMaMGa2266STTmrQpmiuv/56F27/6Ec/sm984xu2Y8cO+8lPfuJuO/fcc+3xxx9v9r6XXXaZ/f3vf3e/X3311XbMMcdEXXRe9dXU1NjHP/7x+sDff+3aQs9x4YUXNrjwbO7cue4Y+RdK6JwfqfFFaomJifaHP/zBXTwRqbnvIQrR9X7Qd4jXX3/d/d5csK2QWsfOD/x1bE488cT6YHz16tU2aNAg+/e//93geX/4wx/WL2qbLzY21v1buuGGG6I+n/69fO5zn2v1Qjx0TYTiAAAAAAAA6HbUOTps2DDX+anQ+N13363vPJ02bZplZWXZFVdc0aAaSB2h6hSPi4tzHa7RKq4UQh977LH23HPPub+feeaZ+sDCXxQeiDrXG9+mAMM3a9Ys++tf/+ra0ZjaoE7maNRmBRc333yz227p0qUucK6oqHAd4g8//LD97Gc/s2AweEjH7Fe/+pWtWLHCBeO+zMxM9zyf+tSn7H//+5/rZNfzK4C+7rrrGnSAq1M+mpKSEjvzzDPtM5/5jKugVyBQWlraZDsF8HptZs+e7ZZ58+bV36YO87vvvttGjBhhw4cPtzFjxrhO8D179rjbFy5caHPmzLGPfexjh7TPANAZzy0SHx9vb775phspQ8vixYtdmBdJf+txo/nyl7/sglMFuJFhqsLg888/P+qFWv/3f/9n77zzjvvsV1CsMPJvf/ubC5l/8YtfuOdTwH366ae7EFN/a1stqamp9v3vf9+1+6GHHrLNmze7x33vvffc4re3rRQO6zwYed7UYypwHzhwYP2iY5uent5gnZbbb7+9wePpHJaRkeHOR/5rocfzlwceeKDZ827jc1Vr28XExLhAX+8THY8///nP9RcaPP300+491By9Pv7j6xynC9X0/opcFPzrcSNDeL3WCrd1X11QoffTyy+/7LbTa6ljovZEWrVqlWubzvt6b2tkAUlOTnbnaX2X0Ptc74tIusBN7fRf+5kzZ1pKSopNnDixfp3+1vecaHJyctzPtLQ0t4hem+aOpS5kOPnkk90+6kKPrVu31l+koX3XhQpaH3kf7ZO+j2kfdX/9uzzuuOPcotdAF9P5f3/iE5+ov++ll17qjpkuZkD3QygOAAAAAACAbmXDhg0uKFYVtqqzRo0a5TqnFegqVJg6daqrTlIl9x133OHu8+KLL7rOfHX+b9q0yQUa3/rWt5o8tjpf1SEdKTc3tz608BcFAGpH5DpVK0UGx+rIf/LJJ13ld35+fpv3Tx24aqfCDfn6179u9957r+vE/u53v2vf/OY3XQfxoVCn8Z133mn/+Mc/mnT2K/zQ8Tn++ONdR76CDwX8Crh1nFXVrnUKpRvbuHGjCx4UcquD+vLLL3fBkR5LwUskdYyrAmz69OmuSi2yEk+huAKS9evX28iRI13VlzrFVZXvu/LKK11H/O9///tD2ncA6IznFv/Cpueff96dQ/x1ej6/ilXhn0SuU/Cq84Ie79RTT3WLH6bqwqEBAwY0qR5XMKpgUOcSXaylbRQc6jyjYPy+++5zAb1C1VdffdWF93379nXBqUYS0XlJw1Gr6thvUzQt3Rbpv//9rztH3HTTTQ3uo4uiVAGuYNpfnn32WXeRVOQ6DRmvIbIjaQQXVcHLhxkeW+3RuaY5qhBXe3Qu04Vqet0V5ioI1nFT+Ktqev+18r8XvPTSS+59ovOcjrHOyS2N+KLzouiCOIXWen30Our8unPnThcE6yIEvU8V+uu9GHlRgt7DH/3oR+2f//yna4+Oif72L9rQe1IjFeh10PeKSD/4wQ/c66CLHxQ265grqNfPf/3rX+4iOB0HDekeya/e1/cVUfjsf+fwL+5QWyP3W8db+7VkyRL3XUPHXhcw6GKJt99+21Xd6z0beSGgjo0eb/78+e5Y6EI+vQ5f+cpX3L/RlStX1k8Do4px/6IN//l+97vfuX8vLVX0o2tqeFkIAAAAAAAA0MWpck+hhSqm/OHEVRmmzk112t94442uQ/baa691lXQKOdRxrSHJ1YEqWqequEjaRp2nqrjyKRxWGBJtiFYNLx5Z4aaOanWUi0JwdeCqM/jRRx91QYM6pP1O7sbUQazt1UaF0aqKa44CFAUequzT737leku0X6r88juqfepEVif0hAkT6jvRfarKuuCCC1znd2PaF3W0qwNaAdD3vve9+tt0XFVpqMoyBUoKylXVpv1Tx7wCAx0XhR+qUlOIruPSlgrDX/7yly6A13Fva/gCAJ3t3CL+Z5hCaX3W+uv0GakwUcNHK2TU56U+Z1UVrEpdje6hz34Fy2qPLkxSqKn7qjpWIbIuZoqk4Fzh9mmnneYeU5/fCidVNav26Vwn//nPf+ovRtLncmS1sj7DtWi9wnc/7NTFAodKx1D7caQ+x3Xhl0Yb0QUKomOnYxR5zHVhgc5NjV9zHcPI84+GjtfiD/etx1IIrxBWF47pvaDjrCHHdWGBKq0VLivoVoirCwn0vLqPjrW+L+hiAl10oUptfT/w91sXOih01ntAwbqec/z48W69RhcQXbCg7wgKfjUyi7bT+0Bhuy6OiBT5HUPfE/Taath1nec1yoG+W/gXbeg1OOWUU9zvutBOo8lMnjy5wePpu4sCco3W4t+mUFxDrjc+Z+uiCx0HDffvtyPadx6NYqDvB3r9/QsIdZz0XLpIIhpVxev10wg9Tz31lFunY6jXQt9j9D1IF3roPalj5x9fHTd971AIHkm3/+Y3v3EXVpxxxhlRnxNdE6E4AAAAAAAAupXIITRFFUCqLlZntDpa/eBXQa8/77eCimj3iaSgVZXYkZVDegwtCgCWLVtW38GrDuEXXnjBhbqiTliF2f5Q6Z///OddFZc6uNWhrKqle+65p74TWRVWU6ZMcR2zuq9CBVWVq1NYoYWqsxQ8tzREujrdGw+V2hxVkP3lL39psl7rFOioMvu8885r02OpA1qvgfZNne7aVz2Gf2y0P6psU+f/1772NRd+qyJL1WyNaV51vQ66r4ZqbY2CGw2xrgBEoQAAdMVzS2Q1s4JnVWU3ns9bFx3ddtttLqxUeO3T56oCe4W0Ckk1/YVCcZ1fVGGrYbQbUzCrEL1xBbQCeFWBR1LVus5nOr/pXKdwV+GiwliN+KE2qnJZ99NntypyRZ/hLV385VPFsc6B/mgojSnsVXs1jYgC09bonKNjFRnO++G/f65U2K9pORpTKKz2RIbzOqa6GMyvbNY+qU0a3lth+NChQ12w/JGPfMSFxQqCdYGBqvAVHOsCCF0QplFNVIWtQFz0OmnRa+kfO1V/6yIKfcfQPNzaD52vI+nCPI3Uojaec8457pyq6U30vC3NBa7vI3pfRF5kp2OikF/t16KQWfuviwP0uI3pYj/Nv+2PZKBjo+8yfhv997D2RcG0vu/o4pHmqNJc+xl54Yi+xyjo1sUFLX2n0egMjYd517zourBO35X03UD/LnQxRORFBwrhdVFgtKBd70MF680N7Y6uh1AcAAAAAAAA3ZY6qlU9rGpkDUmqzmqfOmnVUaqKalVI+dQJqhBXQ4n7NDepOkbVSRxtOE3dpvtoqHFRB7Z+96uoNXS6qph8mrtT/I51hc+RobSGZFdo3LhyUJ366ujWEKvqiFbI4Qfj6nxWOCHq1P7pT39aX5neEgUsqp6KFrCro1hhtYYu1Vzgelx1JjfeVs+t27QvOsYKwnUMFOT4FY7RqIpOIZEfiCsc0Dp1fKtN6oxX0KSOdgUrqubSxQQK2zUsq4KRxlTpt23bNkJxAF363OIHmPp81fzMkesUQKtCWMFrJA0HrWG4VQGs3zWFhaaj0NDhmotb8zDrvKMAWOG4hvhWNbE+czWHsm7z5wKPRoG/Qnf/HKA5o9UWnXsUOj788MMtDvmt21q7WEtBbkvnLj23qnhVje/TZ75fzS46H+kcoXOLQlodQ13YpfOdbmupDbrdr3jX0NuRdB5X8K/pOjTXdzQKcHUO1HlaF8spMNb5SO3VRWOqCBc9tl6TSGqnLjRQWK0LKhRS6wIEXTShynAda30v0Ha6IEDfE3Qhg4JtTTGic7CeSyPV6Jytdug86n+X0HcLn6rDdRxUQe1fqLBlyxb3PPr+oPe1TxdeRIbnCpcjpziJ/F2vsX8Bgn7XPuv5NcKC3js6PrqIJJpnnnnGtUcXGPh0rDWajCq6NbR75OgEej3UXn130NDv+n4USaMk6N+W3gt6rx/q9Cr+9wlC8e6DUBwAAAAAAADdlqqSFCaoKksd2I2H81TwqqrlyOBCHdeqfvKHzFQHtTplVUXXXGW2OpQV/vod7XpMVXMrZBB1xKst0bRU7d2YKqW0yKc//en69Y888ojbP3UW+0GBhkRtCx0Dv/Iwmk9+8pNuEe2DOscVVGsIWFVcKRTS/qrzXcOiiqrZFCz4w75Ho9u0TWT1mYaFVeWcOvZVTamLB/TaKAhQh7gCec3JqgrCxsO4+hSg04ENoKufW/xwWaNu+KG4PjNF5xZ95ipQj6TAVsN0K5RUSKjwUUHjxRdf7MJmDbGt+2gbjcShKlpRCKo2K9zUuUv3bUxDV+viLQXrfnsVnOozWcN7+8NWK5iMvBhKn906RyrsVIDZWiiu8FWf483xnzuy4lyhaWSYrzDZf00UDus4aEh4vzJfQbnOlzoWOqY6pyhc1u86Hyq4Vnsb02ul8F0XiumirMiA1qehxhXC6vXXeXrFihXuddHFcbqPLloQXbCgtvk0/L2qtxWk64ILhdwKkjUc+bx589yIKjq36nhHjhig0FvD3+u4qFJdQ+PrvKzXXa9vSxc56BjpwjR/qHs9hl4fXUCg96beq/5oAJoT3afn11D0zX2v8UW+3npfqPJe1dzNheIa+UDfXSJfW39ueP9iAlGbNLy7Ljy46KKL3NDsGrren6vdp6H+/WOp6ntddKf3hi4I0PHT/vqV8fr31BjfJ7ofQnEAAAAAAAB0S+oQ1pyUqqRSyKoOWXUqR1KwG9mp/de//tXNt6rhcH0KHxQotFR5rLBWQ6O2VCne0pCxaoc6aVsbVlbViXquxqGCAgwNSRo5h7k/36g/xGtrIcTWrVubrc5T6KPOe3VCa2ltTnFVC6qTWe3UonarIzqyc1kVj+qM1hC8Gr7U3w8N06oOes3Vq0Wd0jouGspUi6rEFSwpeGhMHfWqjmxpeFYA6ArnFgV6+vzcsWOH+2xWNbqGIFd1uZ5bVeff+973GtxHoeD111/vPh8VYOuzU5+hqlpvPBy5AkJ/WHa/kliPr6BbQ1E3phBR55/IiuxoNHWIT88fGbq2hdqkAF+hsj8k/aGKnG5Dr0PkXOEKXrXoYrJrrrnGXXSg0PpHP/pRiyObiI6twmCF4zp3RQuFFbir0lvHXaGzPwy3glu9b3SsNW+4wvHIYcIfffRRF+yqut2/mEL38+dwjxQ5pYi+Oyj4Vfit/YwcBl4V9X5Vur6TNJ4mRY/jX7Tg/y067jpHq70aMWbJkiXugjif3k9jx4611jT+/qHh3TXsvY5jY2q/hrGPPCai7w56/0R+P9K/Af170r87v/pd539Vjjd+r6laXBcX6GICf953XTyg/VI4H+07jOj9p/dhW4boR9dxcCIEAAAAAAAAoJvQcJ3qxFWH7rhx49w6VUypAityG3Wg+sNtKkzVMKWqYvKDAtFQt6qQUxWZFs2VqUD45z//ef02/ryZLYmcyzOSwmsFzurkbY2GCFWnrh82+4s6b1WFGLlOQcBdd91lbaFKq8gO70gaOlQd6+p4byt1fCtYUSWiOq01l6g6ovW7OrI1FLrm3NVQ6H4g3lLAEjkXbEsUxis4ihYiAEBXOrco+FZ4qirxJ5980j772c+6cFNhuqqemwuMFeLpM1YB/KpVq1zFswJAff5qURiuETkUBEejanJ/28hFbYkc1rpxVbsqzO+88043JLiCXz9oV+iq31XN7c8v3hJV72ou7sghuY80jT6iYeN1kZWeT5XyOq66KKs52lbHQK+DquV1Xoy2P3p/aBhyHXdV5Cvs1flPI64o2NXtOufqNY8cPl0XOKjyOXLUAZ1DNce2LkRQeKuqcFU867VXpblPF4rpfaILH/R+a7yoCrq5CxN00YLa2LiiXBfZaRh+nbMbfz/Q/mm4/EOl4f51To9WYa/3rS4u9P9d+RdiaF/9CnZ/URX7ypUr3RQAkesbzwuv46+L7fQe1HcnfVfT6ACNh1mPRqMBqFK+tZEN0LXwagIAAAAAAKBbUVWRggV17CtsVieyzJkzx1VuKXzWXJ2qeJ4/f77rbN2zZ48tXrzYDW2qDmf/PgqCFXBEUhW4Os/9qnBRxbM6Xv2qKFUJqupL1dKi0FrDokYLEhRW6DnbMty5Ou5Vnda4olyVUqqw8odWF3X+tiWsFwXqasMTTzxhp512WoPbVD3ndyofKj2/KubUea2qPD/YV+f7Aw880GwnvSr3FBBoqFhVWCogaY2qKRU86bgDQFc/t/jDOmudAlANUa4qZA2nrcdqjp7XH5VDAbsuUFJFeuTQ0wrEmwvVFUyqqrYxBf0KciODY4XlCg81lYbCXM19rWGu/SrmyEpxXQCmSvO2UDX2jBkzXAivecyPdCCuc8qll15aPw2Hqp51blW4fN1117kAOjK41THRkOT+firkV1Csx9F5s3G1vy760nDjCr8V3OpcqDmtRc+r51CwHskfIj/ygjQF6zrmet9FVu/rXN84WNax1bm28YgAovehqqWjiawUj/T3v/+9fkQWvW/9C+N0flZQ39wQ6C1RGK1l//79TW7TcY0cTl70vtFIM40rzv/whz+47zwPPvhgg/WN57PXxQQ67poDXXQxiKZpiRxJwH9PaNh5n47jDTfc4EJ1dC9UigMAAAAAAKBbUQe1OqRVZaaOf39RaKrOewWt6ijVfNXqmBZV3amjVx3xkfcRv+LNX9RJqxBaHdU+DV2qyqmCggK3KDTR/Nr+3wqB/XkxIylcUIf//fff32RO2ubCjmid1/48s5G0XVsDCIUjqmJUNZmqrX3qEFbl3//7f//PPUdLIbs6riPboWFfVRGnfVTAomPmXwSgDmgdo8i5zBU4KfBRteHdd9/tjocCF91XFXYKZCLpb//5FPqo814XDUQLBACgK51bFLIrJFeVqs4dujhJwa1G/1Bors8+BbSqVG587tDntIbm9qe8UBsUku/atctVn+szXZXL0ei+GkLcrxyOXPT5HPlc+jxXJa3uo89qhZT6HG4cOPoUbEabOz0aHQcN9a0LCvxgUs+jc4d/YYEuNtPvOnfoNv3uLxreW0FwJLVXx02V3gsWLKi/UM2/gEznGV2EpVEAVBEuOn8rUNVtOm6Ro5ZovnBdBKGLInS/SArJVfmvIdMVIOt4a190XlQoruOkIcRVCd6Y2qhzsgLfs846q9lj1Ph8rNFo9H0iWpW/3kPRRqvROr9SPHLObn1/iDw/63W+77773O8acl4XA0SGyK3RPqlCu3FoHY3apPd/S1PARPvOI5HvLz3X8OHD3YUhGlZe7x1d2KCLH/xtNVe6nkcBv0auEb3f9Frp/cfQ6d2QBwAAAAAAAPQgu3bt8h555BGvoKDgqD3HOeec491yyy2tbve///3PKy8vb7I+KyvLW7NmTav3/853vuPNmTPHi4mJ8bZu3ep9WOvXr/eWLVtW//esWbO8T37yk+73H/7wh15iYqKXnp7uZWRkeGlpafW/JyUlecFg0Pv1r39df98lS5aox97r06ePN3jwYG/06NHexIkTvblz53qLFy/2Ro4c6Z1//vn1269cudIbM2aMd9ttt3k1NTWuLX379vXuvPNOd/uQIUO8cePGeePHj3fL8OHDvf/+97/utvz8/PrfAaCrn1vefPNN78ILL/RKSkrq1xUVFXkVFRX1f59wwgnewoULvd27d9evq6qqcp+Pubm53tVXX+3a5Fu9erX7vJ42bZr34IMPRn3ea665xsvMzKz/nI1chg4d6o0dO7bB9nv37m3w90UXXeQNHDjQfeZr0Tlg0KBB7ve8vDxv0aJFh3QcXnvtNW/t2rX1+5+QkFB/3mlp0flJxyDSVVdd5V1xxRVedXV1g/X9+vXzVqxY0eS5H374YS8lJcUbMWKE9+qrrzbbxuuuu86d/z73uc/VHxOdC6dOnerOgzo/hUIh78Ybb/QGDBjgXXvttW47/dS+/Otf/2rwWJ/5zGfc6zZ9+nRvx44d3r333tvkNdG5eN26dQ3aoceJj4+vP/aRi47J0qVLm7Rd3x0qKyvdcsEFF3jHHnusW3/llVd6vXr1cs+l87LOv4WFhd7mzZtdW1atWuUdCh1fvRcCgYA7Vi0t2kbb6vtRY3reBQsWuNfMP97R1NXVucfw6fvBGWec4d5PvgceeMB918rJyXGPGQ6H3Xq93yK3Q/cS0H86OpgHAAAAAAAAcOhU9afhQFXlpCFBjzRV2qmSSvN0HypVJaoCS3OmRpuTU9WVanvj4dp9qipTBb0q8QAAbaOh1jV/d7QK26NNI4bo8z7aKCWKonROiDafdGekamSNAKCK8Oaq331vvvmmq8ZXZbLupypwVeqrut+nEQY0PHvkufqxxx5zw3trhIGW2qGq57aMJvNh6DnaUsmvkQ9UyX8otA+qPPeHYG+NRgHQ+7fxe1jvIVXyDxo0yE1R0NwUAEBzCMUBAAAAAAAAAAAAAN0Wc4oDAAAAAAAAAAAAALotQnEAAAAAAAAAAAAAQLdFKA4AAAAAAAAAAAAA6LYIxQEAAAAAAAAAAAAA3RahOAAAAAAAAAAA7aSgoMDC4XBHNwMAgB6FUBwAAAAAAAAAgHayaNEie+yxx5q9/bXXXrPrr7/e/V5VVWV1dXXt2DoAALonQnEAAAAAAAAAAI6g/fv327Rp02z27NlumTdvXv1tgUDA7r77bhsxYoQNHz7cxowZY4MGDbI9e/a423/2s5+5beTrX/+69e/f31JSUmzIkCFuiYuLswcffLDD9g0AgK6IUBwAAAAAAAAAgCMoLS3N/v73v9v06dNt7ty59qc//an+NgXel112ma1fv95Gjhxpf/jDH2zr1q3Wp08fe/jhh12g/s1vftNuvPFG+/znP+8C9IULF9rmzZvtnXfecY992mmndej+AQDQ1RCKAwAAAAAAAABwBAWDQRs9erRlZGS4RRXhqgB/6qmnXCiekJAQddj0pUuX2nnnnWe/+tWv3Pb9+vWz2tpaS0xMdNvo/nPmzLHk5OQO2CsAALouQnEAAAAAAAAAAI6ytWvXuipwheJJSUlNblel+MUXX+xuu/rqq10FuYZOVyheXV3ttnn00Uft+eefd8Oo6ycAAGib2DZuBwAAAAAAAAAA2kjB9tNPP22xsbGu0rusrMxVeJeUlNgbb7xhv/vd7+z999+3P/7xj65K/Nvf/rZdd9119qUvfcmF4+eff757nMLCQnvkkUfcnOPa9ve//72rIJ80aVJH7yIAAF0GleIAAAAAAAAAABxh48ePt9zcXBdga+5whdtZWVm2b98+N7T67Nmz3d/HHHOMTZ482d3HH2Jdc4YrNA+FQrZp0ybLy8uzH/zgB26bZcuW2eDBg92w7AAAoG0IxQEAAAAAAAAAOMI++tGP2rHHHusqus8880x79tlnXVCu4dO1XnOH5+Tk2PHHH2+nnnqq7dy5077//e9bTEyM3XXXXVZUVOTmJn/hhRfs9ttvt//85z+2YsUKu/nmm+2iiy7q6N0DAKBLYfh0AAAAAAAAAADagQLuqVOnRr1N84draHUNsa5h1Z977jk3D7kqzBWc33DDDXbhhRe6bW699dZ2bzsAAF0ZleIAAAAAAAAAABwlu3fvtu9973uuEvyvf/2rLVy4MOp2q1evdtXlGm5dP9etW2eXXnqp/fSnP3W3T5kyxQ29Pm7cODdHOQAAaDtCcQAAAAAAAAAAjqDKykq755577KGHHrK7777bEhISXOit6u9PfvKTVl1d3WB7/a15ws8++2x788037cUXX3RziSsAP/300+2+++6zE044wQXkmZmZdtJJJ7mh1AEAQNsEPM/z2rgtAAAAAAAAAABoxapVq9xQ51dccYULwbdu3WqzZ8+2X/ziF+7voUOHumHSNb+4VFVV2S233GKnnXaa+3vPnj2uqvyyyy6zxYsXu5D9pptucnORh0IhN/f4I488Yq+99hpV4wAAtAGhOAAAAAAAAAAAR1FxcbHdf//9tnTp0kO+b3l5uaWkpDRZr+pyVaADAIDWEYoDAAAAAAAAAAAAALot5hQHAAAAAAAAAAAAAHRbhOIAAAAAAAAAAAAAgG6LUBwAAAAAAAAAAAAA0G0RigMAAAAAAAAAAAAAui1CcQAAAAAAAAAAAABAt0UoDgAAAAAAAAAAAADotgjFAQAAAAAAAAAAAADdFqE4AAAAAAAAAAAAAMC6q/8PflIfOxBWHBcAAAAASUVORK5CYII="}, "metadata": {}, "output_type": "display_data"}], "execution_count": 31}, {"cell_type": "code", "execution_count": null, "id": "459621dd-88a3-404f-aac6-e4b8b947eb09", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}