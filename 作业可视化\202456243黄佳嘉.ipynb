{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-21T04:12:04.648658Z", "start_time": "2025-06-21T04:12:04.592663Z"}}, "source": ["#黄佳嘉202456243 \n", "import pandas as pd\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "warnings.filterwarnings('ignore')\n", "# 设置中文字体，避免中文显示乱码\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  \n", "plt.rcParams['axes.unicode_minus'] = False  \n"], "outputs": [], "execution_count": 102}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:05.394738Z", "start_time": "2025-06-21T04:12:05.332938Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "student_info = pd.read_excel('2024级会计7,8班学生名单.xlsx')\n", "print(student_info.head())\n"], "id": "776c8de712854f8d", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          学号    院系   专业         班级   学校代码      学校\n", "0  202216003  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "1  202456235  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "2  202456236  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "3  202456237  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "4  202456238  会计学院  会计学  2024会计学07  10562  大连财经学院\n"]}], "execution_count": 103}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:06.140110Z", "start_time": "2025-06-21T04:12:06.073780Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "homework_scores = pd.read_excel('2024级会计7,8班_课后作业成绩（前5次）.xlsx')\n", "print(homework_scores.head())\n"], "id": "a7b795d883f73d4b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          学号         班级  第1次作业  第2次作业  第3次作业  第4次作业  第5次作业\n", "0  202216003  2024会计学07    NaN    NaN    NaN    NaN    NaN\n", "1  202456235  2024会计学07   74.0   82.5   70.0  101.0  100.0\n", "2  202456236  2024会计学07   82.0   85.0  100.0  101.0   87.5\n", "3  202456237  2024会计学07   95.0   85.0   90.0  101.0   87.5\n", "4  202456238  2024会计学07   90.0   77.5   90.0   80.6   62.5\n"]}], "execution_count": 104}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:06.801867Z", "start_time": "2025-06-21T04:12:06.791353Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "# \"学号 班级\"列作为合并键\n", "merged_data = pd.merge(student_info, homework_scores, on=['学号','班级'], how='inner')\n"], "id": "8ef91e130dfba16b", "outputs": [], "execution_count": 105}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:07.634074Z", "start_time": "2025-06-21T04:12:07.613016Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "print(merged_data.head())\n"], "id": "d3ed81dfc29dfde1", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  第4次作业  \\\n", "0  202216003  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN    NaN   \n", "1  202456235  会计学院  会计学  2024会计学07  10562  大连财经学院   74.0   82.5   70.0  101.0   \n", "2  202456236  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0   85.0  100.0  101.0   \n", "3  202456237  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0   85.0   90.0  101.0   \n", "4  202456238  会计学院  会计学  2024会计学07  10562  大连财经学院   90.0   77.5   90.0   80.6   \n", "\n", "   第5次作业  \n", "0    NaN  \n", "1  100.0  \n", "2   87.5  \n", "3   87.5  \n", "4   62.5  \n"]}], "execution_count": 106}, {"metadata": {}, "cell_type": "markdown", "source": "# # # 数据清洗", "id": "e0634fb084d68ca9"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:09.823076Z", "start_time": "2025-06-21T04:12:09.801368Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "duplicate_rows = merged_data.duplicated()\n", "print(\"是否存在重复行：\", duplicate_rows.any())"], "id": "2c5d6cda8a2afadd", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在重复行： False\n"]}], "execution_count": 107}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:10.558437Z", "start_time": "2025-06-21T04:12:10.543263Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "# 检查空值\n", "null_values = merged_data.isnull()\n", "print(\"是否存在空值：\", null_values.any().any())\n", "if null_values.any().any():\n", "    print(\"每列的空值数量：\")\n", "    print(null_values.sum())\n", "    \n", "    rows_with_nulls = merged_data[merged_data.isnull().any(axis=1)]\n", "    print(\"包含空值的行数：\", len(rows_with_nulls))\n", "    if len(rows_with_nulls) > 0:\n", "        print(\"包含空值的行：\")\n", "        print(rows_with_nulls)\n"], "id": "3a5616e4ad46c5f8", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在空值： True\n", "每列的空值数量：\n", "学号        0\n", "院系        0\n", "专业        0\n", "班级        0\n", "学校代码      0\n", "学校        0\n", "第1次作业    13\n", "第2次作业    13\n", "第3次作业    14\n", "第4次作业    15\n", "第5次作业    12\n", "dtype: int64\n", "包含空值的行数： 18\n", "包含空值的行：\n", "           学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  \\\n", "0   202216003  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "6   202456240  会计学院  会计学  2024会计学07  10562  大连财经学院  100.0   95.0   90.0   \n", "12  202456247  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "16  202456251  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "17  202456254  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "24  202456262  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "26  202456264  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "34  202456273  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "39  202456279  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   85.0    NaN   \n", "43  202456284  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0  100.0  100.0   \n", "44  202456285  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "48  202456289  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "53  202456294  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   90.0   70.0   \n", "54  202456296  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   87.5   60.0   \n", "56  202456298  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "58  202456300  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "59  202456302  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "68  202456312  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "\n", "    第4次作业  第5次作业  \n", "0     NaN    NaN  \n", "6     NaN  100.0  \n", "12    NaN    NaN  \n", "16    NaN    NaN  \n", "17    NaN    NaN  \n", "24    NaN    NaN  \n", "26    NaN    NaN  \n", "34    NaN    NaN  \n", "39    NaN   62.5  \n", "43    NaN  100.0  \n", "44    NaN    NaN  \n", "48   94.4   62.5  \n", "53    NaN   62.5  \n", "54   80.8    NaN  \n", "56    NaN    NaN  \n", "58    NaN    NaN  \n", "59    NaN    NaN  \n", "68   74.0   62.5  \n"]}], "execution_count": 108}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:11.388179Z", "start_time": "2025-06-21T04:12:11.376540Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "#空值填充为0分\n", "merged_data = merged_data.fillna(0)\n"], "id": "c0607d35ec9b5dba", "outputs": [], "execution_count": 109}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:12.116898Z", "start_time": "2025-06-21T04:12:12.102353Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "print(\"填充后是否还有空值：\", merged_data.isnull().any().any())\n"], "id": "74348e948469defb", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["填充后是否还有空值： False\n"]}], "execution_count": 110}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:12.839759Z", "start_time": "2025-06-21T04:12:12.812762Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "#分数列应该在0-100之间\n", "numeric_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        min_val = merged_data[col].min()\n", "        max_val = merged_data[col].max()\n", "        print(f\"列 '{col}' 的值范围: {min_val} - {max_val}\")\n", "        \n", "        # 检查是否有超出合理范围的值\n", "        if min_val < 0 or max_val > 100:\n", "            print(f\"警告: 列 '{col}' 中存在可能的异常值!\")\n", "            # 显示异常值\n", "            abnormal_values = merged_data[(merged_data[col] < 0) | (merged_data[col] > 100)]\n", "            if len(abnormal_values) > 0:\n", "                print(\"异常值所在行：\")\n", "                print(abnormal_values)\n"], "id": "a60a02021306f2cd", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["列 '第1次作业' 的值范围: 0.0 - 100.0\n", "列 '第2次作业' 的值范围: 0.0 - 100.0\n", "列 '第3次作业' 的值范围: 0.0 - 100.0\n", "列 '第4次作业' 的值范围: 0.0 - 101.0\n", "警告: 列 '第4次作业' 中存在可能的异常值!\n", "异常值所在行：\n", "           学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  \\\n", "1   202456235  会计学院  会计学  2024会计学07  10562  大连财经学院   74.0   82.5   70.0   \n", "2   202456236  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0   85.0  100.0   \n", "3   202456237  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0   85.0   90.0   \n", "7   202456241  会计学院  会计学  2024会计学07  10562  大连财经学院   87.0   95.0   90.0   \n", "11  202456246  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0  100.0  100.0   \n", "13  202456248  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0  100.0  100.0   \n", "15  202456250  会计学院  会计学  2024会计学07  10562  大连财经学院   87.0   95.0  100.0   \n", "20  202456258  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0  100.0   90.0   \n", "36  202456275  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "42  202456282  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   95.0   90.0   \n", "49  202456290  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   95.0   90.0   \n", "50  202456291  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "51  202456292  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   92.5   80.0   \n", "55  202456297  会计学院  会计学  2024会计学08  10562  大连财经学院   90.0   95.0   90.0   \n", "60  202456303  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   90.0   90.0   \n", "61  202456304  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "63  202456307  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   95.0   80.0   \n", "64  202456308  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   95.0  100.0   \n", "65  202456309  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   90.0   90.0   \n", "\n", "    第4次作业  第5次作业  \n", "1   101.0  100.0  \n", "2   101.0   87.5  \n", "3   101.0   87.5  \n", "7   101.0  100.0  \n", "11  101.0  100.0  \n", "13  101.0   87.5  \n", "15  101.0   75.0  \n", "20  101.0   87.5  \n", "36  101.0   62.5  \n", "42  101.0  100.0  \n", "49  101.0  100.0  \n", "50  101.0   50.0  \n", "51  101.0   62.5  \n", "55  101.0  100.0  \n", "60  101.0  100.0  \n", "61  101.0   62.5  \n", "63  101.0   75.0  \n", "64  101.0  100.0  \n", "65  101.0  100.0  \n", "列 '第5次作业' 的值范围: 0.0 - 100.0\n"]}], "execution_count": 111}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:13.913963Z", "start_time": "2025-06-21T04:12:13.893425Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "# 处理异常值：小于0的替换为0，大于100的替换为100\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        # 将小于0的值替换为0\n", "        merged_data.loc[merged_data[col] < 0, col] = 0\n", "        # 将大于100的值替换为100\n", "        merged_data.loc[merged_data[col] > 100, col] = 100\n", "\n", "# 验证是否还有异常值\n", "print(\"\\n处理异常值后的验证：\")\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        min_val = merged_data[col].min()\n", "        max_val = merged_data[col].max()\n", "        print(f\"列 '{col}' 的值范围: {min_val} - {max_val}\")\n"], "id": "2001e0d2759b697f", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "处理异常值后的验证：\n", "列 '第1次作业' 的值范围: 0.0 - 100.0\n", "列 '第2次作业' 的值范围: 0.0 - 100.0\n", "列 '第3次作业' 的值范围: 0.0 - 100.0\n", "列 '第4次作业' 的值范围: 0.0 - 100.0\n", "列 '第5次作业' 的值范围: 0.0 - 100.0\n"]}], "execution_count": 112}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:14.638686Z", "start_time": "2025-06-21T04:12:14.602141Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243  \n", "#保存清洗后的数据\n", "merged_data.to_excel('黄佳嘉202456243.xlsx', index=False)"], "id": "2ec4cdaeb00ba2e7", "outputs": [], "execution_count": 113}, {"metadata": {}, "cell_type": "markdown", "source": "# # # 数据分析 利用Pandas库对整合后的数据进行统计分析", "id": "3443e5b5582a8aa9"}, {"metadata": {}, "cell_type": "markdown", "source": ["# # # 个人学习成绩分析\n", "# # # 计算本人的作业平均成绩、最高成绩、最低成绩"], "id": "2709f78bd81974c6"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:17.514907Z", "start_time": "2025-06-21T04:12:17.499243Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "my_student_id = 202456243\n", "my_data = merged_data[merged_data['学号'] == my_student_id]\n", "\n", "if len(my_data) == 0:\n", "    print(f\"未找到学号为 {my_student_id} 的学生数据\")\n", "else:\n", "    print(f\"学号为 {my_student_id} 的学生信息：\")\n", "    print(my_data[['学号']].iloc[0])\n", "    \n", "    score_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns.tolist()\n", "    score_columns = [col for col in score_columns if col not in ['学号', '学校代码']]\n", "    \n", "    my_scores = my_data[score_columns]\n", "    print(\"\\n本人各次作业成绩：\")\n", "    print(my_scores)\n", "    \n", "    # 计算平均成绩、最高成绩、最低成绩\n", "    avg_score = my_scores.mean(axis=1).iloc[0]\n", "    max_score = my_scores.max(axis=1).iloc[0]\n", "    min_score = my_scores.min(axis=1).iloc[0]\n", "    \n", "    print(f\"\\n本人成绩统计：\")\n", "    print(f\"平均成绩: {avg_score:.2f}\")\n", "    print(f\"最高成绩: {max_score:.2f}\")\n", "    print(f\"最低成绩: {min_score:.2f}\")\n"], "id": "f43d792dfed658a2", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学号为 202456243 的学生信息：\n", "学号    202456243\n", "Name: 8, dtype: int64\n", "\n", "本人各次作业成绩：\n", "   第1次作业  第2次作业  第3次作业  第4次作业  第5次作业\n", "8   82.0   85.0  100.0   67.4  100.0\n", "\n", "本人成绩统计：\n", "平均成绩: 86.88\n", "最高成绩: 100.00\n", "最低成绩: 67.40\n"]}], "execution_count": 114}, {"metadata": {}, "cell_type": "markdown", "source": ["# # # # 2. 班级学习数据分析\n", "# # # # 分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少"], "id": "762b565b08991c96"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:21.164341Z", "start_time": "2025-06-21T04:12:21.137530Z"}}, "cell_type": "code", "source": ["#黄佳嘉202456243 \n", "my_class = my_data['班级'].iloc[0]\n", "\n", "# 筛选出同一班级的学生\n", "class_students = merged_data[merged_data['班级'] == my_class]\n", "print(f\"班级 {my_class} 共有 {len(class_students)} 名学生\")\n", "# 计算每个学生的平均分\n", "student_avg_scores = class_students[score_columns].mean(axis=1)\n", "class_students['平均分'] = student_avg_scores\n", "merged_data.loc[class_students.index, '平均分'] = student_avg_scores\n", "\n", "# 查看班级平均分的统计信息\n", "class_avg = student_avg_scores.mean()\n", "class_max = student_avg_scores.max()\n", "class_min = student_avg_scores.min()\n", "\n", "print(\"班级成绩统计：\")\n", "print(f\"班级平均分: {class_avg:.2f}\")\n", "print(f\"班级最高成绩: {class_max:.2f}\")\n", "print(f\"班级最低成绩: {class_min:.2f}\")\n"], "id": "bcd419118fa6b880", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024会计学07 共有 35 名学生\n", "班级成绩统计：\n", "班级平均分: 70.88\n", "班级最高成绩: 99.00\n", "班级最低成绩: 0.00\n"]}], "execution_count": 115}, {"metadata": {}, "cell_type": "markdown", "source": "# # # # 查找成绩优秀或需要辅导的学生", "id": "8ca7f27b06a23328"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:23.184489Z", "start_time": "2025-06-21T04:12:23.163488Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "# 按平均分排序，找出班级内成绩最好的前10名学生\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "print(f\"班级 {my_class} 成绩优秀的前{top_n}名学生：\")\n", "print(top_students[['学号', '平均分']])\n", "\n", "# 找出班级内成绩最差的后10名学生\n", "bottom_students = class_students.sort_values(by='平均分').head(top_n)\n", "print(f\"\\n班级 {my_class} 需要辅导的后{top_n}名学生：\")\n", "print(bottom_students[['学号', '平均分']])\n"], "id": "a874258a8799e0c9", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024会计学07 成绩优秀的前10名学生：\n", "           学号    平均分\n", "11  202456246  99.00\n", "13  202456248  96.50\n", "9   202456244  95.84\n", "29  202456267  95.16\n", "22  202456260  94.84\n", "7   202456241  94.40\n", "23  202456261  94.34\n", "19  202456257  92.52\n", "21  202456259  92.48\n", "20  202456258  91.90\n", "\n", "班级 2024会计学07 需要辅导的后10名学生：\n", "           学号    平均分\n", "0   202216003   0.00\n", "12  202456247   0.00\n", "17  202456254   0.00\n", "16  202456251   0.00\n", "24  202456262   0.00\n", "26  202456264   0.00\n", "34  202456273   0.00\n", "6   202456240  77.00\n", "14  202456249  78.02\n", "4   202456238  80.12\n"]}], "execution_count": 116}, {"metadata": {}, "cell_type": "markdown", "source": "# # # # 分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布", "id": "a0f2b9205f791969"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:27.784958Z", "start_time": "2025-06-21T04:12:27.760827Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "# 定义分数段\n", "def get_score_range(score):\n", "    if score < 60:\n", "        return '60分以下'\n", "    elif 60 <= score < 70:\n", "        return '60-69分'\n", "    elif 70 <= score < 80:\n", "        return '70-79分'\n", "    elif 80 <= score < 90:\n", "        return '80-89分'\n", "    else:\n", "        return '90分以上'\n", "\n", "# 应用分数段函数到班级学生\n", "class_students['分数段'] = class_students['平均分'].apply(get_score_range)\n", "merged_data.loc[class_students.index, '分数段'] = class_students['分数段']\n", "\n", "# 统计班级内各分数段的学生人数\n", "score_range_counts = class_students['分数段'].value_counts().sort_index()\n", "print(f\"班级 {my_class} 各分数段学生人数分布：\")\n", "print(score_range_counts)\n", "\n", "# 计算班级内各分数段的百分比\n", "score_range_percentage = score_range_counts / len(class_students) * 100\n", "print(f\"\\n班级 {my_class} 各分数段学生人数百分比：\")\n", "for range_name, percentage in score_range_percentage.items():\n", "    print(f\"{range_name}: {percentage:.2f}%\")\n"], "id": "1cce79767ad51111", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024会计学07 各分数段学生人数分布：\n", "分数段\n", "60分以下      7\n", "70-79分     2\n", "80-89分    11\n", "90分以上     15\n", "Name: count, dtype: int64\n", "\n", "班级 2024会计学07 各分数段学生人数百分比：\n", "60分以下: 20.00%\n", "70-79分: 5.71%\n", "80-89分: 31.43%\n", "90分以上: 42.86%\n"]}], "execution_count": 117}, {"metadata": {}, "cell_type": "markdown", "source": "# # # # 数据可视化", "id": "d01ac272ca03bf4c"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:29.298371Z", "start_time": "2025-06-21T04:12:29.281008Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "from matplotlib.gridspec import GridSpec"], "id": "a1589d3b64eaa363", "outputs": [], "execution_count": 118}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:30.204896Z", "start_time": "2025-06-21T04:12:29.949704Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "# 1. 绘制班级成绩Top10的柱状图\n", "plt.figure(figsize=(12, 6))\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "\n", "# 将学号转换为字符串，以便在图表中正确显示\n", "top_students_ids = [str(int(id)) for id in top_students['学号']]\n", "\n", "# 使用字符串类型的学号作为x轴标签\n", "bars = plt.bar(top_students_ids, top_students['平均分'], color='skyblue')\n", "plt.title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "plt.xlabel('学号')\n", "plt.ylabel('平均分')\n", "plt.ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.xticks(rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "            \n", "plt.tight_layout()\n", "plt.savefig('班级成绩Top10柱状图.png')\n", "plt.show()"], "id": "3cf2ef893cfb2db5", "outputs": [{"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 119}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:31.365590Z", "start_time": "2025-06-21T04:12:31.085502Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）\n", "plt.figure(figsize=(12, 6))\n", "my_scores_values = my_scores.iloc[0].values\n", "plt.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "plt.title(f'学号 {my_student_id} 的成绩趋势图')\n", "plt.xlabel('作业序号')\n", "plt.ylabel('分数')\n", "plt.xticks(range(len(score_columns)), [f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "plt.grid(True)\n", "plt.ylim(0, 120)  # 设置y轴范围\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    plt.text(i, score + 2, f'{score:.1f}', ha='center')\n", "    \n", "plt.tight_layout()\n", "plt.savefig('个人成绩趋势图.png')\n", "plt.show()\n"], "id": "6b668569c733e563", "outputs": [{"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 120}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:32.874647Z", "start_time": "2025-06-21T04:12:32.295311Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "# 3. 绘制本班级不同分数段比例图（饼图）\n", "plt.figure(figsize=(10, 8))\n", "# 确保分数段是按照正确顺序排列的\n", "ordered_ranges = ['60分以下', '60-69分', '70-79分', '80-89分', '90分以上']\n", "ordered_counts = [score_range_counts.get(range_name, 0) for range_name in ordered_ranges]\n", "\n", "# 定义更美观的饼图颜色\n", "colors = ['#FF6B6B', '#FFD93D', '#6BCB77', '#4D96FF', '#9B59B6']\n", "explode = (0.05, 0.05, 0.05, 0.05, 0.05)  \n", "\n", "# 绘制饼图，添加更多样式\n", "wedges, texts, autotexts = plt.pie(\n", "    ordered_counts, \n", "    labels=None, \n", "    autopct=lambda p: f'{p:.1f}%' if p >= 1 else f'{p:.2f}%',  # 小于1%的显示两位小数\n", "    startangle=90, \n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=False, \n", "    wedgeprops={'edgecolor': 'white', 'linewidth': 0.8},\n", "    pctdistance=0.75\n", ")\n", "plt.title(f'班级 {my_class} 不同分数段学生比例', fontsize=16, fontweight='bold', pad=20)\n", "plt.axis('equal')  \n", "\n", "# 设置饼图文本样式\n", "plt.setp(autotexts, size=12, weight=\"bold\", color='white')\n", "\n", "# 添加增强的图例\n", "percentages = [count/sum(ordered_counts)*100 for count in ordered_counts]\n", "legend_labels = [f\"{r} ({p:.1f}%)\" for r, p in zip(ordered_ranges, percentages)]\n", "plt.legend(wedges, legend_labels, title=\"分数段\", loc=\"center left\", \n", "           bbox_to_anchor=(1.0, 0, 0.5, 1), fontsize=11)\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级分数段比例饼图.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n"], "id": "94c7a83e1264a705", "outputs": [{"data": {"text/plain": ["<Figure size 1000x800 with 1 Axes>"], "image/png": "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****************************************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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 121}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:41.403402Z", "start_time": "2025-06-21T04:12:41.194549Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "# 4. 按班级分组，绘制两个班的平均成绩对比图\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 获取所有班级\n", "all_classes = merged_data['班级'].unique()\n", "class_avg_scores = []\n", "\n", "# 计算每个班级的平均分\n", "for cls in all_classes:\n", "    cls_students = merged_data[merged_data['班级'] == cls]\n", "    cls_avg = cls_students[score_columns].mean().mean()\n", "    class_avg_scores.append({'班级': cls, '平均分': cls_avg})\n", "\n", "# 创建班级平均分数据框\n", "class_avg_df = pd.DataFrame(class_avg_scores)\n", "\n", "# 绘制班级平均分对比条形图\n", "colors = ['#66B2FF' if cls != my_class else '#FF9900' for cls in class_avg_df['班级']]\n", "bars = plt.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "plt.title('不同班级平均成绩对比')\n", "plt.xlabel('班级')\n", "plt.ylabel('平均分')\n", "plt.ylim(0, 100)\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级平均成绩对比图.png')\n", "plt.show()\n"], "id": "692e5cbf6f11ceb8", "outputs": [{"data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 122}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:12:46.205263Z", "start_time": "2025-06-21T04:12:44.294576Z"}}, "cell_type": "code", "source": ["#  黄佳嘉202456243 \n", "# 5. 将所有图表整合到一张完整的图纸中\n", "plt.figure(figsize=(20, 15))\n", "gs = GridSpec(3, 2, figure=plt.gcf())\n", "\n", "# 1. 绘制班级成绩Top10的柱状图\n", "ax1 = plt.subplot(gs[0, 0])\n", "\n", "top_students_ids = [str(int(id)) for id in top_students['学号']]\n", "bars = ax1.bar(top_students_ids, top_students['平均分'], color='skyblue')\n", "\n", "ax1.set_title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "ax1.set_xlabel('学号')\n", "ax1.set_ylabel('平均分')\n", "ax1.set_ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "ax1.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "\n", "# 2. 绘制班级平均分对比条形图\n", "ax2 = plt.subplot(gs[0, 1])\n", "\n", "# 绘制班级平均分对比条形图\n", "bars = ax2.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "ax2.set_title('不同班级平均成绩对比')\n", "ax2.set_xlabel('班级')\n", "ax2.set_ylabel('平均分')\n", "ax2.set_ylim(0, 100)\n", "ax2.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "# 3. 绘制学生本人成绩的趋势图\n", "ax3 = plt.subplot(gs[1, 0])\n", "ax3.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "ax3.set_title(f'学号 {my_student_id} 的成绩趋势图')\n", "ax3.set_xlabel('作业序号')\n", "ax3.set_ylabel('分数')\n", "ax3.set_xticks(range(len(score_columns)))\n", "ax3.set_xticklabels([f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "ax3.grid(True)\n", "ax3.set_ylim(0, 120)  # 设置y轴范围\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    ax3.text(i, score + 2, f'{score:.1f}', ha='center')\n", "\n", "# 4. 绘制本班级不同分数段比例图（饼图）\n", "ax4 = plt.subplot(gs[1, 1])\n", "wedges, texts, autotexts = ax4.pie(\n", "    ordered_counts, \n", "    labels=None,  # 移除直接标签，改用图例显示\n", "    autopct=lambda p: f'{p:.1f}%' if p >= 1 else f'{p:.2f}%',  # 小于1%的显示两位小数\n", "    startangle=90, \n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=False,  # 移除阴影效果，使图形更清晰\n", "    wedgeprops={'edgecolor': 'white', 'linewidth': 0.8},  # 减小边框线宽\n", "    pctdistance=0.75\n", ")\n", "ax4.set_title(f'班级 {my_class} 不同分数段学生比例', fontsize=14, fontweight='bold')\n", "ax4.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "plt.setp(autotexts, size=10, weight=\"bold\", color='white')\n", "\n", "# 添加增强的图例，确保小数值段也能清晰显示\n", "percentages = [count/sum(ordered_counts)*100 for count in ordered_counts]\n", "legend_labels = [f\"{r} ({p:.1f}%)\" for r, p in zip(ordered_ranges, percentages)]\n", "ax4.legend(wedges, legend_labels, title=\"分数段\", loc=\"center left\", \n", "          bbox_to_anchor=(1.0, 0, 0.5, 1), fontsize=8)\n", "\n", "# 添加总标题\n", "plt.suptitle('学生成绩分析报告', fontsize=20, fontweight='bold')\n", "\n", "# 调整子图布局\n", "plt.tight_layout(rect=[0, 0, 1, 0.96])  # 为顶部标题留出空间\n", "\n", "# 保存整合后的图表\n", "plt.savefig('学生成绩分析报告.png', dpi=300, bbox_inches='tight')\n", "plt.show() "], "id": "2213971594389c22", "outputs": [{"data": {"text/plain": ["<Figure size 2000x1500 with 4 Axes>"], "image/png": "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********************************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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 123}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "9434e36193063aec"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}