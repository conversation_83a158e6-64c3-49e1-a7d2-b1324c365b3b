{"cells": [{"metadata": {}, "cell_type": "markdown", "source": ["# # （一）数据预处理\n", "# 通过分析，将所需数据表进行合并，并对合并后的数据表进行清洗，查看数据是否存在重复、空值、特殊符号等，并用已学过知识进行处理。"], "id": "d83e1924e2d4241a"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:29:56.288598Z", "start_time": "2025-06-20T14:29:56.276167Z"}}, "cell_type": "code", "source": ["# 导入所需库并设置警告过滤\n", "import pandas as pd\n", "import warnings\n", "import matplotlib\n", "\n", "# 忽略所有警告信息\n", "warnings.filterwarnings('ignore')\n", "# 设置matplotlib不使用Xwindows后端，避免显示警告\n", "matplotlib.use('Agg')\n"], "id": "2072da498476aa99", "outputs": [], "execution_count": 107}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:29:57.882850Z", "start_time": "2025-06-20T14:29:57.839156Z"}}, "cell_type": "code", "source": ["# 读取学生信息表\n", "student_info = pd.read_excel('2024审计学、财务管理学生名单.xlsx')\n", "print(\"学生信息表的形状：\", student_info.shape)\n", "print(\"学生信息表的前5行：\")\n", "print(student_info.head())\n"], "id": "d798904c77121549", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学生信息表的形状： (84, 7)\n", "学生信息表的前5行：\n", "       学号/工号   姓名    院系        专业            班级   学校代码      学校\n", "0  202455004   单婧  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n", "1  202455005  丁昱楠  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n", "2  202455008   郭晟  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n", "3  202455017   李优  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n", "4  202455024   潘璠  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院\n"]}], "execution_count": 108}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:29:59.009943Z", "start_time": "2025-06-20T14:29:58.761547Z"}}, "cell_type": "code", "source": ["# 读取作业完成情况表\n", "homework_scores = pd.read_excel('2024审计学、财务管理平时作业成绩统计.xlsx')\n", "print(\"作业完成情况表的形状：\", homework_scores.shape)\n", "print(\"作业完成情况表的前5行：\")\n", "print(homework_scores.head())\n"], "id": "a1f9f30d4ba7df8", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["作业完成情况表的形状： (84, 12)\n", "作业完成情况表的前5行：\n", "       学号/工号            班级  第二章Python程序基本语法课后作业  第三章高级数据类型课后作业  \\\n", "0  202455004  2024审计学（注会班）                 90.0          100.0   \n", "1  202455005  2024审计学（注会班）                 70.5           85.5   \n", "2  202455008  2024审计学（注会班）                 87.0           92.8   \n", "3  202455017  2024审计学（注会班）                 79.5           85.4   \n", "4  202455024  2024审计学（注会班）                 84.0           85.5   \n", "\n", "   第四章Python条件语句课后作业  第五章Python循环语句课后作业  第六章函数课后作业  第七章Pandas数据结构课后作业  \\\n", "0               63.0              100.0       97.0               98.0   \n", "1               64.0               63.0       91.0               70.0   \n", "2               78.0               64.0       73.0               80.0   \n", "3               68.0               83.0       80.0               60.0   \n", "4               73.0               56.0       97.0               80.0   \n", "\n", "   第八章数据的增删改查课后作业  第九章数据清洗课后作业  第十章数据统计分析课后作业  第十一章数据可视化课后作业  \n", "0            77.5         65.0           70.0           50.0  \n", "1            70.5         85.0           90.0           88.0  \n", "2            67.5         85.0            NaN            NaN  \n", "3            70.5         80.0           65.0           75.0  \n", "4            75.5         95.0           85.0          100.0  \n"]}], "execution_count": 109}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:29:59.697365Z", "start_time": "2025-06-20T14:29:59.686362Z"}}, "cell_type": "code", "source": ["# 合并数据表\n", "# 以学号为键进行合并\n", "# 首先查看两个表的列名，确定用于合并的键\n", "print(\"学生信息表的列名：\", student_info.columns.tolist())\n", "print(\"作业完成情况表的列名：\", homework_scores.columns.tolist())\n"], "id": "51ea6b64d857a5f4", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学生信息表的列名： ['学号/工号', '姓名', '院系', '专业', '班级', '学校代码', '学校']\n", "作业完成情况表的列名： ['学号/工号', '班级', '第二章Python程序基本语法课后作业', '第三章高级数据类型课后作业', '第四章Python条件语句课后作业', '第五章Python循环语句课后作业', '第六章函数课后作业', '第七章Pandas数据结构课后作业', '第八章数据的增删改查课后作业', '第九章数据清洗课后作业', '第十章数据统计分析课后作业', '第十一章数据可视化课后作业']\n"]}], "execution_count": 110}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:00.417456Z", "start_time": "2025-06-20T14:30:00.404454Z"}}, "cell_type": "code", "source": ["# 根据列名确定合并键\n", "# 只使用\"学号/工号\"列作为合并键\n", "merged_data = pd.merge(student_info, homework_scores, on=['学号/工号'], how='inner')\n"], "id": "f9b039f9c8d915e4", "outputs": [], "execution_count": 111}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:01.095297Z", "start_time": "2025-06-20T14:30:01.072779Z"}}, "cell_type": "code", "source": ["# 查看合并后的数据\n", "print(\"合并后的数据表的形状：\", merged_data.shape)\n", "print(\"合并后的数据表的前5行：\")\n", "print(merged_data.head())\n"], "id": "ed69b25913d4b257", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["合并后的数据表的形状： (84, 18)\n", "合并后的数据表的前5行：\n", "       学号/工号   姓名    院系        专业          班级_x   学校代码      学校          班级_y  \\\n", "0  202455004   单婧  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院  2024审计学（注会班）   \n", "1  202455005  丁昱楠  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院  2024审计学（注会班）   \n", "2  202455008   郭晟  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院  2024审计学（注会班）   \n", "3  202455017   李优  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院  2024审计学（注会班）   \n", "4  202455024   潘璠  会计学院  审计学（注会班）  2024审计学（注会班）  10562  大连财经学院  2024审计学（注会班）   \n", "\n", "   第二章Python程序基本语法课后作业  第三章高级数据类型课后作业  第四章Python条件语句课后作业  第五章Python循环语句课后作业  \\\n", "0                 90.0          100.0               63.0              100.0   \n", "1                 70.5           85.5               64.0               63.0   \n", "2                 87.0           92.8               78.0               64.0   \n", "3                 79.5           85.4               68.0               83.0   \n", "4                 84.0           85.5               73.0               56.0   \n", "\n", "   第六章函数课后作业  第七章Pandas数据结构课后作业  第八章数据的增删改查课后作业  第九章数据清洗课后作业  第十章数据统计分析课后作业  \\\n", "0       97.0               98.0            77.5         65.0           70.0   \n", "1       91.0               70.0            70.5         85.0           90.0   \n", "2       73.0               80.0            67.5         85.0            NaN   \n", "3       80.0               60.0            70.5         80.0           65.0   \n", "4       97.0               80.0            75.5         95.0           85.0   \n", "\n", "   第十一章数据可视化课后作业  \n", "0           50.0  \n", "1           88.0  \n", "2            NaN  \n", "3           75.0  \n", "4          100.0  \n"]}], "execution_count": 112}, {"metadata": {}, "cell_type": "markdown", "source": "# ## 数据清洗", "id": "5d538406c8bd9733"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:02.427611Z", "start_time": "2025-06-20T14:30:02.412610Z"}}, "cell_type": "code", "source": ["# 检查重复行\n", "duplicate_rows = merged_data.duplicated()\n", "print(\"是否存在重复行：\", duplicate_rows.any())\n", "if duplicate_rows.any():\n", "    print(\"重复行数量：\", duplicate_rows.sum())\n", "    print(\"重复行索引：\", merged_data[duplicate_rows].index.tolist())\n"], "id": "d42fcece8a83db3b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在重复行： False\n"]}], "execution_count": 113}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:03.708298Z", "start_time": "2025-06-20T14:30:03.692320Z"}}, "cell_type": "code", "source": ["# 检查空值\n", "null_values = merged_data.isnull()\n", "print(\"是否存在空值：\", null_values.any().any())\n", "if null_values.any().any():\n", "    print(\"每列的空值数量：\")\n", "    print(null_values.sum())\n", "    \n", "    # 查看哪些行包含空值\n", "    rows_with_nulls = merged_data[merged_data.isnull().any(axis=1)]\n", "    print(\"包含空值的行数：\", len(rows_with_nulls))\n", "    if len(rows_with_nulls) > 0:\n", "        print(\"包含空值的行：\")\n", "        print(rows_with_nulls)\n"], "id": "e2ecdec9abec06b1", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在空值： True\n", "每列的空值数量：\n", "学号/工号                  0\n", "姓名                     0\n", "院系                     0\n", "专业                     0\n", "班级_x                   0\n", "学校代码                   0\n", "学校                     0\n", "班级_y                   0\n", "第二章Python程序基本语法课后作业    2\n", "第三章高级数据类型课后作业          2\n", "第四章Python条件语句课后作业      2\n", "第五章Python循环语句课后作业      2\n", "第六章函数课后作业              6\n", "第七章Pandas数据结构课后作业      3\n", "第八章数据的增删改查课后作业         2\n", "第九章数据清洗课后作业            3\n", "第十章数据统计分析课后作业          2\n", "第十一章数据可视化课后作业          2\n", "dtype: int64\n", "包含空值的行数： 11\n", "包含空值的行：\n", "        学号/工号   姓名    院系                 专业                   班级_x   学校代码  \\\n", "2   202455008   郭晟  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "9   202455079  闫雨欣  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "14  202455156  王楚涵  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "21  202456057  宋昊辰  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "22  202456091  李欣恬  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "40  202456251  彭佳韵  会计学院  财务管理（正保大数据智能产业学院）  2024财务管理（正保大数据智能产业学院）  10562   \n", "45  202456276   程煜  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "65  202456419   吴晓  会计学院           审计学（注会班）           2024审计学（注会班）  10562   \n", "66  202456450  钱文彬  会计学院  财务管理（正保大数据智能产业学院）  2024财务管理（正保大数据智能产业学院）  10562   \n", "73  202457141  霍佳琪  会计学院  财务管理（正保大数据智能产业学院）  2024财务管理（正保大数据智能产业学院）  10562   \n", "81  202457199  朱思静  会计学院  财务管理（正保大数据智能产业学院）  2024财务管理（正保大数据智能产业学院）  10562   \n", "\n", "        学校                   班级_y  第二章Python程序基本语法课后作业  第三章高级数据类型课后作业  \\\n", "2   大连财经学院           2024审计学（注会班）                 87.0           92.8   \n", "9   大连财经学院           2024审计学（注会班）                100.0          100.0   \n", "14  大连财经学院           2024审计学（注会班）                 67.0           78.3   \n", "21  大连财经学院           2024审计学（注会班）                 65.5           78.1   \n", "22  大连财经学院           2024审计学（注会班）                  NaN            NaN   \n", "40  大连财经学院  2024财务管理（正保大数据智能产业学院）                 96.0           58.2   \n", "45  大连财经学院           2024审计学（注会班）                 71.0          100.0   \n", "65  大连财经学院           2024审计学（注会班）                 86.0           78.3   \n", "66  大连财经学院  2024财务管理（正保大数据智能产业学院）                  NaN            NaN   \n", "73  大连财经学院  2024财务管理（正保大数据智能产业学院）                 19.5           27.3   \n", "81  大连财经学院  2024财务管理（正保大数据智能产业学院）                 93.5          100.0   \n", "\n", "    第四章Python条件语句课后作业  第五章Python循环语句课后作业  第六章函数课后作业  第七章Pandas数据结构课后作业  \\\n", "2                78.0               64.0       73.0               80.0   \n", "9                85.0               89.0       76.0              100.0   \n", "14               80.0               73.0        NaN                NaN   \n", "21               73.0               79.0        NaN                NaN   \n", "22                NaN               90.0       82.0               90.0   \n", "40               70.0               58.0       85.0               80.0   \n", "45               69.0               59.0        NaN               90.0   \n", "65               80.0               67.0        NaN               80.0   \n", "66                NaN                NaN        NaN                NaN   \n", "73               26.0                NaN       97.0              100.0   \n", "81               80.0               73.0        NaN              100.0   \n", "\n", "    第八章数据的增删改查课后作业  第九章数据清洗课后作业  第十章数据统计分析课后作业  第十一章数据可视化课后作业  \n", "2             67.5         85.0            NaN            NaN  \n", "9             92.5          NaN           60.0           70.0  \n", "14            63.0         80.0           60.0           60.0  \n", "21            75.5         90.0           60.0           60.0  \n", "22            72.5         90.0           70.0           90.0  \n", "40            72.5          NaN           80.0           88.0  \n", "45            67.5         75.0           90.0           88.0  \n", "65            65.5         80.0           50.0           88.0  \n", "66             NaN          NaN            NaN            NaN  \n", "73            52.5         40.0           90.0           75.0  \n", "81             NaN         90.0          100.0           88.0  \n"]}], "execution_count": 114}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:03.894269Z", "start_time": "2025-06-20T14:30:03.875697Z"}}, "cell_type": "code", "source": ["# 由于课后作业表中的空值是学生没有回答的作业，所以空值填充为0分\n", "merged_data = merged_data.fillna(0)\n"], "id": "249193d06e2fc57c", "outputs": [], "execution_count": 115}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:05.172294Z", "start_time": "2025-06-20T14:30:05.162490Z"}}, "cell_type": "code", "source": ["# 验证是否还有空值\n", "print(\"填充后是否还有空值：\", merged_data.isnull().any().any())\n"], "id": "85eb47cd6746e91", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["填充后是否还有空值： False\n"]}], "execution_count": 116}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:28.721748Z", "start_time": "2025-06-20T14:30:28.701587Z"}}, "cell_type": "code", "source": ["# 检查是否有特殊符号或异常值\n", "# 这里可以根据具体情况检查不同列的数据\n", "# 例如，检查分数列是否在合理范围内\n", "# 假设分数列应该在0-100之间\n", "numeric_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns\n", "for col in numeric_columns:\n", "    if col not in ['学号/工号','学校代码']: # 排除学号/工号列\n", "        min_val = merged_data[col].min()\n", "        max_val = merged_data[col].max()\n", "        print(f\"列 '{col}' 的值范围: {min_val} - {max_val}\")\n", "        \n", "        # 检查是否有超出合理范围的值\n", "        if min_val < 0 or max_val > 100:\n", "            print(f\"警告: 列 '{col}' 中存在可能的异常值!\")\n", "            # 显示异常值\n", "            abnormal_values = merged_data[(merged_data[col] < 0) | (merged_data[col] > 100)]\n", "            if len(abnormal_values) > 0:\n", "                print(\"异常值所在行：\")\n", "                print(abnormal_values)\n"], "id": "d3e6acc9a3329aa", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["列 '第二章Python程序基本语法课后作业' 的值范围: 0.0 - 100.0\n", "列 '第三章高级数据类型课后作业' 的值范围: 0.0 - 100.0\n", "列 '第四章Python条件语句课后作业' 的值范围: 0.0 - 85.0\n", "列 '第五章Python循环语句课后作业' 的值范围: 0.0 - 100.0\n", "列 '第六章函数课后作业' 的值范围: 0.0 - 100.0\n", "列 '第七章Pandas数据结构课后作业' 的值范围: 0.0 - 100.0\n", "列 '第八章数据的增删改查课后作业' 的值范围: 0.0 - 95.5\n", "列 '第九章数据清洗课后作业' 的值范围: 0.0 - 100.0\n", "列 '第十章数据统计分析课后作业' 的值范围: 0.0 - 100.0\n", "列 '第十一章数据可视化课后作业' 的值范围: 0.0 - 100.0\n"]}], "execution_count": 117}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:32.532452Z", "start_time": "2025-06-20T14:30:32.498025Z"}}, "cell_type": "code", "source": ["# 保存清洗后的数据\n", "merged_data.to_excel('清洗后的综合数据表.xlsx', index=False)\n", "print(\"数据清洗完成，已保存到'清洗后的综合数据表.xlsx'\") \n"], "id": "7a1a3f8efdc1ec9a", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据清洗完成，已保存到'清洗后的综合数据表.xlsx'\n"]}], "execution_count": 118}, {"metadata": {}, "cell_type": "markdown", "source": ["# # （二）数据分析\n", "# 利用Pandas库对整合后的数据进行统计分析\n"], "id": "4b6c95f698aacd01"}, {"metadata": {}, "cell_type": "markdown", "source": ["# ## 1. 个人学习成绩分析\n", "# ### （1）计算本人的作业平均成绩、最高成绩、最低成绩"], "id": "683d6619b75a9e98"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:35.034216Z", "start_time": "2025-06-20T14:30:35.017739Z"}}, "cell_type": "code", "source": ["# 学生本人学号、姓名\n", "my_student_id = 202456242\n", "# 筛选出学生本人课后作业成绩数据\n", "my_data = merged_data[merged_data['学号/工号'] == my_student_id]\n", "\n", "if len(my_data) == 0:\n", "    print(f\"未找到学号为 {my_student_id} 的学生数据\")\n", "else:\n", "    print(f\"学号为 {my_student_id} 的学生信息：\")\n", "    print(my_data[['学号/工号', '姓名']].iloc[0])\n", "    \n", "    # 查看列索引用于选取作业成绩列\n", "    print(\"\\n所有列名：\")\n", "    print(merged_data.columns.tolist())\n", "    \n", "    # 假设作业成绩列是数值型列，排除学号/工号和姓名等非成绩列\n", "    score_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns.tolist()\n", "    # 排除学号/工号列和学校代码列\n", "    score_columns = [col for col in score_columns if col not in ['学号/工号', '学校代码']]\n", "    \n", "    print(\"\\n作业成绩列：\")\n", "    print(score_columns)\n", "    \n", "    # 计算本人各次作业的成绩\n", "    my_scores = my_data[score_columns]\n", "    print(\"\\n本人各次作业成绩：\")\n", "    print(my_scores)\n", "    \n", "    # 计算平均成绩、最高成绩、最低成绩\n", "    avg_score = my_scores.mean(axis=1).iloc[0]\n", "    max_score = my_scores.max(axis=1).iloc[0]\n", "    min_score = my_scores.min(axis=1).iloc[0]\n", "    \n", "    print(f\"\\n本人成绩统计：\")\n", "    print(f\"平均成绩: {avg_score:.2f}\")\n", "    print(f\"最高成绩: {max_score:.2f}\")\n", "    print(f\"最低成绩: {min_score:.2f}\")\n"], "id": "2bcd19e87fe19ed5", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学号为 202456242 的学生信息：\n", "学号/工号    202456242\n", "姓名             胡灏迪\n", "Name: 39, dtype: object\n", "\n", "所有列名：\n", "['学号/工号', '姓名', '院系', '专业', '班级_x', '学校代码', '学校', '班级_y', '第二章Python程序基本语法课后作业', '第三章高级数据类型课后作业', '第四章Python条件语句课后作业', '第五章Python循环语句课后作业', '第六章函数课后作业', '第七章Pandas数据结构课后作业', '第八章数据的增删改查课后作业', '第九章数据清洗课后作业', '第十章数据统计分析课后作业', '第十一章数据可视化课后作业']\n", "\n", "作业成绩列：\n", "['第二章Python程序基本语法课后作业', '第三章高级数据类型课后作业', '第四章Python条件语句课后作业', '第五章Python循环语句课后作业', '第六章函数课后作业', '第七章Pandas数据结构课后作业', '第八章数据的增删改查课后作业', '第九章数据清洗课后作业', '第十章数据统计分析课后作业', '第十一章数据可视化课后作业']\n", "\n", "本人各次作业成绩：\n", "    第二章Python程序基本语法课后作业  第三章高级数据类型课后作业  第四章Python条件语句课后作业  第五章Python循环语句课后作业  \\\n", "39                 62.0           92.8               68.0               69.0   \n", "\n", "    第六章函数课后作业  第七章Pandas数据结构课后作业  第八章数据的增删改查课后作业  第九章数据清洗课后作业  第十章数据统计分析课后作业  \\\n", "39       94.0               70.0            70.0         85.0           70.0   \n", "\n", "    第十一章数据可视化课后作业  \n", "39           98.0  \n", "\n", "本人成绩统计：\n", "平均成绩: 77.88\n", "最高成绩: 98.00\n", "最低成绩: 62.00\n"]}], "execution_count": 119}, {"metadata": {}, "cell_type": "markdown", "source": ["# ## 2. 班级学习数据分析\n", "# ### （1）分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少"], "id": "ebef976a9bff0508"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:39.400216Z", "start_time": "2025-06-20T14:30:39.384707Z"}}, "cell_type": "code", "source": ["# 获取学生所在的班级\n", "my_class = my_data['班级_x'].iloc[0]\n", "print(f\"学生 {my_data['姓名'].iloc[0]} 所在的班级是: {my_class}\")\n", "\n", "# 筛选出同一班级的学生\n", "class_students = merged_data[merged_data['班级_x'] == my_class]\n", "print(f\"班级 {my_class} 共有 {len(class_students)} 名学生\")\n", "\n", "# 注意：score_columns已在前面定义，排除了学号/工号和学校代码列\n", "# 计算每个学生的平均分\n", "student_avg_scores = class_students[score_columns].mean(axis=1)\n", "class_students['平均分'] = student_avg_scores\n", "merged_data.loc[class_students.index, '平均分'] = student_avg_scores\n", "\n", "# 查看班级平均分的统计信息\n", "class_avg = student_avg_scores.mean()\n", "class_max = student_avg_scores.max()\n", "class_min = student_avg_scores.min()\n", "\n", "print(\"班级成绩统计：\")\n", "print(f\"班级平均分: {class_avg:.2f}\")\n", "print(f\"班级最高成绩: {class_max:.2f}\")\n", "print(f\"班级最低成绩: {class_min:.2f}\")\n"], "id": "d4d68f642ce3e7be", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学生 胡灏迪 所在的班级是: 2024审计学（注会班）\n", "班级 2024审计学（注会班） 共有 44 名学生\n", "班级成绩统计：\n", "班级平均分: 80.88\n", "班级最高成绩: 91.05\n", "班级最低成绩: 56.13\n"]}], "execution_count": 120}, {"metadata": {}, "cell_type": "markdown", "source": "# ### （2）查找成绩优秀或需要辅导的学生（top10和tail10）", "id": "ce13cf6f4fff8243"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:43.647961Z", "start_time": "2025-06-20T14:30:43.635106Z"}}, "cell_type": "code", "source": ["# 按平均分排序，找出班级内成绩最好的前10名学生（或全部学生，如果班级人数少于10人）\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "print(f\"班级 {my_class} 成绩优秀的前{top_n}名学生：\")\n", "print(top_students[['学号/工号', '姓名', '平均分']])\n", "\n", "# 找出班级内成绩最差的后10名学生（或全部学生，如果班级人数少于10人）\n", "bottom_students = class_students.sort_values(by='平均分').head(top_n)\n", "print(f\"\\n班级 {my_class} 需要辅导的后{top_n}名学生：\")\n", "print(bottom_students[['学号/工号', '姓名', '平均分']])\n"], "id": "ba3d94c2a8317575", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024审计学（注会班） 成绩优秀的前10名学生：\n", "        学号/工号   姓名    平均分\n", "13  202455092   韩月  91.05\n", "11  202455088  赵曼如  89.43\n", "32  202456152  杨宇婷  89.15\n", "8   202455063  刘添祺  88.92\n", "24  202456096   卢婷  88.70\n", "6   202455053  胡耀天  88.60\n", "29  202456136  刘彦伶  87.55\n", "26  202456104  吴紫睿  87.42\n", "49  202456305  张艺菲  87.25\n", "57  202456364  李昊宇  87.13\n", "\n", "班级 2024审计学（注会班） 需要辅导的后10名学生：\n", "        学号/工号   姓名    平均分\n", "14  202455156  王楚涵  56.13\n", "21  202456057  宋昊辰  58.11\n", "22  202456091  李欣恬  58.45\n", "2   202455008   郭晟  62.73\n", "65  202456419   吴晓  67.48\n", "45  202456276   程煜  70.95\n", "53  202456323  黄思源  72.77\n", "3   202455017   李优  74.64\n", "9   202455079  闫雨欣  77.25\n", "1   202455005  丁昱楠  77.75\n"]}], "execution_count": 121}, {"metadata": {}, "cell_type": "markdown", "source": "# ### （3）分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布", "id": "82412a35da686cd5"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:30:48.412995Z", "start_time": "2025-06-20T14:30:48.399013Z"}}, "cell_type": "code", "source": ["# 定义分数段\n", "def get_score_range(score):\n", "    if score < 60:\n", "        return '60分以下'\n", "    elif 60 <= score < 70:\n", "        return '60-69分'\n", "    elif 70 <= score < 80:\n", "        return '70-79分'\n", "    elif 80 <= score < 90:\n", "        return '80-89分'\n", "    else:\n", "        return '90分以上'\n", "\n", "# 应用分数段函数到班级学生\n", "class_students['分数段'] = class_students['平均分'].apply(get_score_range)\n", "merged_data.loc[class_students.index, '分数段'] = class_students['分数段']\n", "\n", "# 统计班级内各分数段的学生人数\n", "score_range_counts = class_students['分数段'].value_counts().sort_index()\n", "print(f\"班级 {my_class} 各分数段学生人数分布：\")\n", "print(score_range_counts)\n", "\n", "# 计算班级内各分数段的百分比\n", "score_range_percentage = score_range_counts / len(class_students) * 100\n", "print(f\"\\n班级 {my_class} 各分数段学生人数百分比：\")\n", "for range_name, percentage in score_range_percentage.items():\n", "    print(f\"{range_name}: {percentage:.2f}%\")\n"], "id": "c97088909fcdb207", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024审计学（注会班） 各分数段学生人数分布：\n", "分数段\n", "60-69分     2\n", "60分以下      3\n", "70-79分     8\n", "80-89分    30\n", "90分以上      1\n", "Name: count, dtype: int64\n", "\n", "班级 2024审计学（注会班） 各分数段学生人数百分比：\n", "60-69分: 4.55%\n", "60分以下: 6.82%\n", "70-79分: 18.18%\n", "80-89分: 68.18%\n", "90分以上: 2.27%\n"]}], "execution_count": 122}, {"metadata": {}, "cell_type": "markdown", "source": ["# ## 3. 数据可视化\n", "# 使用matplotlib库对分析结果进行可视化展示"], "id": "8fffd8dab52e3895"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:33:15.647410Z", "start_time": "2025-06-20T14:33:13.081609Z"}}, "cell_type": "code", "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "from matplotlib.gridspec import GridSpec\n", "\n", "# 设置中文字体，避免中文显示乱码\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n", "\n", "# 1. 绘制班级成绩Top10的柱状图（单独显示）\n", "plt.figure(figsize=(12, 6))\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "bars = plt.bar(top_students['姓名'], top_students['平均分'], color='skyblue')\n", "plt.title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "plt.xlabel('学生姓名')\n", "plt.ylabel('平均分')\n", "plt.ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.xticks(rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "            \n", "plt.tight_layout()\n", "plt.savefig('班级成绩Top10柱状图.png')\n", "plt.show()\n", "\n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）（单独显示）\n", "plt.figure(figsize=(12, 6))\n", "my_scores_values = my_scores.iloc[0].values\n", "# 假设作业是按时间顺序排列的，如果不是，需要调整顺序\n", "plt.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "plt.title(f'学号 {my_student_id} ({my_data[\"姓名\"].iloc[0]}) 的成绩趋势图')\n", "plt.xlabel('作业序号')\n", "plt.ylabel('分数')\n", "plt.xticks(range(len(score_columns)), [f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "plt.grid(True)\n", "plt.ylim(0, 100)  # 设置y轴范围为0-100\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    plt.text(i, score + 2, f'{score:.1f}', ha='center')\n", "    \n", "plt.tight_layout()\n", "plt.savefig('个人成绩趋势图.png')\n", "plt.show()\n", "\n", "# 3. 绘制本班级不同分数段比例图（饼图）（单独显示）\n", "plt.figure(figsize=(10, 8))\n", "# 确保分数段是按照正确顺序排列的\n", "ordered_ranges = ['60分以下', '60-69分', '70-79分', '80-89分', '90分以上']\n", "ordered_counts = [score_range_counts.get(range_name, 0) for range_name in ordered_ranges]\n", "\n", "# 定义饼图颜色\n", "colors = ['#FF9999', '#FFCC99', '#FFFF99', '#99FF99', '#99CCFF']\n", "explode = (0.1, 0, 0, 0, 0)  # 突出显示60分以下的部分\n", "\n", "wedges, texts, autotexts = plt.pie(\n", "    ordered_counts, \n", "    labels=ordered_ranges, \n", "    autopct='%1.1f%%', \n", "    startangle=90, \n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=True\n", ")\n", "plt.title(f'班级 {my_class} 不同分数段学生比例')\n", "plt.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "plt.setp(autotexts, size=10, weight=\"bold\")\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级分数段比例饼图.png')\n", "plt.show()\n", "\n", "# 4. 按班级分组，绘制两个班的平均成绩对比图（单独显示）\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 获取所有班级\n", "all_classes = merged_data['班级_x'].unique()\n", "class_avg_scores = []\n", "\n", "# 计算每个班级的平均分\n", "for cls in all_classes:\n", "    cls_students = merged_data[merged_data['班级_x'] == cls]\n", "    cls_avg = cls_students[score_columns].mean().mean()\n", "    class_avg_scores.append({'班级': cls, '平均分': cls_avg})\n", "\n", "# 创建班级平均分数据框\n", "class_avg_df = pd.DataFrame(class_avg_scores)\n", "\n", "# 绘制班级平均分对比条形图\n", "colors = ['#66B2FF' if cls != my_class else '#FF9900' for cls in class_avg_df['班级']]\n", "bars = plt.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "plt.title('不同班级平均成绩对比')\n", "plt.xlabel('班级')\n", "plt.ylabel('平均分')\n", "plt.ylim(0, 100)\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级平均成绩对比图.png')\n", "plt.show()\n", "\n", "# 5. 学生在班级中的排名位置图（单独显示）\n", "plt.figure(figsize=(12, 6))\n", "\n", "# 获取班级所有学生的平均分并排序\n", "all_class_scores = class_students['平均分'].sort_values(ascending=False).reset_index(drop=True)\n", "all_class_scores.index = all_class_scores.index + 1  # 从1开始计数\n", "\n", "# 找到学生本人在班级中的排名\n", "my_rank = class_students.sort_values(by='平均分', ascending=False)['学号/工号'].tolist().index(my_student_id) + 1\n", "my_score = class_students[class_students['学号/工号'] == my_student_id]['平均分'].iloc[0]\n", "\n", "# 绘制班级所有学生的成绩分布\n", "plt.plot(all_class_scores.index, all_class_scores.values, 'o-', color='gray', alpha=0.5)\n", "plt.title(f'学生 {my_data[\"姓名\"].iloc[0]} 在班级 {my_class} 中的排名位置')\n", "plt.xlabel('排名')\n", "plt.ylabel('平均分')\n", "plt.grid(True)\n", "\n", "# 标记学生本人的位置\n", "plt.plot(my_rank, my_score, 'ro', markersize=10)\n", "plt.annotate(f'我的排名: 第{my_rank}名\\n平均分: {my_score:.2f}',\n", "            xy=(my_rank, my_score),\n", "            xytext=(my_rank+5, my_score),\n", "            arrowprops=dict(facecolor='black', shrink=0.05, width=1.5))\n", "\n", "plt.tight_layout()\n", "plt.savefig('学生班级排名位置图.png')\n", "plt.show()\n", "\n", "# 6. 将所有图表整合到一张完整的图纸中\n", "plt.figure(figsize=(20, 15))\n", "gs = GridSpec(3, 2, figure=plt.gcf())\n", "\n", "# 1. 绘制班级成绩Top10的柱状图\n", "ax1 = plt.subplot(gs[0, 0])\n", "bars = ax1.bar(top_students['姓名'], top_students['平均分'], color='skyblue')\n", "ax1.set_title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "ax1.set_xlabel('学生姓名')\n", "ax1.set_ylabel('平均分')\n", "ax1.set_ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "ax1.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "\n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）\n", "ax2 = plt.subplot(gs[0, 1])\n", "ax2.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "ax2.set_title(f'学号 {my_student_id} ({my_data[\"姓名\"].iloc[0]}) 的成绩趋势图')\n", "ax2.set_xlabel('作业序号')\n", "ax2.set_ylabel('分数')\n", "ax2.set_xticks(range(len(score_columns)))\n", "ax2.set_xticklabels([f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "ax2.grid(True)\n", "ax2.set_ylim(0, 100)  # 设置y轴范围为0-100\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    ax2.text(i, score + 2, f'{score:.1f}', ha='center')\n", "\n", "# 3. 绘制本班级不同分数段比例图（饼图）\n", "ax3 = plt.subplot(gs[1, 0])\n", "wedges, texts, autotexts = ax3.pie(\n", "    ordered_counts, \n", "    labels=ordered_ranges, \n", "    autopct='%1.1f%%', \n", "    startangle=90, \n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=True\n", ")\n", "ax3.set_title(f'班级 {my_class} 不同分数段学生比例')\n", "ax3.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "plt.setp(autotexts, size=10, weight=\"bold\")\n", "\n", "# 4. 按班级分组，绘制两个班的平均成绩对比图\n", "ax4 = plt.subplot(gs[1, 1])\n", "bars = ax4.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "ax4.set_title('不同班级平均成绩对比')\n", "ax4.set_xlabel('班级')\n", "ax4.set_ylabel('平均分')\n", "ax4.set_ylim(0, 100)\n", "ax4.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "# 5. 添加学生在班级中的排名位置图\n", "ax5 = plt.subplot(gs[2, :])\n", "ax5.plot(all_class_scores.index, all_class_scores.values, 'o-', color='gray', alpha=0.5)\n", "ax5.set_title(f'学生 {my_data[\"姓名\"].iloc[0]} 在班级 {my_class} 中的排名位置')\n", "ax5.set_xlabel('排名')\n", "ax5.set_ylabel('平均分')\n", "ax5.grid(True)\n", "\n", "# 添加总标题\n", "plt.suptitle('学生成绩分析报告', fontsize=20, fontweight='bold')\n", "\n", "# 调整子图布局\n", "plt.tight_layout(rect=[0, 0, 1, 0.96])  # 为顶部标题留出空间\n", "\n", "# 保存整合后的图表\n", "plt.savefig('学生成绩分析报告.png', dpi=300, bbox_inches='tight')\n", "plt.show() "], "id": "d0779f2f4a21a063", "outputs": [], "execution_count": 124}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "e13ff4ce9bd44e1d"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}