import sys
import pygame
from time import sleep
import random

from settings import Settings
from game_stats import GameStats
from scoreboard import Scoreboard
from button import But<PERSON>
from ship import Ship
from bullet import Bullet
from alien import Alien

class AlienInvasion:
    """管理游戏资源和行为的类"""

    def __init__(self):
        """初始化游戏并创建游戏资源"""
        pygame.init()
        self.settings = Settings()

        self.screen = pygame.display.set_mode(
            (self.settings.screen_width, self.settings.screen_height))
        pygame.display.set_caption("星际守护者")  # 更改游戏名称

        # 创建一个用于存储游戏统计信息的实例
        self.stats = GameStats(self)
        self.sb = Scoreboard(self)

        self.ship = Ship(self)
        self.bullets = pygame.sprite.Group()
        self.aliens = pygame.sprite.Group()
        
        # 创建星星背景
        self.stars = self._create_stars(100)  # 100颗星星

        # 用于控制自动射击的计时器
        self.last_shot_time = pygame.time.get_ticks()
        
        # 用于控制随机生成外星人的计时器
        self.last_alien_spawn_time = pygame.time.get_ticks()

        # 创建Play按钮
        self.play_button = Button(self, "开始游戏")
        
        # 是否暂停
        self.paused = False
        
        # 游戏结束状态
        self.game_over = False
        self.game_over_time = 0
        
        # 为中文显示创建字体
        try:
            self.pause_font = pygame.font.SysFont('microsoftyahei', 48)
            self.game_over_font = pygame.font.SysFont('microsoftyahei', 64)
            self.score_font = pygame.font.SysFont('microsoftyahei', 36)
            self.instructions_font = pygame.font.SysFont('microsoftyahei', 24)
        except:
            # 如果找不到微软雅黑，就使用默认字体
            self.pause_font = pygame.font.SysFont(None, 48)
            self.game_over_font = pygame.font.SysFont(None, 64)
            self.score_font = pygame.font.SysFont(None, 36)
            self.instructions_font = pygame.font.SysFont(None, 24)
            
    def _create_stars(self, count):
        """创建星星背景"""
        stars = []
        for _ in range(count):
            x = random.randint(0, self.settings.screen_width)
            y = random.randint(0, self.settings.screen_height)
            size = random.randint(1, 3)
            brightness = random.randint(150, 255)
            color = (brightness, brightness, brightness)
            stars.append((x, y, size, color))
        return stars

    def run_game(self):
        """开始游戏的主循环"""
        while True:
            self._check_events()
            
            if self.stats.game_active and not self.paused:
                self.ship.update()
                self._update_bullets()
                self._update_aliens()
                self._check_auto_fire()
                self._check_spawn_alien()
            
            self._update_screen()

    def _check_events(self):
        """响应按键和鼠标事件"""
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                sys.exit()
            elif event.type == pygame.KEYDOWN:
                self._check_keydown_events(event)
            elif event.type == pygame.KEYUP:
                self._check_keyup_events(event)
            elif event.type == pygame.MOUSEBUTTONDOWN:
                mouse_pos = pygame.mouse.get_pos()
                self._check_play_button(mouse_pos)
                
    def _check_auto_fire(self):
        """自动发射子弹"""
        current_time = pygame.time.get_ticks()
        # 检查是否达到发射间隔时间
        if current_time - self.last_shot_time > self.settings.auto_fire_interval:
            self._fire_bullet()
            self.last_shot_time = current_time
            
    def _check_spawn_alien(self):
        """随机生成外星人"""
        current_time = pygame.time.get_ticks()
        # 检查是否达到生成新外星人的时间，并且控制外星人数量
        if (current_time - self.last_alien_spawn_time > self.settings.alien_spawn_interval 
                and len(self.aliens) < self.settings.max_aliens):
            self._spawn_random_alien()
            self.last_alien_spawn_time = current_time

    def _check_play_button(self, mouse_pos):
        """在玩家单击Play按钮时开始新游戏"""
        button_clicked = self.play_button.rect.collidepoint(mouse_pos)
        if button_clicked and not self.stats.game_active:
            # 重置游戏设置
            self.settings.initialize_dynamic_settings()
            
            # 重置游戏统计信息
            self.stats.reset_stats()
            self.stats.game_active = True
            self.sb.prep_score()
            self.sb.prep_level()
            self.sb.prep_ships()
            
            # 清空外星人列表和子弹列表
            self.aliens.empty()
            self.bullets.empty()
            
            # 让飞船居中
            self.ship.center_ship()
            
            # 重置计时器
            self.last_shot_time = pygame.time.get_ticks()
            self.last_alien_spawn_time = pygame.time.get_ticks()
            
            # 重置游戏结束状态
            self.game_over = False
            
            # 隐藏鼠标光标
            pygame.mouse.set_visible(False)

    def _check_keydown_events(self, event):
        """响应按键"""
        if event.key == pygame.K_RIGHT:
            self.ship.moving_right = True
        elif event.key == pygame.K_LEFT:
            self.ship.moving_left = True
        elif event.key == pygame.K_q:
            sys.exit()
        elif event.key == pygame.K_p:
            if not self.stats.game_active:
                self._check_play_button(self.play_button.rect.center)
            else:
                self.paused = not self.paused

    def _check_keyup_events(self, event):
        """响应松开"""
        if event.key == pygame.K_RIGHT:
            self.ship.moving_right = False
        elif event.key == pygame.K_LEFT:
            self.ship.moving_left = False

    def _fire_bullet(self):
        """创建一颗子弹，并将其加入到编组bullets中"""
        if len(self.bullets) < self.settings.bullets_allowed and self.stats.game_active and not self.paused:
            new_bullet = Bullet(self)
            self.bullets.add(new_bullet)

    def _spawn_random_alien(self):
        """在屏幕顶部随机位置创建一个外星人"""
        alien = Alien(self)
        
        # 设置随机水平位置
        alien_width = alien.rect.width
        available_space_x = self.settings.screen_width - (2 * alien_width)
        alien.x = random.randint(alien_width, available_space_x)
        alien.rect.x = alien.x
        
        # 将外星人放在屏幕顶部附近
        alien.rect.y = alien.rect.height
        
        # 添加到外星人组中
        self.aliens.add(alien)

    def _update_bullets(self):
        """更新子弹的位置并删除消失的子弹"""
        # 更新子弹的位置
        self.bullets.update()

        # 删除消失的子弹
        for bullet in self.bullets.copy():
            if bullet.rect.bottom <= 0:
                self.bullets.remove(bullet)

        self._check_bullet_alien_collisions()

    def _check_bullet_alien_collisions(self):
        """响应子弹和外星人碰撞"""
        # 删除发生碰撞的子弹和外星人
        collisions = pygame.sprite.groupcollide(
            self.bullets, self.aliens, True, True)
            
        if collisions:
            for aliens in collisions.values():
                self.stats.score += self.settings.alien_points * len(aliens)
            self.sb.prep_score()
            self.sb.check_high_score()

        # 检查是否进入下一关
        if self.stats.score > 0 and self.stats.score % self.settings.level_up_score == 0:
            # 提高等级
            self.settings.increase_speed()
            self.stats.level += 1
            self.sb.prep_level()

    def _update_aliens(self):
        """更新外星人位置"""
        self.aliens.update()
        
        # 检测外星人和飞船之间的碰撞
        if pygame.sprite.spritecollideany(self.ship, self.aliens):
            self._ship_hit()
            
        # 检查是否有外星人到达了屏幕底端
        self._check_aliens_bottom()

    def _ship_hit(self):
        """响应飞船被外星人撞到"""
        if self.stats.ships_left > 0:
            # 将ships_left减1并更新记分牌
            self.stats.ships_left -= 1
            self.sb.prep_ships()
            
            # 清空外星人列表和子弹列表
            self.aliens.empty()
            self.bullets.empty()
            
            # 将飞船放到屏幕底端的中央
            self.ship.center_ship()
            
            # 暂停
            sleep(0.5)
        else:
            self.stats.game_active = False
            pygame.mouse.set_visible(True)
            
            # 设置游戏结束状态和时间戳
            self.game_over = True
            self.game_over_time = pygame.time.get_ticks()

    def _check_aliens_bottom(self):
        """检查是否有外星人到达了屏幕底端"""
        screen_rect = self.screen.get_rect()
        for alien in self.aliens.sprites():
            if alien.rect.bottom >= screen_rect.bottom:
                # 像飞船被撞到一样处理
                self._ship_hit()
                break

    def _update_screen(self):
        """更新屏幕上的图像，并切换到新屏幕"""
        self.screen.fill(self.settings.bg_color)
        
        # 绘制星星背景
        for star in self.stars:
            x, y, size, color = star
            pygame.draw.circle(self.screen, color, (x, y), size)
            
        self.ship.blitme()
        for bullet in self.bullets.sprites():
            bullet.draw_bullet()
        self.aliens.draw(self.screen)
        
        # 显示得分
        self.sb.show_score()
        
        # 如果游戏处于非活动状态，就绘制Play按钮
        if not self.stats.game_active:
            self.play_button.draw_button()
            
            # 显示游戏说明
            instructions1 = self.instructions_font.render("左右方向键控制移动，飞船会自动射击", True, (255, 255, 255))
            instructions2 = self.instructions_font.render("按P暂停游戏，按Q退出游戏", True, (255, 255, 255))
            inst1_rect = instructions1.get_rect()
            inst2_rect = instructions2.get_rect()
            inst1_rect.centerx = self.screen.get_rect().centerx
            inst2_rect.centerx = self.screen.get_rect().centerx
            inst1_rect.top = self.play_button.rect.bottom + 20
            inst2_rect.top = inst1_rect.bottom + 10
            self.screen.blit(instructions1, inst1_rect)
            self.screen.blit(instructions2, inst2_rect)
            
            # 如果游戏结束，显示游戏结束信息和最终得分
            if self.game_over:
                # 创建游戏结束文本
                game_over_image = self.game_over_font.render("游戏结束", True, (255, 0, 0))
                game_over_rect = game_over_image.get_rect()
                game_over_rect.centerx = self.screen.get_rect().centerx
                game_over_rect.centery = self.screen.get_rect().centery - 60
                
                # 创建最终得分文本
                final_score_str = f"最终得分: {self.stats.score}"
                final_score_image = self.score_font.render(final_score_str, True, (255, 255, 255))
                final_score_rect = final_score_image.get_rect()
                final_score_rect.centerx = self.screen.get_rect().centerx
                final_score_rect.centery = game_over_rect.centery + 60
                
                # 绘制文本
                self.screen.blit(final_score_image, final_score_rect)
                
                # 让游戏结束文本闪烁
                current_time = pygame.time.get_ticks()
                if (current_time - self.game_over_time) % 1000 < 500:
                    self.screen.blit(game_over_image, game_over_rect)
            
        # 如果游戏暂停，显示暂停文本
        if self.paused:
            pause_image = self.pause_font.render("已暂停 - 按P继续", True, (255, 0, 0), self.settings.bg_color)
            pause_rect = pause_image.get_rect()
            pause_rect.center = self.screen.get_rect().center
            self.screen.blit(pause_image, pause_rect)
            
        pygame.display.flip()

if __name__ == '__main__':
    # 创建游戏实例并运行游戏
    ai = AlienInvasion()
    ai.run_game() 