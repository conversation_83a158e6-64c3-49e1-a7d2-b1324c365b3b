import pygame
import os
import math

def create_ship_image(color=(60, 60, 200)):
    """创建更精美的飞船图像"""
    width, height = 60, 60
    image = pygame.Surface((width, height), pygame.SRCALPHA)
    
    # 飞船主体（高级三角形飞船）
    ship_color = color
    highlight_color = (min(color[0] + 70, 255), min(color[1] + 70, 255), min(color[2] + 70, 255))
    shadow_color = (max(color[0] - 40, 0), max(color[1] - 40, 0), max(color[2] - 40, 0))
    
    # 飞船主体
    points = [(30, 0), (0, 50), (60, 50)]
    pygame.draw.polygon(image, ship_color, points)
    
    # 飞船内部细节 - 机翼
    wing_points1 = [(15, 30), (5, 50), (25, 50)]
    wing_points2 = [(45, 30), (35, 50), (55, 50)]
    pygame.draw.polygon(image, highlight_color, wing_points1)
    pygame.draw.polygon(image, highlight_color, wing_points2)
    
    # 飞船驾驶舱
    cockpit_color = (200, 200, 255)
    pygame.draw.circle(image, cockpit_color, (30, 25), 8)
    pygame.draw.circle(image, shadow_color, (30, 25), 8, 2)  # 边框
    
    # 飞船引擎喷射
    pygame.draw.polygon(image, (255, 140, 0), [(20, 50), (30, 60), (40, 50)])
    pygame.draw.polygon(image, (255, 200, 0), [(25, 50), (30, 55), (35, 50)])
    
    # 增加描边，增强立体感
    pygame.draw.polygon(image, shadow_color, points, 2)
    
    return image

def create_alien_image(color=(200, 60, 60)):
    """创建更精美的外星人飞船图像"""
    width, height = 60, 60
    image = pygame.Surface((width, height), pygame.SRCALPHA)
    
    # 外星人飞船颜色
    alien_color = color
    highlight_color = (min(color[0] + 70, 255), min(color[1] + 70, 255), min(color[2] + 70, 255))
    shadow_color = (max(color[0] - 40, 0), max(color[1] - 40, 0), max(color[2] - 40, 0))
    
    # 外星飞船主体 - 碟形飞船
    pygame.draw.ellipse(image, alien_color, (5, 20, 50, 25))  # 主体椭圆
    
    # 飞船上部 - 圆顶
    pygame.draw.circle(image, highlight_color, (30, 20), 15)
    
    # 飞船下部 - 着陆装置
    pygame.draw.rect(image, shadow_color, (25, 45, 10, 5))
    pygame.draw.rect(image, shadow_color, (15, 42, 5, 8))
    pygame.draw.rect(image, shadow_color, (40, 42, 5, 8))
    
    # 飞船窗口 - 发光效果
    window_color = (160, 255, 160)
    pygame.draw.circle(image, window_color, (30, 20), 8)
    pygame.draw.circle(image, (255, 255, 255), (30, 20), 4)
    
    # 引擎光芒
    for angle in range(0, 360, 45):
        x = 30 + 22 * math.cos(math.radians(angle))
        y = 32 + 10 * math.sin(math.radians(angle))
        pygame.draw.line(image, (255, 255, 100), (30, 32), (x, y), 2)
    
    # 整体添加轮廓
    pygame.draw.ellipse(image, shadow_color, (5, 20, 50, 25), 2)
    pygame.draw.circle(image, shadow_color, (30, 20), 15, 2)
    
    return image

def main():
    # 初始化pygame
    pygame.init()
    
    # 确保images目录存在
    if not os.path.exists('images'):
        os.makedirs('images')
    
    # 创建并保存玩家飞船图像（蓝色）
    ship_image = create_ship_image(color=(30, 90, 255))
    pygame.image.save(ship_image, 'images/ship.bmp')
    
    # 创建并保存敌方飞船图像（红色）
    alien_image = create_alien_image(color=(255, 60, 60))
    pygame.image.save(alien_image, 'images/alien.bmp')
    
    print("图像文件已创建完成！")
    pygame.quit()

if __name__ == '__main__':
    main() 