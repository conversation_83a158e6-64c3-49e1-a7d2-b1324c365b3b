import numpy as np
import pandas as pd

# 步骤1：读取数据并将缺失值替换为0
def load_data_with_zeros(file_path):
    # 读取CSV文件，将NA值解析为NaN
    data = pd.read_csv(file_path, header=None, na_values='NA')
    # 将NaN替换为0
    data_filled = data.fillna(0)
    return data_filled.values

# 步骤2：对X进行特异值分解
def perform_svd(X):
    # 执行完整的SVD分解
    U, sigma, Vt = np.linalg.svd(X, full_matrices=False)
    # 返回分解结果和对角矩阵
    return U, sigma, Vt

# 步骤3：使用前5个特异值重构数据
def reconstruct_with_top_k(U, sigma, Vt, k=5):
    # 仅使用前k个特异值和对应的向量
    U_k = U[:, :k]
    sigma_k = sigma[:k]
    Vt_k = Vt[:k, :]
    
    # 重构矩阵
    X_hat = U_k @ np.diag(sigma_k) @ Vt_k
    return X_hat

# 步骤4：计算均方误差
def calculate_mse(X_true, X_pred, missing_mask):
    # 只考虑缺失值的位置
    squared_errors = (X_true[missing_mask] - X_pred[missing_mask]) ** 2
    mse = np.mean(squared_errors)
    return mse

# 主函数
def main():
    # 读取数据
    X_missing = load_data_with_zeros('data_missing.csv')
    X_full = load_data_with_zeros('data_full.csv')
    
    # 创建缺失值掩码（True表示缺失值的位置）
    missing_mask = pd.read_csv('data_missing.csv', header=None, na_values='NA').isna().values
    
    # 对填充了0的缺失数据矩阵进行SVD分解
    U, sigma, Vt = perform_svd(X_missing)
    
    # 使用前5个特异值重构数据
    X_hat = reconstruct_with_top_k(U, sigma, Vt, k=5)
    
    # 计算使用0填充时的MSE
    mse_zeros = calculate_mse(X_full, X_missing, missing_mask)
    print(f"使用0填充的均方误差: {mse_zeros:.2f}")
    
    # 计算使用SVD重构的MSE
    mse_svd = calculate_mse(X_full, X_hat, missing_mask)
    print(f"使用SVD重构的均方误差: {mse_svd:.2f}")
    
    # 比较结果
    print(f"SVD重构相比简单填充0改进了: {((mse_zeros - mse_svd) / mse_zeros) * 100:.2f}%")

if __name__ == "__main__":
    main() 