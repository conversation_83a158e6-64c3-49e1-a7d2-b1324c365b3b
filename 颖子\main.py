from flowmeter_model import UltrasonicFlowmeter
import math
import random

def simulate_transit_times(flowmeter, true_velocity, true_temperature):
    """
    根据真实的流速和温度，反向模拟出顺流和逆流的传播时间。
    """
    # 获取在该温度下水中的真实声速
    true_sound_speed = flowmeter.get_sound_speed_in_water(true_temperature)
    
    # 根据公式 t = L / (c ± v*sin(θ)) 计算理论时间
    sin_theta = math.sin(flowmeter.theta)
    t_ab_perfect = flowmeter.L / (true_sound_speed + true_velocity * sin_theta)
    t_ba_perfect = flowmeter.L / (true_sound_speed - true_velocity * sin_theta)

    # 引入测量噪声：模拟真实世界的不确定性
    # 暂时提高噪声水平，以便更明显地观察温度误差
    noise_level = 20e-9 # 临时从 5e-9 提高到 20e-9
    t_ab_noisy = t_ab_perfect + random.uniform(-noise_level, noise_level)
    t_ba_noisy = t_ba_perfect + random.uniform(-noise_level, noise_level)
    
    return t_ab_noisy, t_ba_noisy

def main():
    """
    主函数，用于演示和验证流量计模型。
    """
    # 1. 初始化流量计参数
    # 假设管道直径为 0.1 米 (100mm)，探头间距为 0.12 米，夹角为 60 度
    flowmeter = UltrasonicFlowmeter(diameter=0.1, path_length=0.12, angle_deg=60)

    # 2. 模拟真实世界场景
    # 假设管道中是水，真实温度为 25°C，真实流速为 1.2 m/s
    TRUE_TEMP = 25.0
    TRUE_VELOCITY = 1.2
    
    # 根据真实情况，计算出理论上的 t_ab 和 t_ba
    t_ab_measured, t_ba_measured = simulate_transit_times(flowmeter, TRUE_VELOCITY, TRUE_TEMP)

    print("--- 场景模拟 ---")
    print(f"真实水温: {TRUE_TEMP}°C")
    print(f"真实流速: {TRUE_VELOCITY:.4f} m/s")
    print(f"模拟测量得到的顺流时间 t_ab: {t_ab_measured:.9f} s")
    print(f"模拟测量得到的逆流时间 t_ba: {t_ba_measured:.9f} s")
    print("-" * 20)
    print()

    # --- 问题 1: 使用理想模型计算 ---
    print("--- 问题 1: 理想模型计算 (假设温度为20°C) ---")
    # 假设我们错误地以为水温是 20°C
    assumed_temp = 20.0
    assumed_sound_speed = flowmeter.get_sound_speed_in_water(assumed_temp)
    time_diff_measured = t_ba_measured - t_ab_measured
    
    # 使用问题1的方法计算流速
    v_problem1 = flowmeter.calculate_velocity_problem1(time_diff_measured, assumed_sound_speed)
    
    print(f"假设声速 (基于 {assumed_temp}°C): {assumed_sound_speed:.4f} m/s")
    print(f"计算得到的流速: {v_problem1:.4f} m/s")
    print(f"与真实流速 ({TRUE_VELOCITY:.4f} m/s) 的误差: {abs(v_problem1 - TRUE_VELOCITY):.4f} m/s")
    print("-" * 20)
    print()

    # --- 问题 2: 使用修正模型计算 ---
    print("--- 问题 2: 修正模型计算 (温度未知) ---")
    
    # a. 首先，根据测量时间估算实际声速
    estimated_c = flowmeter.estimate_sound_speed(t_ab_measured, t_ba_measured)
    print(f"步骤 a: 估算出的实际声速: {estimated_c:.4f} m/s")
    
    # b. 使用修正模型计算流速
    v_problem2 = flowmeter.calculate_velocity_problem2(t_ab_measured, t_ba_measured)
    print(f"步骤 b: 计算得到的修正流速: {v_problem2:.4f} m/s")
    print(f"       与真实流速 ({TRUE_VELOCITY:.4f} m/s) 的误差: {abs(v_problem2 - TRUE_VELOCITY):.4f} m/s")

    # c. 根据估算的声速反推温度
    estimated_temp = flowmeter.estimate_temperature(estimated_c)
    print(f"步骤 c: 估算出的水温: {estimated_temp:.4f}°C")
    print(f"       与真实水温 ({TRUE_TEMP}°C) 的误差: {abs(estimated_temp - TRUE_TEMP):.4f}°C")
    print("-" * 20)
    print()

    # --- 流量计算 ---
    print("--- 流量计算 ---")
    # 使用更准确的速度值进行计算
    total_flow_volume = flowmeter.calculate_flow_volume(v_problem2, time_period=60) # 计算1分钟的流量
    print(f"在 60 秒内，总流量为: {total_flow_volume:.4f} 立方米")
    print("-" * 20)

if __name__ == "__main__":
    main() 