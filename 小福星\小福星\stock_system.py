import os
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
# 设置中文字体
try:
    font = FontProperties(family='SimHei')  # 黑体
except:
    font = FontProperties()

# 全局变量
stock_pool = []  # 存储自选股票池
valid_username = '第11组'
valid_password = '1111'

def login():
    """用户登录功能"""
    max_attempts = 3
    attempts = 0
    
    while attempts < max_attempts:
        print("\n===== 自选股票系统登录 =====")
        username = input("请输入用户名: ")
        password = input("请输入密码: ")
        
        if username == valid_username and password == valid_password:
            print("\n登录成功！欢迎使用自选股票系统。")
            return True
        else:
            attempts += 1
            remaining = max_attempts - attempts
            if remaining > 0:
                print(f"\n登录失败！用户名或密码错误。还剩 {remaining} 次尝试机会。")
            else:
                print("\n登录失败！您已连续3次输入错误，今日无法登录。")
                return False

def add_stock():
    """添加股票到自选股票池"""
    print("\n===== 添加股票 =====")
    stock_name = input("请输入股票名称: ")
    stock_code = input("请输入股票代码: ")
    
    # 检查股票是否已存在
    for stock in stock_pool:
        if stock['code'] == stock_code:
            print(f"\n股票 {stock_name}({stock_code}) 已存在于自选股票池中！")
            return
    
    # 添加股票到自选股票池
    stock_info = {
        'name': stock_name,
        'code': stock_code
    }
    stock_pool.append(stock_info)
    print(f"\n股票 {stock_name}({stock_code}) 已成功添加到自选股票池！")

def show_stock_pool():
    """显示自选股票池"""
    print("\n===== 自选股票池 =====")
    if not stock_pool:
        print("自选股票池为空！")
        return
    
    print(f"{'序号':<5}{'股票名称':<15}{'股票代码':<10}")
    print("-" * 30)
    for i, stock in enumerate(stock_pool, 1):
        print(f"{i:<5}{stock['name']:<15}{stock['code']:<10}")

def load_stock_data(stock_name):
    """加载股票数据"""
    try:
        file_path = f"{stock_name}.csv"
        if not os.path.exists(file_path):
            print(f"错误：找不到股票 {stock_name} 的数据文件！")
            return None
        
        # 读取CSV文件
        df = pd.read_csv(file_path)
        
        # 检查数据是否包含必要的列
        required_columns = ['日期', '收盘', '开盘', '高', '低', '交易', '涨跌幅']
        for col in required_columns:
            if col not in df.columns:
                print(f"错误：数据文件缺少必要的列 '{col}'！")
                return None
        
        return df
    except Exception as e:
        print(f"加载股票数据时出错：{str(e)}")
        return None

def process_stock_data(stock_name):
    """处理股票数据"""
    print(f"\n===== 处理 {stock_name} 股票数据 =====")
    
    # 加载股票数据
    df = load_stock_data(stock_name)
    if df is None:
        return
    
    # 显示数据基本信息
    print("\n数据基本信息:")
    print(f"行数: {df.shape[0]}, 列数: {df.shape[1]}")
    print("\n列名:")
    print(df.columns.tolist())
    
    # 检查是否存在缺失值
    missing_values = df.isnull().sum().sum()
    print(f"\n缺失值数量: {missing_values}")
    
    # 按日期升序排序
    if '日期' in df.columns:
        df['日期'] = pd.to_datetime(df['日期'])
        df = df.sort_values(by='日期')
        print("\n数据已按交易日期升序排序")
    
    # 显示数据前5行
    print("\n数据前5行:")
    print(df.head())
    
    return df

def visualize_stock_data(stock_name, df):
    """可视化股票数据"""
    print(f"\n===== 可视化 {stock_name} 股票数据 =====")
    
    if df is None or df.empty:
        print("没有可用的数据进行可视化！")
        return
    
    # 创建一个包含两个子图的图形
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)
    
    # 绘制收盘价走势图
    ax1.plot(df['日期'], df['收盘'], 'b-', linewidth=2)
    ax1.set_title(f'{stock_name}收盘价走势图 (2025年)', fontproperties=font, fontsize=14)
    ax1.set_ylabel('收盘价', fontproperties=font, fontsize=12)
    ax1.grid(True)
    
    # 绘制成交量柱状图
    ax2.bar(df['日期'], df['交易'], color='g', alpha=0.7)
    ax2.set_title(f'{stock_name}成交量柱状图 (2025年)', fontproperties=font, fontsize=14)
    ax2.set_xlabel('日期', fontproperties=font, fontsize=12)
    ax2.set_ylabel('成交量', fontproperties=font, fontsize=12)
    ax2.grid(True)
    
    # 自动调整布局
    plt.tight_layout()
    
    # 保存图表
    plt.savefig(f"{stock_name}_analysis.png")
    print(f"图表已保存为 {stock_name}_analysis.png")
    
    # 显示图表
    plt.show()
    
    # 分析股票行情
    analyze_stock_trend(stock_name, df)

def analyze_stock_trend(stock_name, df):
    """分析股票行情"""
    print(f"\n===== {stock_name} 2025年行情分析 =====")
    
    # 计算基本统计数据
    start_price = df.iloc[0]['收盘']
    end_price = df.iloc[-1]['收盘']
    price_change = end_price - start_price
    price_change_pct = (price_change / start_price) * 100
    
    max_price = df['收盘'].max()
    min_price = df['收盘'].min()
    avg_price = df['收盘'].mean()
    
    max_volume = df['交易'].max()
    avg_volume = df['交易'].mean()
    
    # 输出分析结果
    print(f"\n起始价格: {start_price:.2f}")
    print(f"结束价格: {end_price:.2f}")
    print(f"价格变化: {price_change:.2f} ({price_change_pct:.2f}%)")
    print(f"最高价格: {max_price:.2f}")
    print(f"最低价格: {min_price:.2f}")
    print(f"平均价格: {avg_price:.2f}")
    print(f"最大成交量: {max_volume:.2f}")
    print(f"平均成交量: {avg_volume:.2f}")
    
    # 趋势分析
    if price_change > 0:
        trend = "上升"
        if price_change_pct > 10:
            strength = "强劲"
        else:
            strength = "温和"
    elif price_change < 0:
        trend = "下降"
        if price_change_pct < -10:
            strength = "明显"
        else:
            strength = "轻微"
    else:
        trend = "平稳"
        strength = ""
    
    print(f"\n总体趋势: {trend}{strength}")
    
    # 波动性分析
    price_std = df['收盘'].std()
    price_volatility = (price_std / avg_price) * 100
    print(f"价格波动率: {price_volatility:.2f}%")
    
    if price_volatility > 5:
        print("市场波动较大，风险较高")
    else:
        print("市场相对稳定，波动较小")

def create_stock_pool():
    """创建自选股票池"""
    print("\n===== 创建自选股票池 =====")
    
    # 清空现有股票池
    stock_pool.clear()
    
    # 每位组员添加一支股票
    num_members = int(input("请输入小组成员数量: "))
    
    for i in range(num_members):
        print(f"\n请第 {i+1} 位组员添加股票:")
        add_stock()
    
    print("\n自选股票池创建完成！")
    show_stock_pool()

def main_menu():
    """主菜单"""
    while True:
        print("\n===== 自选股票系统主菜单 =====")
        print("1. 创建自选股票池")
        print("2. 添加股票")
        print("3. 查看自选股票池")
        print("4. 处理股票数据")
        print("5. 可视化股票数据")
        print("0. 退出系统")
        
        choice = input("\n请输入您的选择: ")
        
        if choice == '1':
            create_stock_pool()
        elif choice == '2':
            add_stock()
        elif choice == '3':
            show_stock_pool()
        elif choice == '4':
            if not stock_pool:
                print("\n自选股票池为空！请先添加股票。")
                continue
            
            show_stock_pool()
            stock_idx = int(input("\n请选择要处理的股票序号: ")) - 1
            
            if 0 <= stock_idx < len(stock_pool):
                selected_stock = stock_pool[stock_idx]
                process_stock_data(selected_stock['name'])
            else:
                print("\n无效的股票序号！")
        elif choice == '5':
            if not stock_pool:
                print("\n自选股票池为空！请先添加股票。")
                continue
            
            show_stock_pool()
            stock_idx = int(input("\n请选择要可视化的股票序号: ")) - 1
            
            if 0 <= stock_idx < len(stock_pool):
                selected_stock = stock_pool[stock_idx]
                df = process_stock_data(selected_stock['name'])
                if df is not None:
                    visualize_stock_data(selected_stock['name'], df)
            else:
                print("\n无效的股票序号！")
        elif choice == '0':
            print("\n感谢使用自选股票系统，再见！")
            break
        else:
            print("\n无效的选择，请重新输入！")

def main():
    """主函数"""
    print("欢迎使用自选股票系统！")
    
    # 用户登录
    if login():
        # 登录成功，显示主菜单
        main_menu()

if __name__ == "__main__":
    main() 