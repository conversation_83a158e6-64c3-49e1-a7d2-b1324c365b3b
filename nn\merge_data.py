import os
import pandas as pd
import numpy as np
import glob
import matplotlib.pyplot as plt
from matplotlib.font_manager import FontProperties
import datetime
import seaborn as sns

# 合并所有chunk开头的CSV文件
def merge_csv_files():
    print("开始合并CSV文件...")
    # 获取所有chunk开头的CSV文件
    csv_files = glob.glob("chunk_*.csv")
    print(f"找到{len(csv_files)}个CSV文件: {csv_files}")
    
    # 读取并合并CSV文件
    dfs = []
    for file in csv_files:
        print(f"正在处理文件: {file}")
        # 使用chunksize来处理大文件，避免内存不足
        for chunk in pd.read_csv(file, chunksize=100000):
            dfs.append(chunk)
    
    # 合并所有数据帧
    merged_df = pd.concat(dfs, ignore_index=True)
    print(f"合并完成，总行数: {len(merged_df)}")
    
    return merged_df

# 读取轨道站点信息
def read_station_info():
    print("读取轨道站点信息...")
    # 修改这里，使用GBK编码读取文件
    station_df = pd.read_csv("轨道站点.txt", sep='\t', encoding='gbk')
    # 将站点编码转换为字符串格式，确保与CSV文件中的LINE_NO格式一致
    station_df['站点编码'] = station_df['站点编码'].astype(str).str.zfill(4)
    print(f"读取了{len(station_df)}个站点信息")
    return station_df

# 合并数据并添加站点名称
def merge_data():
    # 合并CSV文件
    merged_df = merge_csv_files()
    
    # 读取站点信息
    station_df = read_station_info()
    
    # 确保LINE_NO是字符串格式，并填充为4位数
    merged_df['LINE_NO'] = merged_df['LINE_NO'].astype(str).str.zfill(4)
    
    # 使用LINE_NO与站点编码进行合并，添加站点名称
    print("添加站点名称信息...")
    result_df = pd.merge(
        merged_df, 
        station_df, 
        left_on='LINE_NO', 
        right_on='站点编码', 
        how='left'
    )
    
    print(f"合并后的数据行数: {len(result_df)}")
    
    # 保存结果到新的CSV文件
    output_file = "merged_data_with_stations.csv"
    print(f"保存结果到文件: {output_file}")
    # 使用utf-8编码保存输出文件
    result_df.to_csv(output_file, index=False, encoding='utf-8')
    print("数据合并处理完成!")
    
    return result_df

# 提取轨道交通刷卡数据
def extract_metro_data(df):
    print("提取轨道交通刷卡数据...")
    metro_df = df[df['TRAFFIC_TYPE'] == 2].copy()
    print(f"轨道交通数据行数: {len(metro_df)}")
    
    # 转换时间格式
    metro_df['RIDING_DATE'] = metro_df['RIDING_DATE'].astype(str)
    metro_df['RIDING_TIME'] = metro_df['RIDING_TIME'].astype(str).str.zfill(6)
    
    # 创建日期时间列
    metro_df['DATETIME'] = pd.to_datetime(
        metro_df['RIDING_DATE'] + metro_df['RIDING_TIME'],
        format='%Y%m%d%H%M%S'
    )
    
    # 提取小时和分钟
    metro_df['HOUR'] = metro_df['DATETIME'].dt.hour
    metro_df['MINUTE'] = metro_df['DATETIME'].dt.minute
    
    # 提取线路号（第一位数字）
    metro_df['LINE'] = metro_df['LINE_NO'].str[0:2].astype(int)
    
    return metro_df

# 按线路统计流量
def analyze_by_line(metro_df):
    print("按线路统计流量...")
    line_stats = metro_df.groupby('LINE').size().reset_index(name='流量')
    
    # 保存统计结果
    line_stats.to_csv('line_flow_stats.csv', index=False, encoding='utf-8')
    
    # 绘制柱状图
    plt.figure(figsize=(12, 6))
    plt.bar(line_stats['LINE'], line_stats['流量'])
    plt.title('各线路客流量统计')
    plt.xlabel('线路')
    plt.ylabel('客流量')
    plt.xticks(line_stats['LINE'])
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    plt.savefig('line_flow_stats.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return line_stats

# 按站点统计流量
def analyze_by_station(metro_df):
    print("按站点统计流量...")
    station_stats = metro_df.groupby(['LINE_NO', '站点名称']).size().reset_index(name='流量')
    station_stats = station_stats.sort_values('流量', ascending=False)
    
    # 保存统计结果
    station_stats.to_csv('station_flow_stats.csv', index=False, encoding='utf-8')
    
    # 绘制前20个站点的柱状图
    top_stations = station_stats.head(20)
    plt.figure(figsize=(15, 8))
    plt.bar(top_stations['站点名称'], top_stations['流量'])
    plt.title('前20个站点客流量统计')
    plt.xlabel('站点')
    plt.ylabel('客流量')
    plt.xticks(rotation=45, ha='right')
    plt.grid(axis='y', linestyle='--', alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('station_flow_stats.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return station_stats

# 按时段统计流量
def analyze_by_time(metro_df):
    print("按时段统计流量...")
    # 按小时统计
    hour_stats = metro_df.groupby('HOUR').size().reset_index(name='流量')
    
    # 保存统计结果
    hour_stats.to_csv('hour_flow_stats.csv', index=False, encoding='utf-8')
    
    # 绘制折线图
    plt.figure(figsize=(12, 6))
    plt.plot(hour_stats['HOUR'], hour_stats['流量'], marker='o', linestyle='-')
    plt.title('各时段客流量统计')
    plt.xlabel('小时')
    plt.ylabel('客流量')
    plt.xticks(range(24))
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 标记早晚高峰
    peak_hours = hour_stats.nlargest(3, '流量')
    for _, peak in peak_hours.iterrows():
        plt.annotate(
            f"{peak['HOUR']}点: {peak['流量']}",
            xy=(peak['HOUR'], peak['流量']),
            xytext=(0, 15),
            textcoords='offset points',
            ha='center',
            arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=.2')
        )
    
    plt.tight_layout()
    plt.savefig('hour_flow_stats.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return hour_stats

# 按乘客统计流量
def analyze_by_passenger(metro_df):
    print("按乘客统计流量...")
    passenger_stats = metro_df.groupby('CARD_NO').size().reset_index(name='乘坐次数')
    passenger_stats = passenger_stats.sort_values('乘坐次数', ascending=False)
    
    # 保存统计结果
    passenger_stats.to_csv('passenger_stats.csv', index=False, encoding='utf-8')
    
    # 绘制乘坐次数分布的直方图
    plt.figure(figsize=(12, 6))
    sns.histplot(passenger_stats['乘坐次数'], bins=30, kde=True)
    plt.title('乘客乘坐次数分布')
    plt.xlabel('乘坐次数')
    plt.ylabel('乘客数量')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('passenger_ride_distribution.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    return passenger_stats

# 绘制散点图展示两个变量之间的关系
def plot_scatter_relationship(metro_df):
    print("绘制散点图展示变量关系...")
    # 例如：分析不同时段的平均支付金额
    time_paid_df = metro_df.groupby('HOUR')['PAID'].mean().reset_index()
    
    plt.figure(figsize=(12, 6))
    plt.scatter(time_paid_df['HOUR'], time_paid_df['PAID'], s=50, alpha=0.7)
    plt.title('各时段平均支付金额关系')
    plt.xlabel('小时')
    plt.ylabel('平均支付金额')
    plt.xticks(range(24))
    plt.grid(True, linestyle='--', alpha=0.7)
    
    # 添加趋势线
    z = np.polyfit(time_paid_df['HOUR'], time_paid_df['PAID'], 1)
    p = np.poly1d(z)
    plt.plot(time_paid_df['HOUR'], p(time_paid_df['HOUR']), "r--", alpha=0.7)
    
    plt.tight_layout()
    plt.savefig('hour_paid_scatter.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    # 分析站点流量与支付金额的关系
    station_paid_df = metro_df.groupby('站点名称').agg({
        'CARD_NO': 'count',
        'PAID': 'mean'
    }).reset_index()
    station_paid_df.rename(columns={'CARD_NO': '流量'}, inplace=True)
    
    plt.figure(figsize=(12, 8))
    plt.scatter(station_paid_df['流量'], station_paid_df['PAID'], s=30, alpha=0.7)
    
    # 标记一些特殊点
    for i, row in station_paid_df.nlargest(5, '流量').iterrows():
        plt.annotate(
            row['站点名称'],
            xy=(row['流量'], row['PAID']),
            xytext=(5, 0),
            textcoords='offset points',
            fontsize=8
        )
    
    plt.title('站点流量与平均支付金额关系')
    plt.xlabel('站点流量')
    plt.ylabel('平均支付金额')
    plt.grid(True, linestyle='--', alpha=0.7)
    plt.tight_layout()
    plt.savefig('station_flow_paid_scatter.png', dpi=300, bbox_inches='tight')
    plt.close()

# 分时段集计交通流量（15分钟为一个时间片）
def calculate_time_slice_flow(metro_df):
    print("分时段集计交通流量（15分钟为一个时间片）...")
    
    # 创建时间片索引（每天96个15分钟时间片）
    metro_df['TIME_SLICE'] = metro_df['HOUR'] * 4 + (metro_df['MINUTE'] // 15)
    
    # 按日期和时间片统计流量
    time_slice_stats = metro_df.groupby(['RIDING_DATE', 'TIME_SLICE']).size().reset_index(name='流量')
    
    # 创建一个完整的日期-时间片网格（确保所有时间片都有数据）
    dates = time_slice_stats['RIDING_DATE'].unique()
    time_slices = range(96)  # 0-95，共96个15分钟时间片
    
    # 创建一个多维数组来存储流量数据 [日期数, 96]
    flow_array = np.zeros((len(dates), 96), dtype=np.int32)
    
    # 填充数组
    for i, date in enumerate(dates):
        date_data = time_slice_stats[time_slice_stats['RIDING_DATE'] == date]
        for _, row in date_data.iterrows():
            time_slice = row['TIME_SLICE']
            if 0 <= time_slice < 96:  # 确保时间片在有效范围内
                flow_array[i, time_slice] = row['流量']
    
    # 保存为numpy数组
    np.save('metroFlow.npy', flow_array)
    print(f"保存metroFlow.npy完成，形状为: {flow_array.shape}")
    
    # 可视化某一天的时间片流量
    if len(dates) > 0:
        sample_date_idx = 0
        sample_date = dates[sample_date_idx]
        sample_flow = flow_array[sample_date_idx]
        
        plt.figure(figsize=(15, 6))
        plt.plot(range(96), sample_flow, marker='.', linestyle='-')
        
        # 将时间片转换为小时:分钟格式用于x轴标签
        x_ticks = []
        x_positions = []
        for i in range(0, 96, 4):  # 每小时显示一个标签
            hour = i // 4
            x_ticks.append(f"{hour:02d}:00")
            x_positions.append(i)
        
        plt.xticks(x_positions, x_ticks, rotation=45)
        plt.title(f'日期 {sample_date} 的15分钟时间片流量')
        plt.xlabel('时间')
        plt.ylabel('流量')
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.tight_layout()
        plt.savefig('time_slice_flow.png', dpi=300, bbox_inches='tight')
        plt.close()
    
    return flow_array

# 主函数
def main():
    # 合并数据
    merged_df = merge_data()
    
    # 提取轨道交通数据
    metro_df = extract_metro_data(merged_df)
    
    # 按线路统计流量
    line_stats = analyze_by_line(metro_df)
    
    # 按站点统计流量
    station_stats = analyze_by_station(metro_df)
    
    # 按时段统计流量
    hour_stats = analyze_by_time(metro_df)
    
    # 按乘客统计流量
    passenger_stats = analyze_by_passenger(metro_df)
    
    # 绘制散点图
    plot_scatter_relationship(metro_df)
    
    # 分时段集计交通流量
    flow_array = calculate_time_slice_flow(metro_df)
    
    print("所有分析和可视化任务已完成！")
    print("生成的文件包括:")
    print("- merged_data_with_stations.csv: 合并后的完整数据")
    print("- line_flow_stats.csv 和 line_flow_stats.png: 线路流量统计")
    print("- station_flow_stats.csv 和 station_flow_stats.png: 站点流量统计")
    print("- hour_flow_stats.csv 和 hour_flow_stats.png: 时段流量统计")
    print("- passenger_stats.csv 和 passenger_ride_distribution.png: 乘客统计")
    print("- hour_paid_scatter.png 和 station_flow_paid_scatter.png: 散点图关系分析")
    print("- metroFlow.npy: 15分钟时间片流量数据")
    print("- time_slice_flow.png: 某一天的时间片流量可视化")

if __name__ == "__main__":
    # 设置中文显示
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
    plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
    
    main() 