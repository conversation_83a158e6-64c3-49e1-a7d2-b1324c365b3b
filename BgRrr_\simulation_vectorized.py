import numpy as np

def simulate_competition_vectorized(n, sigma_squared, num_simulations=1000):
    # 步骤 1: 一次性为所有模拟生成所有运动员的真实能力
    # 形状为 (num_simulations, n)
    true_abilities = np.random.normal(0, 1, size=(num_simulations, n))

    # 步骤 2: 基于真实能力生成所有表现
    # NumPy 会自动将 true_abilities 广播到正确的形状
    performances = np.random.normal(true_abilities, np.sqrt(sigma_squared))

    # 步骤 3: 找到每次模拟中表现最佳的运动员的索引
    # np.argmax 在 axis=1 上操作，为每一行（即每一次模拟）返回最大值的索引
    best_indices = np.argmax(performances, axis=1)

    # 步骤 4: 使用高级索引一次性提取所有获胜者的真实能力和表现
    # np.arange(num_simulations) 为我们提供了行索引 [0, 1, 2, ...]
    # best_indices 为我们提供了每行中对应的列索引
    winner_true_abilities = true_abilities[np.arange(num_simulations), best_indices]
    winner_performances = performances[np.arange(num_simulations), best_indices]

    # 步骤 5: 计算每次模拟的偏差
    deviations = winner_true_abilities - winner_performances

    # 步骤 6: 返回所有偏差的平均值
    return np.mean(deviations)

parameters = [(10, 0.1), (10, 1.0), (100, 0.1), (100, 1.0)]

results = {}
for n, sigma_squared in parameters:
    avg_deviation = simulate_competition_vectorized(n, sigma_squared)
    results[(n, sigma_squared)] = avg_deviation
    print(f'n={n}, sigma^2={sigma_squared}: 平均偏差 = {avg_deviation}') 