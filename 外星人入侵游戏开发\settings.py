class Settings:
    """存储游戏《外星人入侵》中所有设置的类"""
    
    def __init__(self):
        """初始化游戏的静态设置"""
        # 屏幕设置
        self.screen_width = 1200
        self.screen_height = 800
        self.bg_color = (0, 0, 20)  # 更改背景为深蓝黑色，模拟太空
        
        # 飞船设置
        self.ship_limit = 3
        
        # 子弹设置
        self.bullet_width = 3
        self.bullet_height = 15
        self.bullet_color = (255, 255, 0)  # 黄色子弹，更具视觉效果
        self.bullets_allowed = 5
        
        # 自动射击设置
        self.auto_fire_interval = 500  # 毫秒
        
        # 外星人设置
        self.max_aliens = 6  # 减少屏幕上同时出现的外星人数量
        self.alien_spawn_interval = 2500  # 毫秒，大幅增加新外星人生成间隔
        
        # 升级设置
        self.level_up_score = 1000  # 每获得1000分升级一次
        
        # 以什么速度加快游戏节奏
        self.speedup_scale = 1.03  # 降低每次升级的速度增幅
        # 外星人分数的提高速度
        self.score_scale = 1.5
        
        self.initialize_dynamic_settings()
        
    def initialize_dynamic_settings(self):
        """初始化随游戏进行而变化的设置"""
        self.ship_speed = 1.5
        self.bullet_speed = 3.0
        self.alien_speed = 0.25  # 进一步降低初始外星人速度
        
        # 记分
        self.alien_points = 50
        
    def increase_speed(self):
        """提高速度设置和外星人分数"""
        self.ship_speed *= self.speedup_scale
        self.bullet_speed *= self.speedup_scale
        
        # 外星人速度增长要更慢一些
        self.alien_speed *= (self.speedup_scale * 0.7)
        # 严格限制外星人最大速度
        self.alien_speed = min(self.alien_speed, 1.0)
        
        # 减少生成间隔，提高难度，但同时确保不会太快
        self.alien_spawn_interval = max(1200, int(self.alien_spawn_interval * 0.95))
        self.auto_fire_interval = max(300, int(self.auto_fire_interval * 0.98))
        
        self.alien_points = int(self.alien_points * self.score_scale) 