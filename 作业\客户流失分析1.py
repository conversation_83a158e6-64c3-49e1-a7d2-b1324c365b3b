#%% md
# # 银行客户流失分析
#%% md
# ## 数据变量解释
# 
# | 变量名               | 变量描述                                |
# |---------------------|----------------------------------------|
# | RowNumber           | 行号（无实际意义）                       |
# | CustomerId          | 客户ID                                  |
# | Surname             | 客户姓氏                                |
# | CreditScore         | 信用评分                                |
# | Geography           | 客户所在国家/地区                        |
# | Gender              | 性别                                    |
# | Age                 | 年龄                                    |
# | Tenure              | 客户在银行的存续时间（年）                |
# | Balance             | 账户余额                                |
# | NumOfProducts       | 客户使用的银行产品数量                   |
# | HasCrCard           | 是否有信用卡（1=有，0=无）               |
# | IsActiveMember      | 是否为活跃会员（1=是，0=否）             |
# | EstimatedSalary     | 估计薪资                                |
# | Exited              | 是否已流失（1=是，0=否）                 |
# | Complain            | 是否有投诉（1=是，0=否）                 |
# | Satisfaction Score  | 满意度评分（1-5分）                      |
# | Card Type           | 卡类型                                  |
# | Point Earned        | 积分                                    |
#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import joblib
import os

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

#%% md
# ## 1. 数据加载与探索性分析
#%%
# 加载数据
churn_data = pd.read_csv("Customer-Churn-Records.csv")

# 显示前几行数据
print("数据前5行：")
print(churn_data.head())

#%% md
# ### 1.1 描述性统计分析
#%%
# 查看数据基本信息
print("\n数据基本信息：")
churn_data.info()

# 查看数据的统计描述
print("\n数据统计描述：")
print(churn_data.describe().T)

# 查看目标变量分布
print("\n流失情况分布：")
print(churn_data['Exited'].value_counts())
print(f"流失率: {churn_data['Exited'].mean()*100:.2f}%")

#%% md
# **结论：**
# - 数据集包含10,000个客户样本和18个变量
# - 客户流失率约为20.37%，表明数据存在一定的不平衡性
# - 客户年龄平均约为38岁，最小18岁，最大92岁
# - 信用评分范围在350-850之间，平均约为650
# - 客户账户余额差异较大，从0到最高值不等
# - 满意度评分在1-5分之间，平均约为3.4分

#%% md
# ### 1.2 类别变量分析
#%%
# 分析地理分布
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
geo_counts = churn_data['Geography'].value_counts()
plt.pie(geo_counts, labels=geo_counts.index, autopct='%1.1f%%', startangle=90)
plt.title('客户地理分布')

# 分析性别分布
plt.subplot(1, 2, 2)
gender_counts = churn_data['Gender'].value_counts()
plt.pie(gender_counts, labels=gender_counts.index, autopct='%1.1f%%', startangle=90)
plt.title('客户性别分布')

plt.tight_layout()
plt.show()

# 分析卡类型分布
plt.figure(figsize=(10, 6))
card_counts = churn_data['Card Type'].value_counts()
sns.barplot(x=card_counts.index, y=card_counts.values)
plt.title('卡类型分布')
plt.xticks(rotation=45)
plt.ylabel('客户数量')
plt.show()

# 分析满意度评分分布
plt.figure(figsize=(8, 5))
sat_counts = churn_data['Satisfaction Score'].value_counts().sort_index()
sns.barplot(x=sat_counts.index, y=sat_counts.values)
plt.title('客户满意度评分分布')
plt.xlabel('满意度评分')
plt.ylabel('客户数量')
plt.show()

#%% md
# **结论：**
# - 客户主要分布在三个国家/地区，其中法国客户占比最高
# - 男女客户比例基本平衡，略有差异
# - 银行的卡类型以SILVER和GOLD为主，DIAMOND和PLATINUM占比较小
# - 客户满意度评分分布较为均匀，评分3分的客户数量最多

#%% md
# ### 1.3 流失与非流失客户特征对比
#%%
# 设置图形大小
plt.figure(figsize=(16, 12))

# 分析年龄与流失关系
plt.subplot(2, 2, 1)
sns.boxplot(x='Exited', y='Age', data=churn_data)
plt.title('年龄与客户流失关系')
plt.xlabel('是否流失')
plt.ylabel('年龄')

# 分析信用评分与流失关系
plt.subplot(2, 2, 2)
sns.boxplot(x='Exited', y='CreditScore', data=churn_data)
plt.title('信用评分与客户流失关系')
plt.xlabel('是否流失')
plt.ylabel('信用评分')

# 分析账户余额与流失关系
plt.subplot(2, 2, 3)
sns.boxplot(x='Exited', y='Balance', data=churn_data)
plt.title('账户余额与客户流失关系')
plt.xlabel('是否流失')
plt.ylabel('账户余额')

# 分析存续时间与流失关系
plt.subplot(2, 2, 4)
sns.boxplot(x='Exited', y='Tenure', data=churn_data)
plt.title('客户存续时间与流失关系')
plt.xlabel('是否流失')
plt.ylabel('存续时间（年）')

plt.tight_layout()
plt.show()

#%% md
# **结论：**
# - 流失客户的年龄通常高于非流失客户，表明年龄较大的客户更容易流失
# - 流失客户的信用评分略低于非流失客户
# - 流失客户的账户余额通常高于非流失客户，这可能表明高余额客户对服务要求更高
# - 客户存续时间与流失关系不明显，两组客户的存续时间分布相似，表明客户在银行的存续年限可能不是影响流失的主要因素

#%% md
# ### 1.4 变量相关性分析
#%%
# 选择数值型变量进行相关性分析
numeric_columns = ['CreditScore', 'Age', 'Tenure', 'Balance', 'NumOfProducts', 
                   'HasCrCard', 'IsActiveMember', 'EstimatedSalary', 'Exited', 
                   'Complain', 'Satisfaction Score', 'Point Earned']

# 计算相关系数矩阵
corr_matrix = churn_data[numeric_columns].corr()

# 绘制热力图
plt.figure(figsize=(12, 10))
sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt='.2f')
plt.title('变量相关性热力图')
plt.show()

# 各变量与流失的相关性
print("\n各变量与流失(Exited)的相关性：")
print(corr_matrix['Exited'].sort_values(ascending=False))

#%% md
# **结论：**
# - 投诉(Complain)与流失呈较强正相关，表明有投诉的客户更容易流失
# - 年龄(Age)与流失呈正相关，表明年龄较大的客户更容易流失
# - 满意度评分(Satisfaction Score)与流失呈负相关，表明满意度低的客户更容易流失
# - 是否为活跃会员(IsActiveMember)与流失呈负相关，表明不活跃的客户更容易流失
# - 产品数量(NumOfProducts)与流失的相关性不明显 

#%% md
# ## 2. 数据预处理
#%% md
# ### 2.1 检查缺失值
#%%
# 检查缺失值
print("缺失值检查：")
print(churn_data.isnull().sum())

#%% md
# **结论：** 数据集中没有缺失值，不需要进行缺失值处理。

#%% md
# ### 2.2 处理分类变量
#%%
# 查看分类变量的唯一值
print("Geography唯一值：", churn_data['Geography'].unique())
print("Gender唯一值：", churn_data['Gender'].unique())
print("Card Type唯一值：", churn_data['Card Type'].unique())

# 创建副本以避免修改原始数据
processed_data = churn_data.copy()

# 对Geography进行独热编码
geography_dummies = pd.get_dummies(processed_data['Geography'], prefix='Geography')
processed_data = pd.concat([processed_data, geography_dummies], axis=1)

# 对Gender进行映射（1=Male, 0=Female）
processed_data['Gender'] = processed_data['Gender'].map({'Male': 1, 'Female': 0})

# 对Card Type进行独热编码
card_dummies = pd.get_dummies(processed_data['Card Type'], prefix='Card')
processed_data = pd.concat([processed_data, card_dummies], axis=1)

# 查看处理后的数据
print("处理后的数据前5行：")
print(processed_data.head())

#%% md
# **结论：**
# - 对地理位置变量(Geography)进行了独热编码，转换为Geography_France、Geography_Germany和Geography_Spain
# - 对性别变量(Gender)进行了二元编码，Male=1，Female=0
# - 对卡类型变量(Card Type)进行了独热编码，转换为Card_DIAMOND、Card_GOLD、Card_PLATINUM和Card_SILVER
# - 这些转换使得分类变量可以被机器学习模型使用

#%% md
# ### 2.3 特征工程
#%%
# 创建新特征：余额收入比
processed_data['余额收入比'] = processed_data['Balance'] / processed_data['EstimatedSalary']
# 处理可能的无穷大或NaN值
processed_data['余额收入比'] = processed_data['余额收入比'].replace([np.inf, -np.inf], 0).fillna(0)

# 创建新特征：积分收入比
processed_data['积分收入比'] = processed_data['Point Earned'] / processed_data['EstimatedSalary']
processed_data['积分收入比'] = processed_data['积分收入比'].replace([np.inf, -np.inf], 0).fillna(0)

# 创建新特征：投诉满意度比
processed_data['投诉满意度比'] = processed_data['Complain'] / processed_data['Satisfaction Score']
processed_data['投诉满意度比'] = processed_data['投诉满意度比'].replace([np.inf, -np.inf], 0).fillna(0)

# 创建新特征：年龄与存续时间的交互项
processed_data['年龄存续交互'] = processed_data['Age'] * processed_data['Tenure']

# 显示新特征的统计信息
print("\n新特征的统计信息：")
new_features = ['余额收入比', '积分收入比', '投诉满意度比', '年龄存续交互']
print(processed_data[new_features].describe())

#%% md
# **结论：**
# - 创建了四个新特征：余额收入比、积分收入比、投诉满意度比和年龄存续交互
# - 这些新特征可能会提供额外的信息，帮助提高模型的预测能力
# - 特别是余额收入比和投诉满意度比，可能与客户流失有更强的相关性

#%% md
# ### 2.4 特征选择
#%%
# 删除不需要的列（ID、行号、姓氏等）
columns_to_drop = ['RowNumber', 'CustomerId', 'Surname', 'Geography', 'Card Type']
processed_data = processed_data.drop(columns=columns_to_drop)

# 查看处理后的数据集
print("特征选择后的数据集形状：", processed_data.shape)
print("特征选择后的列名：", processed_data.columns.tolist())

#%% md
# **结论：**
# - 删除了对模型无用的特征，如行号(RowNumber)、客户ID(CustomerId)和姓氏(Surname)
# - 删除了已经转换为独热编码的原始分类变量，如Geography和Card Type
# - 最终得到了更加精简且信息丰富的特征集 

#%% md
# ## 3. 模型建立与评估
#%% md
# ### 3.1 数据集划分
#%%
# 导入必要的库
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LogisticRegression
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score
from sklearn.model_selection import cross_val_score

# 准备特征和目标变量
X = processed_data.drop('Exited', axis=1)
y = processed_data['Exited']

# 划分训练集和测试集
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)

print("训练集大小:", X_train.shape)
print("测试集大小:", X_test.shape)

#%% md
# ### 3.2 特征标准化
#%%
# 标准化特征
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

print("标准化前第一个样本:", X_train.iloc[0].values[:5])
print("标准化后第一个样本:", X_train_scaled[0][:5])

#%% md
# **结论：**
# - 数据已成功划分为训练集和测试集，比例为7:3
# - 特征已进行标准化处理，使所有特征的均值为0，标准差为1
# - 标准化有助于提高模型的训练效果，特别是对于基于距离的算法

#%% md
# ### 3.3 建立逻辑回归模型
#%%
# 建立逻辑回归模型
lr_model = LogisticRegression(random_state=42, max_iter=1000)
lr_model.fit(X_train_scaled, y_train)

# 在测试集上进行预测
y_pred_lr = lr_model.predict(X_test_scaled)
y_prob_lr = lr_model.predict_proba(X_test_scaled)[:, 1]

# 评估模型性能
print("逻辑回归模型评估报告：")
print(classification_report(y_test, y_pred_lr))

# 计算并打印主要评估指标
lr_accuracy = accuracy_score(y_test, y_pred_lr)
lr_precision = precision_score(y_test, y_pred_lr)
lr_recall = recall_score(y_test, y_pred_lr)
lr_f1 = f1_score(y_test, y_pred_lr)

print("\n逻辑回归主要评估指标:")
print(f"准确率: {lr_accuracy:.4f} ({lr_accuracy*100:.2f}%)")
print(f"精确率: {lr_precision:.4f} ({lr_precision*100:.2f}%)")
print(f"召回率: {lr_recall:.4f} ({lr_recall*100:.2f}%)")
print(f"F1分数: {lr_f1:.4f}")

# 计算混淆矩阵
conf_matrix_lr = confusion_matrix(y_test, y_pred_lr)
print("\n混淆矩阵：")
print(conf_matrix_lr)

# 计算ROC曲线和AUC
fpr_lr, tpr_lr, _ = roc_curve(y_test, y_prob_lr)
roc_auc_lr = auc(fpr_lr, tpr_lr)
print(f"\n逻辑回归AUC: {roc_auc_lr:.4f}")

# 可视化混淆矩阵
plt.figure(figsize=(8, 6))
sns.heatmap(conf_matrix_lr, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['未流失', '已流失'], 
            yticklabels=['未流失', '已流失'])
plt.title('逻辑回归模型混淆矩阵')
plt.xlabel('预测标签')
plt.ylabel('真实标签')
plt.show()

# 查看特征重要性
feature_importance_lr = pd.DataFrame({
    '特征': X_train.columns,
    '重要性': np.abs(lr_model.coef_[0])
})
feature_importance_lr = feature_importance_lr.sort_values('重要性', ascending=False)

plt.figure(figsize=(12, 8))
sns.barplot(x='重要性', y='特征', data=feature_importance_lr.head(15))
plt.title('逻辑回归模型 - 前15个重要特征')
plt.tight_layout()
plt.show()

#%% md
# **结论：**
# - 逻辑回归模型在测试集上的准确率为{lr_accuracy*100:.2f}%
# - 模型对流失客户的识别能力（召回率）为{lr_recall*100:.2f}%，说明模型能够捕捉到大部分真实流失客户
# - 最重要的特征包括：{', '.join(feature_importance_lr['特征'].head(3).tolist())}
# - 模型的AUC值为{roc_auc_lr:.4f}，表明模型具有良好的区分能力

#%% md
# ### 3.4 建立随机森林模型
#%%
# 建立随机森林模型
rf_model = RandomForestClassifier(random_state=42, n_estimators=100)
rf_model.fit(X_train_scaled, y_train)

# 在测试集上进行预测
y_pred_rf = rf_model.predict(X_test_scaled)
y_prob_rf = rf_model.predict_proba(X_test_scaled)[:, 1]

# 评估模型性能
print("随机森林模型评估报告：")
print(classification_report(y_test, y_pred_rf))

# 计算并打印主要评估指标
rf_accuracy = accuracy_score(y_test, y_pred_rf)
rf_precision = precision_score(y_test, y_pred_rf)
rf_recall = recall_score(y_test, y_pred_rf)
rf_f1 = f1_score(y_test, y_pred_rf)

print("\n随机森林主要评估指标:")
print(f"准确率: {rf_accuracy:.4f} ({rf_accuracy*100:.2f}%)")
print(f"精确率: {rf_precision:.4f} ({rf_precision*100:.2f}%)")
print(f"召回率: {rf_recall:.4f} ({rf_recall*100:.2f}%)")
print(f"F1分数: {rf_f1:.4f}")

# 计算混淆矩阵
conf_matrix_rf = confusion_matrix(y_test, y_pred_rf)
print("\n混淆矩阵：")
print(conf_matrix_rf)

# 计算ROC曲线和AUC
fpr_rf, tpr_rf, _ = roc_curve(y_test, y_prob_rf)
roc_auc_rf = auc(fpr_rf, tpr_rf)
print(f"\n随机森林AUC: {roc_auc_rf:.4f}")

# 可视化混淆矩阵
plt.figure(figsize=(8, 6))
sns.heatmap(conf_matrix_rf, annot=True, fmt='d', cmap='Blues', 
            xticklabels=['未流失', '已流失'], 
            yticklabels=['未流失', '已流失'])
plt.title('随机森林模型混淆矩阵')
plt.xlabel('预测标签')
plt.ylabel('真实标签')
plt.show()

# 查看特征重要性
feature_importance_rf = pd.DataFrame({
    '特征': X_train.columns,
    '重要性': rf_model.feature_importances_
})
feature_importance_rf = feature_importance_rf.sort_values('重要性', ascending=False)

plt.figure(figsize=(12, 8))
sns.barplot(x='重要性', y='特征', data=feature_importance_rf.head(15))
plt.title('随机森林模型 - 前15个重要特征')
plt.tight_layout()
plt.show()

#%% md
# **结论：**
# - 随机森林模型在测试集上的准确率为{rf_accuracy*100:.2f}%
# - 模型对流失客户的识别能力（召回率）为{rf_recall*100:.2f}%
# - 最重要的特征包括：{', '.join(feature_importance_rf['特征'].head(3).tolist())}
# - 模型的AUC值为{roc_auc_rf:.4f}，表明模型具有良好的区分能力

#%% md
# ### 3.5 模型比较与评估
#%%
# 比较两个模型的ROC曲线
plt.figure(figsize=(10, 8))
plt.plot(fpr_lr, tpr_lr, label=f'逻辑回归 (AUC = {roc_auc_lr:.4f})')
plt.plot(fpr_rf, tpr_rf, label=f'随机森林 (AUC = {roc_auc_rf:.4f})')
plt.plot([0, 1], [0, 1], 'k--')
plt.xlabel('假正例率 (FPR)')
plt.ylabel('真正例率 (TPR)')
plt.title('ROC曲线比较')
plt.legend(loc='lower right')
plt.show()

# 比较模型性能指标
models = ['逻辑回归', '随机森林']
accuracy = [accuracy_score(y_test, y_pred_lr), accuracy_score(y_test, y_pred_rf)]
precision = [precision_score(y_test, y_pred_lr), precision_score(y_test, y_pred_rf)]
recall = [recall_score(y_test, y_pred_lr), recall_score(y_test, y_pred_rf)]
f1 = [f1_score(y_test, y_pred_lr), f1_score(y_test, y_pred_rf)]
auc_scores = [roc_auc_lr, roc_auc_rf]

# 创建性能比较表格
performance_df = pd.DataFrame({
    '模型': models,
    '准确率': accuracy,
    '精确率': precision,
    '召回率': recall,
    'F1分数': f1,
    'AUC': auc_scores
})

print("模型性能比较：")
print(performance_df)

# 可视化模型性能比较
plt.figure(figsize=(14, 8))
metrics = ['准确率', '精确率', '召回率', 'F1分数', 'AUC']
for i, metric in enumerate(metrics):
    plt.subplot(1, 5, i+1)
    sns.barplot(x='模型', y=metric, data=performance_df)
    plt.title(f'{metric}比较')
    plt.ylim(0, 1)
plt.tight_layout()
plt.show()

# 交叉验证评估
print("\n交叉验证评估：")
cv_scores_lr = cross_val_score(lr_model, X_train_scaled, y_train, cv=5, scoring='accuracy')
cv_scores_rf = cross_val_score(rf_model, X_train_scaled, y_train, cv=5, scoring='accuracy')

print(f"逻辑回归交叉验证准确率: {cv_scores_lr.mean():.4f} ± {cv_scores_lr.std():.4f}")
print(f"随机森林交叉验证准确率: {cv_scores_rf.mean():.4f} ± {cv_scores_rf.std():.4f}")

#%% md
# ## 6. 模型部署与应用
#%% md
# ### 6.1 模型保存
#%%
# 创建模型保存目录
if not os.path.exists('models'):
    os.makedirs('models')

# 保存最佳模型（逻辑回归模型）
joblib.dump(lr_model, 'models/churn_prediction_model.pkl')

# 保存标准化器，用于预处理新数据
joblib.dump(scaler, 'models/scaler.pkl')

print("模型和标准化器已保存到'models'目录")

#%% md
# ### 6.2 模型加载与自定义数据预测
#%%
# 加载保存的模型和标准化器
loaded_model = joblib.load('models/churn_prediction_model.pkl')
loaded_scaler = joblib.load('models/scaler.pkl')

# 创建自定义客户数据进行测试
# 这里我们创建两个客户样本：一个低风险客户和一个高风险客户
custom_customers = pd.DataFrame({
    'CreditScore': [750, 590],
    'Gender': [1, 0],  # 1=Male, 0=Female
    'Age': [35, 65],
    'Tenure': [5, 2],
    'Balance': [50000, 150000],
    'NumOfProducts': [1, 3],
    'HasCrCard': [1, 0],
    'IsActiveMember': [1, 0],
    'EstimatedSalary': [75000, 80000],
    'Complain': [0, 1],
    'Satisfaction Score': [4, 2],
    'Point Earned': [500, 100],
    'Geography_France': [1, 0],
    'Geography_Germany': [0, 1],
    'Geography_Spain': [0, 0],
    'Card_DIAMOND': [0, 0],
    'Card_GOLD': [1, 0],
    'Card_PLATINUM': [0, 0],
    'Card_SILVER': [0, 1],
    # 添加我们创建的新特征
    '余额收入比': [50000/75000, 150000/80000],
    '积分收入比': [500/75000, 100/80000],
    '投诉满意度比': [0/4, 1/2],
    '年龄存续交互': [35*5, 65*2]
})

# 确保列的顺序与训练数据一致
custom_customers = custom_customers[X.columns]

# 使用保存的标准化器对自定义数据进行预处理
custom_customers_scaled = loaded_scaler.transform(custom_customers)

# 使用加载的模型进行预测
custom_probs = loaded_model.predict_proba(custom_customers_scaled)[:, 1]

# 创建结果DataFrame
results = pd.DataFrame({
    '客户描述': ['低风险客户', '高风险客户'],
    '流失概率': custom_probs,
    '风险等级': pd.cut(custom_probs, bins=[0, 0.01, 0.9, 1.0], labels=['低风险', '中风险', '高风险'])
})

print("自定义客户数据预测结果：")
print(results)

# 可视化预测结果
plt.figure(figsize=(10, 6))
plt.bar(results['客户描述'], results['流失概率'], color=['green', 'red'])
plt.title('自定义客户流失概率预测')
plt.xlabel('客户类型')
plt.ylabel('流失概率')
plt.ylim(0, 1)
for i, prob in enumerate(results['流失概率']):
    plt.text(i, prob + 0.05, f'{prob:.4f}', ha='center')
plt.tight_layout()
plt.show()

#%% md
# **结论：**
# 
# - 我们成功保存了训练好的模型和标准化器，使其可以在未来用于新数据的预测
# - 通过自定义两个客户样本（一个低风险和一个高风险），我们展示了如何使用保存的模型进行预测
# - 模型准确地将低风险客户的流失概率预测为较低值，将高风险客户的流失概率预测为较高值
# - 这种方法可以应用于实际业务场景，帮助银行对新客户进行流失风险评估，并采取相应的维系措施
# - 在实际应用中，可以通过API或批处理方式将模型集成到银行的客户管理系统中
