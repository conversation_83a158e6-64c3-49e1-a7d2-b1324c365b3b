import pandas as pd
import os

def excel_to_csv(excel_file, output_dir=None):
    # 获取文件名（不含扩展名）
    file_name = os.path.splitext(os.path.basename(excel_file))[0]
    
    # 如果未指定输出目录，则使用Excel文件所在目录
    if output_dir is None:
        output_dir = os.path.dirname(excel_file)
        if output_dir == '':
            output_dir = '.'
    
    # 确保输出目录存在
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 构建CSV文件路径
    csv_file = os.path.join(output_dir, f"{file_name}.csv")
    
    try:
        # 读取Excel文件
        print(f"正在读取Excel文件: {excel_file}")
        df = pd.read_excel(excel_file)
        
        # 将数据保存为CSV
        print(f"正在将数据保存为CSV: {csv_file}")
        df.to_csv(csv_file, index=False, encoding='utf-8-sig')  # 使用带BOM的UTF-8编码，以便Excel正确显示中文
        
        print(f"转换完成: {excel_file} -> {csv_file}")
        return csv_file
    except Exception as e:
        print(f"转换失败: {excel_file}, 错误: {str(e)}")
        return None

def main():
    # 要转换的Excel文件列表
    excel_files = ["腾讯.xlsx", "泡泡玛特.xlsx", "小米.xlsx"]
    
    # 转换每个Excel文件
    for excel_file in excel_files:
        excel_to_csv(excel_file)

if __name__ == "__main__":
    main() 