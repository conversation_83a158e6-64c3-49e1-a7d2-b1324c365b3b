{"cells": [{"cell_type": "code", "execution_count": 1, "id": "628a75c979279d6d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import tushare as ts\n", "import time\n", "import os\n", "from datetime import datetime\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "import warnings\n", "warnings.filterwarnings('ignore')"]}, {"cell_type": "code", "execution_count": 2, "id": "cacb53d0-7348-48aa-b841-ea4e4e13cbed", "metadata": {}, "outputs": [], "source": ["# 设置Tushare token\n", "ts.set_token('349eb0a373e77df911a0d9673cc9a21a4678d42ee77d2466e336ad3b')\n", "pro = ts.pro_api()"]}, {"cell_type": "code", "execution_count": 3, "id": "249e34b5-b7a5-483b-acab-c61a30dd536e", "metadata": {}, "outputs": [], "source": ["# 读取违规数据\n", "def read_violation_data():\n", "    \"\"\"读取违规数据文件\"\"\"\n", "    violation_file = 'STK_Violation_Main.xlsx'\n", "    try:\n", "        df = pd.read_excel(violation_file)\n", "        print(f\"成功读取违规数据文件，共 {df.shape[0]} 条记录\")\n", "        \n", "        # 处理违规数据\n", "        # 1. 将Symbol列的数据类型转换为字符串，并确保6位数字格式（前导0）\n", "        df['Symbol'] = df['Symbol'].astype(str).str.z<PERSON>(6)\n", "        \n", "        # 2. 将DeclareDate列转换为datetime类型\n", "        df['DeclareDate'] = pd.to_datetime(df['DeclareDate'])\n", "        \n", "        # 3. 提取年份信息\n", "        df['Year'] = df['DeclareDate'].dt.year\n", "        \n", "        return df\n", "    except Exception as e:\n", "        print(f\"读取违规数据文件时出错: {e}\")\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 4, "id": "2c749f64-af71-4516-a117-271c5e360ebc", "metadata": {}, "outputs": [], "source": ["# 处理违规标签\n", "def process_violation_labels(violation_df):\n", "    \"\"\"\n", "    处理违规标签，根据规则生成最终的违规数据标记\n", "    \n", "    规则：如果某年某公司在STK_Violation_Main.xlsx中存在Y情况，则标记为1，其他情况为0\n", "    \"\"\"\n", "    # 按公司代码和年份分组，保留每组中的违规记录（IsViolated为Y）\n", "    # 如果存在违规记录，则该公司在该年被标记为违规\n", "    violation_yearly = violation_df.groupby(['Symbol', 'Year'])['IsViolated'].apply(\n", "        lambda x: 1 if 'Y' in x.values else 0\n", "    ).reset_index()\n", "    \n", "    violation_yearly.rename(columns={'IsViolated': 'Fraud'}, inplace=True)\n", "    \n", "    return violation_yearly\n"]}, {"cell_type": "code", "execution_count": 5, "id": "7a3e6da1-1cb7-474c-a0d2-eb4a85a90acc", "metadata": {}, "outputs": [], "source": ["# 获取A股上市公司列表\n", "def get_stock_list():\n", "    \"\"\"获取A股上市公司列表\"\"\"\n", "    try:\n", "        # 获取股票列表\n", "        stocks = pro.stock_basic(exchange='', list_status='L', \n", "                                 fields='ts_code,symbol,name,area,industry,list_date')\n", "        print(f\"成功获取上市公司列表，共 {stocks.shape[0]} 家公司\")\n", "        return stocks\n", "    except Exception as e:\n", "        print(f\"获取股票列表时出错: {e}\")\n", "        time.sleep(1)  # 等待一秒防止频繁请求\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 6, "id": "2f4071b8-9f20-4c60-af67-697e5a722ccc", "metadata": {}, "outputs": [], "source": ["# 获取公司财务指标\n", "def get_financial_indicators(ts_code, start_year=2018, end_year=2023):\n", "    \"\"\"获取公司的财务指标数据\"\"\"\n", "    indicators = []\n", "    \n", "    try:\n", "        # 循环获取每一年的财务指标\n", "        for year in range(start_year, end_year + 1):\n", "            time.sleep(0.5)  # 防止请求过于频繁\n", "            \n", "            # 获取财务指标数据\n", "            fin_indicator = pro.fina_indicator(ts_code=ts_code, period=f'{year}1231')\n", "            \n", "            if fin_indicator is not None and not fin_indicator.empty:\n", "                fin_indicator['Year'] = year\n", "                indicators.append(fin_indicator)\n", "                print(f\"获取 {ts_code} {year}年 财务指标成功\")\n", "                time.sleep(2)\n", "            else:\n", "                print(f\"获取 {ts_code} {year}年 财务指标为空\")\n", "    \n", "    except Exception as e:\n", "        print(f\"获取 {ts_code} 财务指标时出错: {e}\")\n", "        time.sleep(2)  # 发生错误时等待时间更长\n", "    \n", "    if indicators:\n", "        return pd.concat(indicators, ignore_index=True)\n", "    else:\n", "        return None\n"]}, {"cell_type": "code", "execution_count": 7, "id": "a4d6551a-d141-4b62-a36f-49ba4ee7bfe7", "metadata": {}, "outputs": [], "source": ["# 获取公司治理数据\n", "def get_governance_data(ts_code, start_year=2018, end_year=2023):\n", "    \"\"\"获取公司治理数据\"\"\"\n", "    governance_data = []\n", "    \n", "    try:\n", "        # 获取ESG评级数据\n", "        for year in range(start_year, end_year + 1):\n", "            time.sleep(0.5)\n", "            \n", "            # 获取公司治理数据（以ESG评级为例）\n", "            esg = pro.esg_basic(ts_code=ts_code, report_date=f'{year}1231')\n", "            \n", "            if esg is not None and not esg.empty:\n", "                esg['Year'] = year\n", "                governance_data.append(esg)\n", "                print(f\"获取 {ts_code} {year}年 ESG数据成功\")\n", "                time.sleep(2)\n", "    \n", "    except Exception as e:\n", "        print(f\"获取 {ts_code} ESG数据时出错: {e}\")\n", "        time.sleep(2)\n", "    \n", "    if governance_data:\n", "        return pd.concat(governance_data, ignore_index=True)\n", "    else:\n", "        return None\n"]}, {"cell_type": "code", "execution_count": null, "id": "0b3542af-a2f4-4b76-b1f2-41f89a35c8f8", "metadata": {}, "outputs": [], "source": ["# 获取公司股票收益率和波动率\n", "def get_stock_returns(ts_code, start_year=2018, end_year=2023):\n", "    \"\"\"获取公司股票收益率和波动率\"\"\"\n", "    returns_data = []\n", "    \n", "    try:\n", "        for year in range(start_year, end_year + 1):\n", "            start_date = f'{year}0101'\n", "            end_date = f'{year}1231'\n", "            time.sleep(0.5)\n", "            \n", "            # 获取日线数据\n", "            daily = pro.daily(ts_code=ts_code, start_date=start_date, end_date=end_date)\n", "            \n", "            if daily is not None and not daily.empty:\n", "                # 计算年度收益率和波动率\n", "                yearly_return = (daily.iloc[0]['close'] - daily.iloc[-1]['close']) / daily.iloc[-1]['close']\n", "                volatility = daily['pct_chg'].std()\n", "                \n", "                returns_data.append({\n", "                    'ts_code': ts_code,\n", "                    'Year': year,\n", "                    'YearlyReturn': yearly_return,\n", "                    'Volatility': volatility\n", "                })\n", "                print(f\"获取 {ts_code} {year}年 收益率数据成功\")\n", "                time.sleep(2)\n", "    \n", "    except Exception as e:\n", "        print(f\"获取 {ts_code} 收益率数据时出错: {e}\")\n", "        time.sleep(2)\n", "    \n", "    if returns_data:\n", "        return pd.DataFrame(returns_data)\n", "    else:\n", "        return None\n"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}