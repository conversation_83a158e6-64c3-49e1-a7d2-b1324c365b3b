{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# （一）数据预处理\n", "通过分析，将所需数据表进行合并，并对合并后的数据表进行清洗，查看数据是否存在重复、空值、特殊符号等，并用已学过知识进行处理。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#导入pandas\n", "import pandas as pd\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取学生信息表\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 读取作业完成情况表\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 合并数据表\n", "# 以学号为键进行合并\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 查看合并后的数据\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## 数据清洗"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查重复行\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 检查空值\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 由于课后作业表中的空值是学生没有回答的作业，所以空值填充为0分\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": [" #验证是否还有空值\n", "    "]}, {"cell_type": "markdown", "metadata": {}, "source": ["# （二）数据分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 1. 个人学习成绩分析\n", "### （1）计算本人的作业平均成绩、最高成绩、最低成绩"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#学生本人学号、姓名\n", "#筛选出学生本人课后作业成绩数据\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#查看列索引用于选取列名\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 计算本人5次作业的平均成绩、最高成绩、最低成绩\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# 输出结果\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （2）个人成绩差异比较，从“作业成绩统计表”中提取自己的作业成绩序列，计算每个成绩与平均成绩的离差（离差=作业成绩-平均成绩），并对离差结果进行分析。（进阶）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["### 2.班级学习数据分析\n", "（1）分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少；   \n", "（2）查找成绩优秀或需要辅导的学生（top10和tail10） ；   \n", "（3）分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布。  \n", "（4）学习数据比较（进阶分析），例如分析任务参度与作业成"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （1）分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少；"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （2）查找成绩优秀和需要辅导的学生（top10和tail10） ；"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （3）分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （4）学习数据比较，例如分析作业难易程度，即那些作业是难度高的等。（进阶）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["## （三）数据可视化\n", "使用matplotlib库对分析结果进行可视化展示，包括但不限于以下内容：   \n", "（1）绘制班级成绩Top10的柱状图；     \n", "（2）绘制学生本人成绩的趋势图（按照作业成绩前后的折线图）；   \n", "（3）绘制本班级不同分数段比例图（饼图）。（如60分以下、60-69分、70-79分、80-89分、90分以上）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "plt.rcParams['font.family']='KaiTi'"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （1）绘制班级成绩Top10的柱状图；\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （2）绘制学生本人成绩的趋势图（按照作业成绩前后的折线图）；"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （3）绘制本班级不同分数段比例图（饼图）。（如60分以下、60-69分、70-79分、80-89分、90分以上）"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### （4）绘制两个班的平均成绩对比图，在同一个画布里显示每一章的平均分； （进阶）"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.7.0"}}, "nbformat": 4, "nbformat_minor": 5}