import numpy as np
states = [1, 2]
actions = [1, 2]
gamma = 0.9

P = {
 (1, 1): {1: 0.0, 2: 1.0},
 (1, 2): {1: 1.0, 2: 0.0},
 (2, 1): {1: 1.0, 2: 0.0},
 (2, 2): {1: 0.0, 2: 1.0}
 }

R = {
 (1, 1): 1.0,
 (1, 2): 0.0,
 (2, 1): 1.0,
 (2, 2): 0.0
 }

policy = {1: 1, 2: 2}

def policy_evaluation(policy , V, P, R, gamma , theta=1e-6):
    while True:
        delta = 0
        for s in states:
            v = V[s]
            a = policy[s]
            V[s] = sum(P[s, a][s_prime] * (R[s, a] + gamma * V[s_prime]) for s_prime in states)
            delta = max(delta , abs(v - V[s]))
        if delta < theta:
            break
    return V

def policy_improvement(policy , V, P, R, gamma):
    policy_stable = True
    for s in states:
        old_action = policy[s]
        action_values = np.zeros(len(actions))
        for a in actions:
            action_values[a - 1] = sum(P[s, a][s_prime] * (R[s, a] + gamma * V[s_prime]) for s_prime in states)
        best_action = actions[np.argmax(action_values)]
        if old_action != best_action:
            policy_stable = False
            policy[s] = best_action
    return policy , policy_stable

V = {s: 0.0 for s in states}
while True:
    V = policy_evaluation(policy , V, P, R, gamma)
    policy , policy_stable = policy_improvement(policy , V, P, R, gamma)
    if policy_stable:
        break
print("Optimal Policy:", policy)
print("Optimal Value Function:", V)

