import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset, random_split
from sklearn.preprocessing import MinMaxScaler
import seaborn as sns
from tqdm import tqdm
import os

# 设置随机种子，确保结果可复现
torch.manual_seed(42)
np.random.seed(42)

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 定义LSTM模型
class TrafficForecastLSTM(nn.Module):
    def __init__(self, input_size=1, hidden_size=64, num_layers=2, output_size=1, dropout=0.2):
        """
        初始化LSTM模型
        
        参数:
        input_size (int): 输入特征维度，默认为1（单变量时序）
        hidden_size (int): LSTM隐藏层大小
        num_layers (int): LSTM层数
        output_size (int): 输出维度，默认为1（预测未来一个时间点的流量）
        dropout (float): Dropout比率，用于防止过拟合
        """
        super(TrafficForecastLSTM, self).__init__()
        
        self.hidden_size = hidden_size
        self.num_layers = num_layers
        
        # LSTM层
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout if num_layers > 1 else 0
        )
        
        # 输出层
        self.fc = nn.Linear(hidden_size, output_size)
    
    def forward(self, x):
        """
        前向传播
        
        参数:
        x: 输入数据，形状为 [batch_size, seq_len, input_size]
        
        返回:
        输出，形状为 [batch_size, output_size]
        """
        # 初始化隐藏状态和单元状态
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(x.device)
        
        # 前向传播 LSTM
        out, _ = self.lstm(x, (h0, c0))
        
        # 只使用最后一个时间步的输出
        out = self.fc(out[:, -1, :])
        
        return out

def load_data():
    """
    加载数据并获取流量最高的4条线路
    """
    print("加载数据...")
    
    # 加载metroFlow.npy数据
    metro_flow = np.load('metroFlow.npy')
    print(f"加载的metroFlow.npy形状: {metro_flow.shape}")
    
    # 加载线路流量统计数据
    line_stats = pd.read_csv('line_flow_stats.csv')
    print("线路流量统计:")
    print(line_stats)
    
    # 获取流量最高的4条线路
    top_lines = line_stats.nlargest(4, '流量')['LINE'].tolist()
    print(f"流量最高的4条线路: {top_lines}")
    
    # 加载合并后的数据，用于提取各线路的流量
    merged_data = pd.read_csv('merged_data_with_stations.csv')
    
    return metro_flow, merged_data, top_lines

def extract_metro_data(df):
    """提取轨道交通刷卡数据并处理"""
    print("提取轨道交通刷卡数据...")
    metro_df = df[df['TRAFFIC_TYPE'] == 2].copy()
    print(f"轨道交通数据行数: {len(metro_df)}")
    
    # 转换时间格式
    metro_df['RIDING_DATE'] = metro_df['RIDING_DATE'].astype(str)
    metro_df['RIDING_TIME'] = metro_df['RIDING_TIME'].astype(str).str.zfill(6)
    
    # 创建日期时间列
    metro_df['DATETIME'] = pd.to_datetime(
        metro_df['RIDING_DATE'] + metro_df['RIDING_TIME'],
        format='%Y%m%d%H%M%S'
    )
    
    # 提取小时和分钟
    metro_df['HOUR'] = metro_df['DATETIME'].dt.hour
    metro_df['MINUTE'] = metro_df['DATETIME'].dt.minute
    
    # 提取线路号（前两位数字）
    metro_df['LINE_NO'] = metro_df['LINE_NO'].astype(str).str.zfill(4)
    metro_df['LINE'] = metro_df['LINE_NO'].str[0:2].astype(int)
    
    return metro_df

def prepare_data(merged_data, line_no, seq_length=4, pred_length=1, train_ratio=0.7, val_ratio=0.2):
    """
    为特定线路准备时序预测数据
    
    参数:
    merged_data: 合并后的数据
    line_no: 线路编号
    seq_length: 输入序列长度（使用多少个时间片来预测）
    pred_length: 预测序列长度（预测未来多少个时间片）
    train_ratio: 训练集比例
    val_ratio: 验证集比例
    
    返回:
    训练、验证和测试数据加载器以及缩放器
    """
    print(f"为线路 {line_no} 准备数据...")
    print(f"使用最近 {seq_length} 个时间段预测未来 {pred_length} 个时间段")
    
    # 筛选特定线路的数据
    line_data = merged_data[merged_data['LINE'] == line_no].copy()
    
    # 按日期和时间片统计流量
    line_data['TIME_SLICE'] = line_data['HOUR'] * 4 + (line_data['MINUTE'] // 15)
    time_slice_stats = line_data.groupby(['RIDING_DATE', 'TIME_SLICE']).size().reset_index(name='流量')
    
    # 创建一个完整的日期-时间片网格
    dates = time_slice_stats['RIDING_DATE'].unique()
    
    # 创建流量数组 [日期数, 96]
    flow_array = np.zeros((len(dates), 96), dtype=np.float32)
    
    # 填充数组
    for i, date in enumerate(dates):
        date_data = time_slice_stats[time_slice_stats['RIDING_DATE'] == date]
        for _, row in date_data.iterrows():
            time_slice = row['TIME_SLICE']
            if 0 <= time_slice < 96:
                flow_array[i, time_slice] = row['流量']
    
    # 数据标准化
    scaler = MinMaxScaler(feature_range=(0, 1))
    flow_array_scaled = scaler.fit_transform(flow_array)
    
    # 创建时序数据（X: 输入序列, y: 目标值）
    X, y = [], []
    for i in range(len(flow_array_scaled)):
        for j in range(96 - seq_length - pred_length + 1):
            X.append(flow_array_scaled[i, j:j+seq_length])
            y.append(flow_array_scaled[i, j+seq_length:j+seq_length+pred_length])
    
    X = np.array(X, dtype=np.float32)
    y = np.array(y, dtype=np.float32)
    
    # 调整形状以适应LSTM输入 [batch_size, seq_len, features]
    X = X.reshape(X.shape[0], X.shape[1], 1)
    
    # 转换为PyTorch张量
    X_tensor = torch.FloatTensor(X)
    y_tensor = torch.FloatTensor(y)
    
    # 创建TensorDataset
    dataset = TensorDataset(X_tensor, y_tensor)
    
    # 划分训练集、验证集和测试集
    train_size = int(len(dataset) * train_ratio)
    val_size = int(len(dataset) * val_ratio)
    test_size = len(dataset) - train_size - val_size
    
    train_dataset, val_dataset, test_dataset = random_split(
        dataset, [train_size, val_size, test_size]
    )
    
    # 创建DataLoader
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    test_loader = DataLoader(test_dataset, batch_size=32, shuffle=False)
    
    print(f"数据准备完成: 训练集 {train_size} 样本, 验证集 {val_size} 样本, 测试集 {test_size} 样本")
    
    return train_loader, val_loader, test_loader, scaler

def train_model(model, train_loader, val_loader, line_no, epochs=50, lr=0.001, patience=10):
    """
    训练模型
    
    参数:
    model: 模型实例
    train_loader: 训练数据加载器
    val_loader: 验证数据加载器
    line_no: 线路编号（用于保存模型）
    epochs: 训练轮数
    lr: 学习率
    patience: 早停耐心值
    
    返回:
    训练好的模型和训练历史
    """
    print(f"开始训练线路 {line_no} 的LSTM模型...")
    
    # 使用GPU（如果可用）
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # 定义损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=lr)
    
    # 用于早停的变量
    best_val_loss = float('inf')
    counter = 0
    
    # 记录训练历史
    history = {
        'train_loss': [],
        'val_loss': []
    }
    
    # 训练循环
    for epoch in range(epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        
        for inputs, targets in train_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            
            # 前向传播
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            
            # 反向传播和优化
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        history['train_loss'].append(train_loss)
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                val_loss += loss.item()
        
        val_loss /= len(val_loader)
        history['val_loss'].append(val_loss)
        
        print(f"Epoch {epoch+1}/{epochs}, Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
        
        # 早停检查
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            counter = 0
            # 保存最佳模型
            model_save_path = os.path.join('model', f'lstm_model_line_{line_no}.pth')
            torch.save(model.state_dict(), model_save_path)
        else:
            counter += 1
            if counter >= patience:
                print(f"早停: 验证损失在 {patience} 轮内没有改善")
                break
    
    # 加载最佳模型
    model_load_path = os.path.join('model', f'lstm_model_line_{line_no}.pth')
    model.load_state_dict(torch.load(model_load_path))
    
    print(f"线路 {line_no} 的LSTM模型训练完成")
    return model, history

def evaluate_model(model, test_loader, scaler, line_no):
    """
    评估模型性能
    
    参数:
    model: 训练好的模型
    test_loader: 测试数据加载器
    scaler: 用于反标准化的缩放器
    line_no: 线路编号
    
    返回:
    评估指标和预测结果
    """
    print(f"评估线路 {line_no} 的LSTM模型...")
    
    # 使用GPU（如果可用）
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
    model = model.to(device)
    
    # 设置为评估模式
    model.eval()
    
    # 收集所有预测和真实值
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for inputs, targets in test_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            
            # 收集批次预测和目标
            all_predictions.append(outputs.cpu().numpy())
            all_targets.append(targets.cpu().numpy())
    
    # 合并批次数据
    all_predictions = np.vstack(all_predictions)
    all_targets = np.vstack(all_targets)
    
    # 反标准化
    # 创建一个临时数组用于反标准化
    temp_pred = np.zeros((all_predictions.shape[0], 96))
    temp_target = np.zeros((all_targets.shape[0], 96))
    
    # 将预测和目标值放入临时数组的相应位置
    temp_pred[:, 0] = all_predictions.flatten()
    temp_target[:, 0] = all_targets.flatten()
    
    # 反标准化
    pred_rescaled = scaler.inverse_transform(temp_pred)[:, 0].reshape(-1, 1)
    target_rescaled = scaler.inverse_transform(temp_target)[:, 0].reshape(-1, 1)
    
    # 计算评估指标
    mse = np.mean((pred_rescaled - target_rescaled) ** 2)
    rmse = np.sqrt(mse)
    mae = np.mean(np.abs(pred_rescaled - target_rescaled))
    
    # 计算R²
    ss_tot = np.sum((target_rescaled - np.mean(target_rescaled)) ** 2)
    ss_res = np.sum((target_rescaled - pred_rescaled) ** 2)
    r2 = 1 - (ss_res / ss_tot)
    
    print(f"线路 {line_no} 评估指标:")
    print(f"MSE: {mse:.4f}")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"R²: {r2:.4f}")
    
    # 保存评估结果
    results = {
        'mse': mse,
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'predictions': pred_rescaled,
        'targets': target_rescaled
    }
    
    return results

def visualize_training_history(history, line_no):
    """可视化训练历史"""
    plt.figure(figsize=(10, 6))
    plt.plot(history['train_loss'], label='训练损失')
    plt.plot(history['val_loss'], label='验证损失')
    plt.title(f'线路 {line_no} LSTM模型训练历史')
    plt.xlabel('Epoch')
    plt.ylabel('损失')
    plt.legend()
    plt.grid(True)
    save_path = os.path.join('lstm_results', f'lstm_training_history_line_{line_no}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def visualize_predictions(results, line_no):
    """可视化预测结果"""
    # 选择一部分数据进行可视化
    sample_size = min(100, len(results['predictions']))
    indices = np.random.choice(len(results['predictions']), sample_size, replace=False)
    
    pred_sample = results['predictions'][indices]
    target_sample = results['targets'][indices]
    
    plt.figure(figsize=(12, 6))
    plt.scatter(range(sample_size), target_sample, label='实际值', alpha=0.7, s=30)
    plt.scatter(range(sample_size), pred_sample, label='预测值', alpha=0.7, s=30)
    plt.title(f'线路 {line_no} LSTM客流量预测结果')
    plt.xlabel('样本')
    plt.ylabel('客流量')
    plt.legend()
    plt.grid(True)
    save_path = os.path.join('lstm_results', f'lstm_predictions_line_{line_no}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    # 绘制预测值与实际值的散点图
    plt.figure(figsize=(10, 10))
    plt.scatter(target_sample, pred_sample, alpha=0.5)
    plt.plot([0, max(target_sample.max(), pred_sample.max())], 
             [0, max(target_sample.max(), pred_sample.max())], 
             'r--')
    plt.title(f'线路 {line_no} LSTM预测值 vs 实际值')
    plt.xlabel('实际值')
    plt.ylabel('预测值')
    plt.grid(True)
    save_path = os.path.join('lstm_results', f'lstm_scatter_line_{line_no}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def visualize_all_results(all_results, all_histories):
    """可视化所有线路的结果比较"""
    # 比较不同线路的评估指标
    lines = list(all_results.keys())
    metrics = ['mse', 'rmse', 'mae', 'r2']
    
    for metric in metrics:
        values = [all_results[line][metric] for line in lines]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(lines, values)
        
        # 在柱状图上添加数值标签
        for bar in bars:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.4f}',
                    ha='center', va='bottom')
        
        plt.title(f'不同线路的LSTM {metric.upper()} 比较')
        plt.xlabel('线路')
        plt.ylabel(metric.upper())
        plt.grid(axis='y', linestyle='--', alpha=0.7)
        save_path = os.path.join('lstm_results', f'lstm_comparison_{metric}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
    
    # 比较不同线路的训练历史
    plt.figure(figsize=(12, 8))
    for line in lines:
        plt.plot(all_histories[line]['val_loss'], label=f'线路 {line}')
    
    plt.title('不同线路的LSTM验证损失比较')
    plt.xlabel('Epoch')
    plt.ylabel('验证损失')
    plt.legend()
    plt.grid(True)
    save_path = os.path.join('lstm_results', 'lstm_all_validation_loss.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

def main():
    """主函数"""
    # 创建保存结果和模型的目录
    os.makedirs('lstm_results', exist_ok=True)
    os.makedirs('model', exist_ok=True)
    
    # 加载数据
    _, merged_data, top_lines = load_data()
    
    # 提取轨道交通数据并添加LINE列
    metro_df = extract_metro_data(merged_data)
    
    # 存储所有线路的结果
    all_results = {}
    all_histories = {}
    
    # 为每条线路训练模型
    for line_no in top_lines:
        print(f"\n{'='*50}")
        print(f"处理线路 {line_no}")
        print(f"{'='*50}")
        
        # 准备数据 - 使用最近4个时间段预测未来1个时间段
        train_loader, val_loader, test_loader, scaler = prepare_data(
            metro_df, line_no, seq_length=4, pred_length=1
        )
        
        # 创建LSTM模型
        model = TrafficForecastLSTM(
            input_size=1,  # 单变量时序
            hidden_size=64,
            num_layers=2,
            output_size=1,
            dropout=0.2
        )
        
        # 训练模型
        trained_model, history = train_model(
            model, train_loader, val_loader, line_no, epochs=50
        )
        
        # 评估模型
        results = evaluate_model(trained_model, test_loader, scaler, line_no)
        
        # 可视化训练历史
        visualize_training_history(history, line_no)
        
        # 可视化预测结果
        visualize_predictions(results, line_no)
        
        # 保存结果
        all_results[line_no] = results
        all_histories[line_no] = history
    
    # 可视化所有线路的比较结果
    visualize_all_results(all_results, all_histories)
    
    print("\n所有线路的LSTM模型训练和评估已完成！")
    print("生成的文件包括:")
    print("\n模型文件:")
    for line_no in top_lines:
        print(f"- model/lstm_model_line_{line_no}.pth: 线路 {line_no} 的LSTM模型权重")
    
    print("\n可视化结果:")
    for line_no in top_lines:
        print(f"- lstm_results/lstm_training_history_line_{line_no}.png: 线路 {line_no} 的训练历史")
        print(f"- lstm_results/lstm_predictions_line_{line_no}.png: 线路 {line_no} 的预测结果")
        print(f"- lstm_results/lstm_scatter_line_{line_no}.png: 线路 {line_no} 的预测值vs实际值散点图")
    
    print(f"- lstm_results/lstm_comparison_mse.png: 不同线路MSE比较")
    print(f"- lstm_results/lstm_comparison_rmse.png: 不同线路RMSE比较")
    print(f"- lstm_results/lstm_comparison_mae.png: 不同线路MAE比较")
    print(f"- lstm_results/lstm_comparison_r2.png: 不同线路R²比较")
    print(f"- lstm_results/lstm_all_validation_loss.png: 所有线路验证损失比较")

if __name__ == "__main__":
    main() 