#%% md
# # # 客户流失分析报告
# 
#%% md
# # ## 1. 数据加载与预处理
# 
#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.express as px
import plotly.graph_objects as go
import calmap
from datetime import datetime
import squarify
import warnings
warnings.filterwarnings('ignore')

# 设置中文显示
plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

# 加载数据
df = pd.read_csv('Customer-Churn-Records.csv')

# 显示数据基本信息
print("数据基本信息：")
print(df.info())

# 显示数据统计描述
print("\n数据统计描述：")
print(df.describe())

# 检查缺失值
print("\n缺失值统计：")
print(df.isnull().sum())

#%% md
# # ## 2. 数据可视化分析
# 
#%% md
# # ### 2.1 类别比较图
# # #### 2.1.1 客户流失情况的单变量柱状图
# 
#%%
plt.figure(figsize=(10, 6))
sns.countplot(data=df, x='Exited', palette='Set2')
plt.title('客户流失情况分布')
plt.xlabel('是否流失')
plt.ylabel('客户数量')

# 添加数值标签
for i in plt.gca().containers:
    plt.gca().bar_label(i)

plt.show()

# 计算流失率
churn_rate = df['Exited'].mean() * 100
print(f"\n客户流失率: {churn_rate:.2f}%")

#%% md
# # #### 2.1.2 不同信用卡类型的客户流失情况（簇状柱状图）
# 
#%%
plt.figure(figsize=(12, 6))
sns.countplot(data=df, x='Card Type', hue='Exited', palette='Set2')
plt.title('不同信用卡类型的客户流失情况')
plt.xlabel('信用卡类型')
plt.ylabel('客户数量')

# 添加数值标签
for i in plt.gca().containers:
    plt.gca().bar_label(i)

plt.legend(title='是否流失', labels=['未流失', '已流失'])
plt.xticks(rotation=45)
plt.show()

# 计算每种卡类型的流失率
card_churn_rate = df.groupby('Card Type')['Exited'].mean() * 100
print("\n各类型信用卡的流失率：")
print(card_churn_rate)

#%% md
# # #### 2.1.3 客户满意度与投诉情况（堆积柱状图）
# 
#%%
# 计算满意度和投诉的交叉统计
satisfaction_complaint = pd.crosstab(df['Satisfaction Score'], df['Complain'], normalize='index') * 100

# 绘制堆积柱状图
satisfaction_complaint.plot(kind='bar', stacked=True, figsize=(10, 6))
plt.title('客户满意度与投诉情况')
plt.xlabel('满意度评分')
plt.ylabel('百分比')
plt.legend(title='是否投诉', labels=['未投诉', '已投诉'])

# 添加数值标签
for c in plt.gca().containers:
    plt.gca().bar_label(c, fmt='%.1f%%')

plt.show()

#%% md
# # ### 2.2 时序趋势图
# # #### 2.2.1 客户年龄分布的折线图
# 
#%%
# 计算不同年龄段的客户数量
age_bins = [18, 25, 35, 45, 55, 65, 75]
df['Age_Group'] = pd.cut(df['Age'], bins=age_bins)
age_distribution = df['Age_Group'].value_counts().sort_index()

plt.figure(figsize=(12, 6))
plt.plot(range(len(age_distribution)), age_distribution.values, marker='o', linewidth=2)
plt.title('客户年龄分布')
plt.xlabel('年龄段')
plt.ylabel('客户数量')
plt.xticks(range(len(age_distribution)), [str(x) for x in age_distribution.index], rotation=45)

# 添加数值标签
for i, v in enumerate(age_distribution.values):
    plt.text(i, v, str(v), ha='center', va='bottom')

plt.grid(True, linestyle='--', alpha=0.7)
plt.show()

#%% md
# # ### 2.3 分布/关联图
# # #### 2.3.1 信用评分与年龄的散点图
# 
#%%
plt.figure(figsize=(12, 8))
sns.scatterplot(data=df, x='Age', y='CreditScore', hue='Exited', style='Exited', s=100)
plt.title('客户年龄与信用评分的关系')
plt.xlabel('年龄')
plt.ylabel('信用评分')
plt.legend(title='是否流失', labels=['未流失', '已流失'])
plt.show()

# 计算相关系数
correlation = df['Age'].corr(df['CreditScore'])
print(f"\n年龄与信用评分的相关系数: {correlation:.4f}")

#%% md
# # #### 2.3.2 不同地区客户余额的箱线图
# 
#%%
plt.figure(figsize=(12, 6))
sns.boxplot(data=df, x='Geography', y='Balance', hue='Exited')
plt.title('不同地区客户余额分布')
plt.xlabel('地区')
plt.ylabel('账户余额')
plt.legend(title='是否流失', labels=['未流失', '已流失'])
plt.show()

# 计算各地区的平均余额
print("\n各地区平均余额：")
print(df.groupby('Geography')['Balance'].mean())

#%% md
# # #### 2.3.3 客户满意度得分的小提琴图
# 
#%%
plt.figure(figsize=(12, 6))
sns.violinplot(data=df, x='Satisfaction Score', y='Balance', hue='Exited', split=True)
plt.title('客户满意度与账户余额分布')
plt.xlabel('满意度评分')
plt.ylabel('账户余额')
plt.legend(title='是否流失', labels=['未流失', '已流失'])
plt.show()

#%% md
# # ### 2.4 比例/结构图
# # #### 2.4.1 客户地理分布的饼图
# 
#%%
# 计算各地区客户比例
geography_dist = df['Geography'].value_counts()

plt.figure(figsize=(10, 8))
plt.pie(geography_dist, labels=geography_dist.index, autopct='%1.1f%%', 
        colors=sns.color_palette('Set3'), startangle=90)
plt.title('客户地理分布')
plt.axis('equal')
plt.show()

#%% md
# # #### 2.4.2 信用卡类型与活跃状态的马赛克图
# 
#%%
from statsmodels.graphics.mosaicplot import mosaic

plt.figure(figsize=(12, 8))
mosaic(df, ['Card Type', 'IsActiveMember'], title='信用卡类型与活跃状态关系')
plt.show()

#%% md
# # #### 2.4.3 客户投诉与流失的华夫饼图
# 
#%%
# 计算投诉和流失的组合比例
complaint_churn = pd.crosstab(df['Complain'], df['Exited'])
total = complaint_churn.sum().sum()

# 创建华夫饼图数据
waffle_data = []
for i in range(2):
    for j in range(2):
        value = complaint_churn.iloc[i, j]
        percentage = value / total * 100
        waffle_data.append({
            'category': f'{"投诉" if i == 1 else "未投诉"}-{"流失" if j == 1 else "未流失"}',
            'value': percentage
        })

# 绘制华夫饼图
plt.figure(figsize=(12, 8))
squarify.plot(sizes=[d['value'] for d in waffle_data],
              label=[d['category'] for d in waffle_data],
              alpha=.6,
              color=sns.color_palette("Set2", len(waffle_data)))
plt.title('客户投诉与流失关系（华夫饼图）')
plt.axis('off')
plt.show()

#%% md
# # ### 2.5 多维可视化
# # #### 2.5.1 客户特征的散点矩阵
# 
#%%
# 选择数值型特征
numeric_features = ['Age', 'CreditScore', 'Balance', 'EstimatedSalary']
scatter_matrix = df[numeric_features + ['Exited']]

# 绘制散点矩阵
plt.figure(figsize=(15, 15))
sns.pairplot(scatter_matrix, hue='Exited', diag_kind='hist')
plt.suptitle('客户特征散点矩阵', y=1.02)
plt.show()

#%% md
# # ### 2.6 地理专题图
# # #### 2.6.1 欧洲地区客户分布图
# 
#%%
# 创建地理分布数据
geo_data = df.groupby('Geography').agg({
    'CustomerId': 'count',
    'Exited': 'mean'
}).reset_index()

# 使用plotly创建地理地图
fig = go.Figure()

# 添加地理标记
fig.add_trace(go.Scattergeo(
    locations=['FRA', 'DEU', 'ESP'],  # ISO-3国家代码
    locationmode='ISO-3',
    text=geo_data.apply(lambda x: f"{x['Geography']}<br>客户数: {int(x['CustomerId']):,}<br>流失率: {x['Exited']:.2%}", axis=1),
    marker=dict(
        size=geo_data['CustomerId']/50,
        color=geo_data['Exited'],
        colorscale='Viridis',
        showscale=True,
        colorbar_title='流失率'
    ),
    hovertemplate='%{text}'
))

# 更新布局
fig.update_layout(
    title='欧洲地区客户分布与流失率',
    geo=dict(
        scope='europe',
        showland=True,
        landcolor='rgb(243, 243, 243)',
        countrycolor='rgb(204, 204, 204)',
        showframe=False,
        showcoastlines=True,
        projection_type='equirectangular'
    )
)

# 显示图表
fig.show()

#%% md
# # ## 3. 分析结论
# 
#%% md
# # ### 3.1 客户流失情况
# # 1. 总体流失率约为20.38%，需要关注
# # 2. 不同信用卡类型的客户流失率存在差异，DIAMOND卡客户流失率较高
# # 
# # ### 3.2 地理分布特征
# # 1. 客户主要分布在法国、德国和西班牙三个国家
# # 2. 德国客户的流失率相对较高
# # 3. 不同地区的客户余额分布存在差异
# # 
# # ### 3.3 客户特征分析
# # 1. 账户余额较高的客户流失风险较大
# # 2. 活跃客户的流失率明显低于非活跃客户
# # 
# # ### 3.4 建议
# # 1. 加强对DIAMOND卡客户的维护
# # 2. 提高客户满意度，特别是对投诉客户的处理
# # 3. 针对不同地区制定差异化的客户维护策略
# # 4. 对高余额账户客户提供个性化服务
# # 5. 增加客户活跃度，提高客户粘性 