{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-21T04:13:22.708912Z", "start_time": "2025-06-21T04:13:22.673631Z"}}, "source": ["#孔雨涵202456245 \n", "import pandas as pd\n", "import warnings\n", "import matplotlib.pyplot as plt\n", "warnings.filterwarnings('ignore')\n", "# 设置中文字体，避免中文显示乱码\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  \n", "plt.rcParams['axes.unicode_minus'] = False  \n"], "outputs": [], "execution_count": 45}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:23.460393Z", "start_time": "2025-06-21T04:13:23.413647Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "student_info = pd.read_excel('2024级会计7,8班学生名单.xlsx')\n", "print(student_info.head())\n"], "id": "fcf0e4b9e851dbc8", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          学号    院系   专业         班级   学校代码      学校\n", "0  202216003  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "1  202456235  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "2  202456236  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "3  202456237  会计学院  会计学  2024会计学07  10562  大连财经学院\n", "4  202456238  会计学院  会计学  2024会计学07  10562  大连财经学院\n"]}], "execution_count": 46}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:24.134161Z", "start_time": "2025-06-21T04:13:24.091599Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "homework_scores = pd.read_excel('2024级会计7,8班_课后作业成绩（前5次）.xlsx')\n", "print(homework_scores.head())\n"], "id": "e37ddfa007735c22", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          学号         班级  第1次作业  第2次作业  第3次作业  第4次作业  第5次作业\n", "0  202216003  2024会计学07    NaN    NaN    NaN    NaN    NaN\n", "1  202456235  2024会计学07   74.0   82.5   70.0  101.0  100.0\n", "2  202456236  2024会计学07   82.0   85.0  100.0  101.0   87.5\n", "3  202456237  2024会计学07   95.0   85.0   90.0  101.0   87.5\n", "4  202456238  2024会计学07   90.0   77.5   90.0   80.6   62.5\n"]}], "execution_count": 47}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:24.809904Z", "start_time": "2025-06-21T04:13:24.790903Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "# \"学号 班级\"列作为合并键\n", "merged_data = pd.merge(student_info, homework_scores, on=['学号','班级'], how='inner')\n"], "id": "d4a0bcf8e3675d3e", "outputs": [], "execution_count": 48}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:25.523050Z", "start_time": "2025-06-21T04:13:25.514919Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "print(merged_data.head())\n"], "id": "822332d4c6a7a59e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["          学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  第4次作业  \\\n", "0  202216003  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN    NaN   \n", "1  202456235  会计学院  会计学  2024会计学07  10562  大连财经学院   74.0   82.5   70.0  101.0   \n", "2  202456236  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0   85.0  100.0  101.0   \n", "3  202456237  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0   85.0   90.0  101.0   \n", "4  202456238  会计学院  会计学  2024会计学07  10562  大连财经学院   90.0   77.5   90.0   80.6   \n", "\n", "   第5次作业  \n", "0    NaN  \n", "1  100.0  \n", "2   87.5  \n", "3   87.5  \n", "4   62.5  \n"]}], "execution_count": 49}, {"metadata": {}, "cell_type": "markdown", "source": "# # 数据清洗", "id": "4696df2028131169"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:26.925058Z", "start_time": "2025-06-21T04:13:26.912033Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "duplicate_rows = merged_data.duplicated()\n", "print(\"是否存在重复行：\", duplicate_rows.any())"], "id": "cc9275e874c5c7bc", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在重复行： False\n"]}], "execution_count": 50}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:27.679378Z", "start_time": "2025-06-21T04:13:27.661847Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "# 检查空值\n", "null_values = merged_data.isnull()\n", "print(\"是否存在空值：\", null_values.any().any())\n", "if null_values.any().any():\n", "    print(\"每列的空值数量：\")\n", "    print(null_values.sum())\n", "    \n", "    rows_with_nulls = merged_data[merged_data.isnull().any(axis=1)]\n", "    print(\"包含空值的行数：\", len(rows_with_nulls))\n", "    if len(rows_with_nulls) > 0:\n", "        print(\"包含空值的行：\")\n", "        print(rows_with_nulls)\n"], "id": "88b2588e3cec1ed6", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["是否存在空值： True\n", "每列的空值数量：\n", "学号        0\n", "院系        0\n", "专业        0\n", "班级        0\n", "学校代码      0\n", "学校        0\n", "第1次作业    13\n", "第2次作业    13\n", "第3次作业    14\n", "第4次作业    15\n", "第5次作业    12\n", "dtype: int64\n", "包含空值的行数： 18\n", "包含空值的行：\n", "           学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  \\\n", "0   202216003  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "6   202456240  会计学院  会计学  2024会计学07  10562  大连财经学院  100.0   95.0   90.0   \n", "12  202456247  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "16  202456251  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "17  202456254  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "24  202456262  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "26  202456264  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "34  202456273  会计学院  会计学  2024会计学07  10562  大连财经学院    NaN    NaN    NaN   \n", "39  202456279  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   85.0    NaN   \n", "43  202456284  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0  100.0  100.0   \n", "44  202456285  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "48  202456289  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "53  202456294  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   90.0   70.0   \n", "54  202456296  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   87.5   60.0   \n", "56  202456298  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "58  202456300  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "59  202456302  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "68  202456312  会计学院  会计学  2024会计学08  10562  大连财经学院    NaN    NaN    NaN   \n", "\n", "    第4次作业  第5次作业  \n", "0     NaN    NaN  \n", "6     NaN  100.0  \n", "12    NaN    NaN  \n", "16    NaN    NaN  \n", "17    NaN    NaN  \n", "24    NaN    NaN  \n", "26    NaN    NaN  \n", "34    NaN    NaN  \n", "39    NaN   62.5  \n", "43    NaN  100.0  \n", "44    NaN    NaN  \n", "48   94.4   62.5  \n", "53    NaN   62.5  \n", "54   80.8    NaN  \n", "56    NaN    NaN  \n", "58    NaN    NaN  \n", "59    NaN    NaN  \n", "68   74.0   62.5  \n"]}], "execution_count": 51}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:28.440855Z", "start_time": "2025-06-21T04:13:28.434342Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "#空值填充为0分\n", "merged_data = merged_data.fillna(0)\n"], "id": "b0cbcad17b911461", "outputs": [], "execution_count": 52}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:29.258036Z", "start_time": "2025-06-21T04:13:29.243527Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "print(\"填充后是否还有空值：\", merged_data.isnull().any().any())\n"], "id": "4e75edd466c1ff21", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["填充后是否还有空值： False\n"]}], "execution_count": 53}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:29.984672Z", "start_time": "2025-06-21T04:13:29.960511Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "#分数列应该在0-100之间\n", "numeric_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        min_val = merged_data[col].min()\n", "        max_val = merged_data[col].max()\n", "        print(f\"列 '{col}' 的值范围: {min_val} - {max_val}\")\n", "        \n", "        # 检查是否有超出合理范围的值\n", "        if min_val < 0 or max_val > 100:\n", "            print(f\"警告: 列 '{col}' 中存在可能的异常值!\")\n", "            # 显示异常值\n", "            abnormal_values = merged_data[(merged_data[col] < 0) | (merged_data[col] > 100)]\n", "            if len(abnormal_values) > 0:\n", "                print(\"异常值所在行：\")\n", "                print(abnormal_values)\n"], "id": "bb639928bbd29f81", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["列 '第1次作业' 的值范围: 0.0 - 100.0\n", "列 '第2次作业' 的值范围: 0.0 - 100.0\n", "列 '第3次作业' 的值范围: 0.0 - 100.0\n", "列 '第4次作业' 的值范围: 0.0 - 101.0\n", "警告: 列 '第4次作业' 中存在可能的异常值!\n", "异常值所在行：\n", "           学号    院系   专业         班级   学校代码      学校  第1次作业  第2次作业  第3次作业  \\\n", "1   202456235  会计学院  会计学  2024会计学07  10562  大连财经学院   74.0   82.5   70.0   \n", "2   202456236  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0   85.0  100.0   \n", "3   202456237  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0   85.0   90.0   \n", "7   202456241  会计学院  会计学  2024会计学07  10562  大连财经学院   87.0   95.0   90.0   \n", "11  202456246  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0  100.0  100.0   \n", "13  202456248  会计学院  会计学  2024会计学07  10562  大连财经学院   95.0  100.0  100.0   \n", "15  202456250  会计学院  会计学  2024会计学07  10562  大连财经学院   87.0   95.0  100.0   \n", "20  202456258  会计学院  会计学  2024会计学07  10562  大连财经学院   82.0  100.0   90.0   \n", "36  202456275  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "42  202456282  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   95.0   90.0   \n", "49  202456290  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   95.0   90.0   \n", "50  202456291  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "51  202456292  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   92.5   80.0   \n", "55  202456297  会计学院  会计学  2024会计学08  10562  大连财经学院   90.0   95.0   90.0   \n", "60  202456303  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   90.0   90.0   \n", "61  202456304  会计学院  会计学  2024会计学08  10562  大连财经学院   87.0   95.0   80.0   \n", "63  202456307  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   95.0   80.0   \n", "64  202456308  会计学院  会计学  2024会计学08  10562  大连财经学院  100.0   95.0  100.0   \n", "65  202456309  会计学院  会计学  2024会计学08  10562  大连财经学院   95.0   90.0   90.0   \n", "\n", "    第4次作业  第5次作业  \n", "1   101.0  100.0  \n", "2   101.0   87.5  \n", "3   101.0   87.5  \n", "7   101.0  100.0  \n", "11  101.0  100.0  \n", "13  101.0   87.5  \n", "15  101.0   75.0  \n", "20  101.0   87.5  \n", "36  101.0   62.5  \n", "42  101.0  100.0  \n", "49  101.0  100.0  \n", "50  101.0   50.0  \n", "51  101.0   62.5  \n", "55  101.0  100.0  \n", "60  101.0  100.0  \n", "61  101.0   62.5  \n", "63  101.0   75.0  \n", "64  101.0  100.0  \n", "65  101.0  100.0  \n", "列 '第5次作业' 的值范围: 0.0 - 100.0\n"]}], "execution_count": 54}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:30.680407Z", "start_time": "2025-06-21T04:13:30.666340Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "# 处理异常值：小于0的替换为0，大于100的替换为100\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        # 将小于0的值替换为0\n", "        merged_data.loc[merged_data[col] < 0, col] = 0\n", "        # 将大于100的值替换为100\n", "        merged_data.loc[merged_data[col] > 100, col] = 100\n", "\n", "# 验证是否还有异常值\n", "print(\"\\n处理异常值后的验证：\")\n", "for col in numeric_columns:\n", "    if col not in ['学号','学校代码']:\n", "        min_val = merged_data[col].min()\n", "        max_val = merged_data[col].max()\n", "        print(f\"列 '{col}' 的值范围: {min_val} - {max_val}\")\n"], "id": "a9d82f4443ac17ed", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "处理异常值后的验证：\n", "列 '第1次作业' 的值范围: 0.0 - 100.0\n", "列 '第2次作业' 的值范围: 0.0 - 100.0\n", "列 '第3次作业' 的值范围: 0.0 - 100.0\n", "列 '第4次作业' 的值范围: 0.0 - 100.0\n", "列 '第5次作业' 的值范围: 0.0 - 100.0\n"]}], "execution_count": 55}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:31.723515Z", "start_time": "2025-06-21T04:13:31.678209Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245  \n", "#保存清洗后的数据\n", "merged_data.to_excel('孔雨涵202456245.xlsx', index=False)"], "id": "95ffb1ca1261ff34", "outputs": [], "execution_count": 56}, {"metadata": {}, "cell_type": "markdown", "source": "# # 数据分析 利用Pandas库对整合后的数据进行统计分析", "id": "4bd2348baf9928e1"}, {"metadata": {}, "cell_type": "markdown", "source": ["# # 个人学习成绩分析\n", "# # 计算本人的作业平均成绩、最高成绩、最低成绩"], "id": "8ca4094df8c38990"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:38.949365Z", "start_time": "2025-06-21T04:13:38.923060Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "my_student_id = 202456245\n", "my_data = merged_data[merged_data['学号'] == my_student_id]\n", "\n", "if len(my_data) == 0:\n", "    print(f\"未找到学号为 {my_student_id} 的学生数据\")\n", "else:\n", "    print(f\"学号为 {my_student_id} 的学生信息：\")\n", "    print(my_data[['学号']].iloc[0])\n", "    \n", "    score_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns.tolist()\n", "    score_columns = [col for col in score_columns if col not in ['学号', '学校代码']]\n", "    \n", "    my_scores = my_data[score_columns]\n", "    print(\"\\n本人各次作业成绩：\")\n", "    print(my_scores)\n", "    \n", "    # 计算平均成绩、最高成绩、最低成绩\n", "    avg_score = my_scores.mean(axis=1).iloc[0]\n", "    max_score = my_scores.max(axis=1).iloc[0]\n", "    min_score = my_scores.min(axis=1).iloc[0]\n", "    \n", "    print(f\"\\n本人成绩统计：\")\n", "    print(f\"平均成绩: {avg_score:.2f}\")\n", "    print(f\"最高成绩: {max_score:.2f}\")\n", "    print(f\"最低成绩: {min_score:.2f}\")\n"], "id": "b679914d4838e855", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["学号为 202456245 的学生信息：\n", "学号    202456245\n", "Name: 10, dtype: int64\n", "\n", "本人各次作业成绩：\n", "    第1次作业  第2次作业  第3次作业  第4次作业  第5次作业\n", "10   87.0  100.0   90.0   80.6   50.0\n", "\n", "本人成绩统计：\n", "平均成绩: 81.52\n", "最高成绩: 100.00\n", "最低成绩: 50.00\n"]}], "execution_count": 57}, {"metadata": {}, "cell_type": "markdown", "source": ["# # # 2. 班级学习数据分析\n", "# # # 分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少"], "id": "4cda199af96e6810"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:42.996271Z", "start_time": "2025-06-21T04:13:42.978602Z"}}, "cell_type": "code", "source": ["#孔雨涵202456245 \n", "my_class = my_data['班级'].iloc[0]\n", "\n", "# 筛选出同一班级的学生\n", "class_students = merged_data[merged_data['班级'] == my_class]\n", "print(f\"班级 {my_class} 共有 {len(class_students)} 名学生\")\n", "# 计算每个学生的平均分\n", "student_avg_scores = class_students[score_columns].mean(axis=1)\n", "class_students['平均分'] = student_avg_scores\n", "merged_data.loc[class_students.index, '平均分'] = student_avg_scores\n", "\n", "# 查看班级平均分的统计信息\n", "class_avg = student_avg_scores.mean()\n", "class_max = student_avg_scores.max()\n", "class_min = student_avg_scores.min()\n", "\n", "print(\"班级成绩统计：\")\n", "print(f\"班级平均分: {class_avg:.2f}\")\n", "print(f\"班级最高成绩: {class_max:.2f}\")\n", "print(f\"班级最低成绩: {class_min:.2f}\")\n"], "id": "dfc9048c4c422a37", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024会计学07 共有 35 名学生\n", "班级成绩统计：\n", "班级平均分: 70.88\n", "班级最高成绩: 99.00\n", "班级最低成绩: 0.00\n"]}], "execution_count": 58}, {"metadata": {}, "cell_type": "markdown", "source": "# # # 查找成绩优秀或需要辅导的学生", "id": "ce07799a9f5c17e6"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:44.688009Z", "start_time": "2025-06-21T04:13:44.662169Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "# 按平均分排序，找出班级内成绩最好的前10名学生\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "print(f\"班级 {my_class} 成绩优秀的前{top_n}名学生：\")\n", "print(top_students[['学号', '平均分']])\n", "\n", "# 找出班级内成绩最差的后10名学生\n", "bottom_students = class_students.sort_values(by='平均分').head(top_n)\n", "print(f\"\\n班级 {my_class} 需要辅导的后{top_n}名学生：\")\n", "print(bottom_students[['学号', '平均分']])\n"], "id": "2e57a20478368ab5", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024会计学07 成绩优秀的前10名学生：\n", "           学号    平均分\n", "11  202456246  99.00\n", "13  202456248  96.50\n", "9   202456244  95.84\n", "29  202456267  95.16\n", "22  202456260  94.84\n", "7   202456241  94.40\n", "23  202456261  94.34\n", "19  202456257  92.52\n", "21  202456259  92.48\n", "20  202456258  91.90\n", "\n", "班级 2024会计学07 需要辅导的后10名学生：\n", "           学号    平均分\n", "0   202216003   0.00\n", "12  202456247   0.00\n", "17  202456254   0.00\n", "16  202456251   0.00\n", "24  202456262   0.00\n", "26  202456264   0.00\n", "34  202456273   0.00\n", "6   202456240  77.00\n", "14  202456249  78.02\n", "4   202456238  80.12\n"]}], "execution_count": 59}, {"metadata": {}, "cell_type": "markdown", "source": "# # # 分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布", "id": "a04e9f5024b925fb"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:48.086836Z", "start_time": "2025-06-21T04:13:48.067489Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "# 定义分数段\n", "def get_score_range(score):\n", "    if score < 60:\n", "        return '60分以下'\n", "    elif 60 <= score < 70:\n", "        return '60-69分'\n", "    elif 70 <= score < 80:\n", "        return '70-79分'\n", "    elif 80 <= score < 90:\n", "        return '80-89分'\n", "    else:\n", "        return '90分以上'\n", "\n", "# 应用分数段函数到班级学生\n", "class_students['分数段'] = class_students['平均分'].apply(get_score_range)\n", "merged_data.loc[class_students.index, '分数段'] = class_students['分数段']\n", "\n", "# 统计班级内各分数段的学生人数\n", "score_range_counts = class_students['分数段'].value_counts().sort_index()\n", "print(f\"班级 {my_class} 各分数段学生人数分布：\")\n", "print(score_range_counts)\n", "\n", "# 计算班级内各分数段的百分比\n", "score_range_percentage = score_range_counts / len(class_students) * 100\n", "print(f\"\\n班级 {my_class} 各分数段学生人数百分比：\")\n", "for range_name, percentage in score_range_percentage.items():\n", "    print(f\"{range_name}: {percentage:.2f}%\")\n"], "id": "5dc6253d20136789", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["班级 2024会计学07 各分数段学生人数分布：\n", "分数段\n", "60分以下      7\n", "70-79分     2\n", "80-89分    11\n", "90分以上     15\n", "Name: count, dtype: int64\n", "\n", "班级 2024会计学07 各分数段学生人数百分比：\n", "60分以下: 20.00%\n", "70-79分: 5.71%\n", "80-89分: 31.43%\n", "90分以上: 42.86%\n"]}], "execution_count": 60}, {"metadata": {}, "cell_type": "markdown", "source": "# # # 数据可视化", "id": "78252f37cbcf6e67"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:49.924565Z", "start_time": "2025-06-21T04:13:49.911052Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "from matplotlib.gridspec import GridSpec"], "id": "c81a96ab6a4434da", "outputs": [], "execution_count": 61}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:50.865415Z", "start_time": "2025-06-21T04:13:50.568913Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "# 1. 绘制班级成绩Top10的柱状图\n", "plt.figure(figsize=(12, 6))\n", "top_n = min(10, len(class_students))\n", "top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)\n", "\n", "# 将学号转换为字符串，以便在图表中正确显示\n", "top_students_ids = [str(int(id)) for id in top_students['学号']]\n", "\n", "# 使用字符串类型的学号作为x轴标签\n", "bars = plt.bar(top_students_ids, top_students['平均分'], color='skyblue')\n", "plt.title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "plt.xlabel('学号')\n", "plt.ylabel('平均分')\n", "plt.ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.xticks(rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "            \n", "plt.tight_layout()\n", "plt.savefig('班级成绩Top10柱状图.png')\n", "plt.show()"], "id": "7de2a0ec993e2f35", "outputs": [{"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 62}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:57.255332Z", "start_time": "2025-06-21T04:13:57.058055Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）\n", "plt.figure(figsize=(12, 6))\n", "my_scores_values = my_scores.iloc[0].values\n", "plt.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "plt.title(f'学号 {my_student_id} 的成绩趋势图')\n", "plt.xlabel('作业序号')\n", "plt.ylabel('分数')\n", "plt.xticks(range(len(score_columns)), [f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "plt.grid(True)\n", "plt.ylim(0, 120)  # 设置y轴范围\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    plt.text(i, score + 2, f'{score:.1f}', ha='center')\n", "    \n", "plt.tight_layout()\n", "plt.savefig('个人成绩趋势图.png')\n", "plt.show()\n"], "id": "20fd8f2f2fbe45e", "outputs": [{"data": {"text/plain": ["<Figure size 1200x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 64}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:13:59.712016Z", "start_time": "2025-06-21T04:13:58.938758Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "# 3. 绘制本班级不同分数段比例图（饼图）\n", "plt.figure(figsize=(10, 8))\n", "# 确保分数段是按照正确顺序排列的\n", "ordered_ranges = ['60分以下', '60-69分', '70-79分', '80-89分', '90分以上']\n", "ordered_counts = [score_range_counts.get(range_name, 0) for range_name in ordered_ranges]\n", "\n", "# 定义更加美观的饼图颜色方案\n", "colors = ['#FF6347', '#FFA500', '#32CD32', '#1E90FF', '#9370DB']\n", "# 完全移除explode效果，使饼图更加紧凑\n", "explode = None\n", "\n", "# 绘制饼图，使用更加现代的样式\n", "wedges, texts, autotexts = plt.pie(\n", "    ordered_counts, \n", "    labels=None,  # 移除直接标签，改用图例显示\n", "    autopct=lambda p: f'{p:.1f}%' if p >= 1 else f'{p:.2f}%',  # 小于1%的显示两位小数\n", "    startangle=45,  # 调整起始角度\n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=False,\n", "    wedgeprops={'edgecolor': 'white', 'linewidth': 1.2, 'antialiased': True},\n", "    textprops={'fontsize': 10},\n", "    pctdistance=0.65,  # 将百分比文字放得更靠近中心\n", "    radius=0.8  # 稍微减小饼图半径，留出更多空间给图例\n", ")\n", "plt.title(f'班级 {my_class} 不同分数段学生比例', fontsize=18, fontweight='bold', pad=20)\n", "plt.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "for autotext in autotexts:\n", "    autotext.set_color('white')\n", "    autotext.set_fontweight('bold')\n", "    autotext.set_fontsize(9)\n", "\n", "# 添加增强的图例，确保小数值段也能清晰显示\n", "percentages = [count/sum(ordered_counts)*100 for count in ordered_counts]\n", "counts = [f\"({count}人)\" for count in ordered_counts]\n", "legend_labels = [f\"{r} {c} {p:.1f}%\" for r, c, p in zip(ordered_ranges, counts, percentages)]\n", "plt.legend(wedges, legend_labels, title=\"分数段分布\", loc=\"center left\", \n", "          bbox_to_anchor=(1.0, 0, 0.5, 1), fontsize=8, \n", "          title_fontsize=10, frameon=True, framealpha=0.8, \n", "          edgecolor='gray', fancybox=True)\n", "\n", "# 添加饼图中心的文字注释\n", "plt.annotate(f'总人数: {sum(ordered_counts)}',\n", "            xy=(0, 0), xytext=(0, 0),\n", "            ha='center', va='center',\n", "            fontsize=10, fontweight='bold')\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级分数段比例饼图.png', dpi=300, bbox_inches='tight')\n", "plt.show()\n"], "id": "9894b7c498b79dd5", "outputs": [{"data": {"text/plain": ["<Figure size 1000x800 with 1 Axes>"], "image/png": "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***************************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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 65}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:14:04.956232Z", "start_time": "2025-06-21T04:14:04.787362Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "# 4. 按班级分组，绘制两个班的平均成绩对比图\n", "plt.figure(figsize=(10, 6))\n", "\n", "# 获取所有班级\n", "all_classes = merged_data['班级'].unique()\n", "class_avg_scores = []\n", "\n", "# 计算每个班级的平均分\n", "for cls in all_classes:\n", "    cls_students = merged_data[merged_data['班级'] == cls]\n", "    cls_avg = cls_students[score_columns].mean().mean()\n", "    class_avg_scores.append({'班级': cls, '平均分': cls_avg})\n", "\n", "# 创建班级平均分数据框\n", "class_avg_df = pd.DataFrame(class_avg_scores)\n", "\n", "# 绘制班级平均分对比条形图\n", "colors = ['#66B2FF' if cls != my_class else '#FF9900' for cls in class_avg_df['班级']]\n", "bars = plt.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)\n", "plt.title('不同班级平均成绩对比')\n", "plt.xlabel('班级')\n", "plt.ylabel('平均分')\n", "plt.ylim(0, 100)\n", "plt.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "plt.tight_layout()\n", "plt.savefig('班级平均成绩对比图.png')\n", "plt.show()\n"], "id": "874bf4c15ebcaa9", "outputs": [{"data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 66}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-21T04:14:08.742654Z", "start_time": "2025-06-21T04:14:07.189998Z"}}, "cell_type": "code", "source": ["#  孔雨涵202456245 \n", "# 5. 将所有图表整合到一张完整的图纸中\n", "plt.figure(figsize=(20, 15))\n", "gs = GridSpec(3, 2, figure=plt.gcf())\n", "\n", "# 1. 绘制班级成绩Top10的柱状图\n", "ax1 = plt.subplot(gs[0, 0])\n", "\n", "# 确保使用字符串类型的学号作为x轴标签\n", "top_students_ids = [str(int(id)) for id in top_students['学号']]\n", "bars = ax1.bar(top_students_ids, top_students['平均分'], color=colors[:top_n])\n", "\n", "ax1.set_title(f'班级 {my_class} 成绩Top{top_n}学生')\n", "ax1.set_xlabel('学号')\n", "ax1.set_ylabel('平均分')\n", "ax1.set_ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围\n", "ax1.grid(axis='y', linestyle='--', alpha=0.7)\n", "plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')\n", "\n", "# 在柱状图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.1f}', ha='center', va='bottom')\n", "\n", "# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）\n", "ax2 = plt.subplot(gs[0, 1])\n", "ax2.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)\n", "ax2.set_title(f'学号 {my_student_id} 的成绩趋势图')\n", "ax2.set_xlabel('作业序号')\n", "ax2.set_ylabel('分数')\n", "ax2.set_xticks(range(len(score_columns)))\n", "ax2.set_xticklabels([f'作业{i+1}' for i in range(len(score_columns))], rotation=45)\n", "ax2.grid(True)\n", "ax2.set_ylim(0, 120)  # 设置y轴范围\n", "\n", "# 在趋势图上添加具体分数\n", "for i, score in enumerate(my_scores_values):\n", "    ax2.text(i, score + 2, f'{score:.1f}', ha='center')\n", "\n", "# 3. 绘制班级平均分对比条形图（放在左侧）\n", "ax3 = plt.subplot(gs[1, 0])\n", "\n", "# 绘制班级平均分对比条形图\n", "# 使用与班级数量相匹配的颜色\n", "class_colors = colors[:len(class_avg_df['班级'])]\n", "bars = ax3.bar(class_avg_df['班级'], class_avg_df['平均分'], color=class_colors)\n", "ax3.set_title('不同班级平均成绩对比')\n", "ax3.set_xlabel('班级')\n", "ax3.set_ylabel('平均分')\n", "ax3.set_ylim(0, 100)\n", "ax3.grid(axis='y', linestyle='--', alpha=0.7)\n", "\n", "# 在条形图上添加具体分数\n", "for bar in bars:\n", "    height = bar.get_height()\n", "    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,\n", "            f'{height:.2f}', ha='center', va='bottom')\n", "\n", "# 4. 绘制本班级不同分数段比例图（饼图）（放在右侧）\n", "ax4 = plt.subplot(gs[1, 1])\n", "wedges, texts, autotexts = ax4.pie(\n", "    ordered_counts, \n", "    labels=None,  # 移除直接标签，改用图例显示\n", "    autopct=lambda p: f'{p:.1f}%' if p >= 1 else f'{p:.2f}%',  # 小于1%的显示两位小数\n", "    startangle=45,  # 调整起始角度\n", "    colors=colors,\n", "    explode=explode,\n", "    shadow=False,\n", "    wedgeprops={'edgecolor': 'white', 'linewidth': 1.2, 'antialiased': True},\n", "    textprops={'fontsize': 10},\n", "    pctdistance=0.65,  # 将百分比文字放得更靠近中心\n", "    radius=0.8  # 稍微减小饼图半径，留出更多空间给图例\n", ")\n", "ax4.set_title(f'班级 {my_class} 不同分数段学生比例', fontsize=14, fontweight='bold')\n", "ax4.axis('equal')  # 保证饼图是圆形的\n", "\n", "# 设置饼图文本样式\n", "for autotext in autotexts:\n", "    autotext.set_color('white')\n", "    autotext.set_fontweight('bold')\n", "    autotext.set_fontsize(9)\n", "\n", "# 添加增强的图例，确保小数值段也能清晰显示\n", "percentages = [count/sum(ordered_counts)*100 for count in ordered_counts]\n", "counts = [f\"({count}人)\" for count in ordered_counts]\n", "legend_labels = [f\"{r} {c} {p:.1f}%\" for r, c, p in zip(ordered_ranges, counts, percentages)]\n", "ax4.legend(wedges, legend_labels, title=\"分数段分布\", loc=\"center left\", \n", "          bbox_to_anchor=(1.0, 0, 0.5, 1), fontsize=8, \n", "          title_fontsize=10, frameon=True, framealpha=0.8, \n", "          edgecolor='gray', fancybox=True)\n", "\n", "# 添加饼图中心的文字注释\n", "ax4.annotate(f'总人数: {sum(ordered_counts)}',\n", "            xy=(0, 0), xytext=(0, 0),\n", "            ha='center', va='center',\n", "            fontsize=10, fontweight='bold')\n", "\n", "# 添加总标题\n", "plt.suptitle('学生成绩分析报告', fontsize=20, fontweight='bold')\n", "\n", "plt.tight_layout(rect=[0, 0, 1, 0.96])  \n", "\n", "plt.savefig('学生成绩分析报告.png', dpi=300, bbox_inches='tight')\n", "plt.show() "], "id": "ffa644e082ceef6a", "outputs": [{"data": {"text/plain": ["<Figure size 2000x1500 with 4 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 67}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "e02f00d3134f1ac2"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}