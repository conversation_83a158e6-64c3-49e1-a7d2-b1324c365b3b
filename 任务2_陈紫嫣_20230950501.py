
class Student:
    """学生类，用于存储和验证学生信息"""
    
    def __init__(self, name, student_id, grade, contact):
        """
        初始化学生对象
        
        参数:
        name (str): 学生姓名
        student_id (str): 学号，必须以2024开头，长度8位
        grade (int): 年级，如2024
        contact (str): 联系方式，需包含@(邮箱)或11位数字(电话)
        """
        self.name = name
        self.student_id = student_id
        self.grade = grade
        self.contact = contact
    
    def validate(self):
        """
        验证学号和联系方式格式
        
        验证规则:
        - 学号必须以2024开头，长度8位
        - 联系方式需包含@(邮箱)或11位数字(电话)
        
        如果验证失败，抛出ValueError异常
        """
        # 验证学号
        if not self.student_id.startswith("2024") or len(self.student_id) != 8:
            raise ValueError(f"学号 {self.student_id} 格式错误，必须以2024开头且长度为8位")
        
        # 验证联系方式
        if "@" not in self.contact and (not self.contact.isdigit() or len(self.contact) != 11):
            raise ValueError(f"联系方式 {self.contact} 格式错误，必须为邮箱(包含@)或11位电话号码")
        
        return True

    def __str__(self):
        """返回学生信息的字符串表示"""
        return f"{self.student_id}|{self.name}|{self.grade}"


class Club:
    """社团类，用于管理社团成员和招新信息"""
    
    def __init__(self, club_name, quota):
        """
        初始化社团对象
        
        参数:
        club_name (str): 社团名称，如"Python爱好者协会"
        quota (int): 招新名额
        """
        self.club_name = club_name
        self.quota = quota
        self.members = []
    
    def add_member(self, student):
        """
        添加成员到社团
        
        参数:
        student (Student): 学生对象
        
        如果名额已满或验证失败，打印加入失败信息
        """
        if len(self.members) >= self.quota:
            print(f"加入失败：{self.club_name}名额已满")
            return False
        
        try:
            student.validate()
            self.members.append(student)
            print(f"成功添加 {student.name} 到 {self.club_name}")
            return True
        except ValueError as e:
            print(f"加入失败：{e}")
            return False
    
    def show_members(self):
        """按年级降序排列成员并打印"""
        if not self.members:
            print(f"{self.club_name}目前没有成员")
            return
        
        # 按年级降序排序
        sorted_members = sorted(self.members, key=lambda x: x.grade, reverse=True)
        
        print(f"\n{self.club_name}成员列表:")
        print("学号|姓名|年级")
        print("-" * 30)
        for student in sorted_members:
            print(student)
    
    def stat_by_grade(self):
        """
        统计各年级报名人数
        
        返回:
        dict: 包含各年级人数的字典，如 {2024:3, 2023:2}
        """
        stats = {}
        for student in self.members:
            if student.grade in stats:
                stats[student.grade] += 1
            else:
                stats[student.grade] = 1
        
        return stats


if __name__ == "__main__":
    # 创建社团
    python_club = Club("Python爱好者协会", 5)

    while True:
        print("\n======= Python爱好者协会招新系统 =======")
        print("1. 学生报名")
        print("2. 查看所有成员")
        print("3. 查看年级统计")
        print("0. 退出系统")
        print("======================================")

        choice = input("请选择功能(0-3): ")

        if choice == "1":
            # 学生报名
            name = input("请输入姓名: ")
            student_id = input("请输入学号(以2024开头，8位): ")

            try:
                grade = int(input("请输入年级: "))
            except ValueError:
                print("年级必须为数字")
                continue

            contact = input("请输入联系方式(邮箱或11位电话): ")

            student = Student(name, student_id, grade, contact)
            python_club.add_member(student)

        elif choice == "2":
            # 查看所有成员
            python_club.show_members()

        elif choice == "3":
            # 查看年级统计
            stats = python_club.stat_by_grade()

            if not stats:
                print("目前没有成员")
                continue

            print("\n各年级报名人数统计:")
            for grade, count in sorted(stats.items(), reverse=True):
                print(f"年级 {grade}: {count}人")

        elif choice == "0":
            # 退出系统
            print("感谢使用Python爱好者协会招新系统，再见！")
            break

        else:
            print("无效选择，请重新输入！")