# 初始化存储数据的字典
study_data = {}

def show_menu():
    print("\n====== 个人学习数据管理器 ======")
    print("1 - 添加数据")
    print("2 - 查看所有课程")
    print("3 - 查看课程时长最长的课程")
    print("4 - 查看自学时间最多的课程")
    print("0 - 退出")
    print("==============================")

def add_data():
    """添加课程数据"""
    course_name = input("请输入课程名称: ")
    
    # 如果课程已存在，询问是否覆盖
    if course_name in study_data:
        choice = input(f"课程 {course_name} 已存在，是否覆盖? (y/n): ")
        if choice.lower() != 'y':
            return
    
    # 初始化课程数据字典
    course_data = {}
    
    # 收集课程数据
    class_times = []
    print("请输入上课时间 (如'周一 8:00-10:00'，直接回车结束):")
    while True:
        try:
            class_time = input("> ")
            if class_time == "":
                break
            class_times.append(class_time)
        except EOFError:
            break
    
    course_data["上课时间"] = class_times
    
    try:
        course_duration = float(input("请输入课程总时长(小时): "))
        course_data["课程时长"] = course_duration
        
        self_study_time = float(input("请输入自学时间(小时): "))
        course_data["自学时间"] = self_study_time
        
        # 收集作业成绩
        scores = []
        print("请输入作业成绩 (0-100，直接回车结束):")
        while True:
            try:
                score_input = input("> ")
                if score_input == "":
                    break
                score = float(score_input)
                if 0 <= score <= 100:
                    scores.append(score)
                else:
                    print("成绩应在0-100之间！")
            except ValueError:
                print("请输入有效的数字！")
            except EOFError:
                break
        
        course_data["作业成绩"] = scores
        
        practice_time = float(input("请输入总练习时长(小时): "))
        course_data["总练习时长"] = practice_time
        
        error_count = int(input("请输入错题数量: "))
        course_data["错题数"] = error_count
        
        # 将课程数据添加到主字典中
        study_data[course_name] = course_data
        print(f"课程 {course_name} 数据已添加!")
        
    except ValueError:
        print("输入无效，请确保输入正确的数字类型！")

def show_all_courses():
    """显示所有课程的信息"""
    if not study_data:
        print("没有课程数据！")
        return
    
    print("\n=== 所有课程信息 ===")
    for course, data in study_data.items():
        print(f"\n课程: {course}")
        for key, value in data.items():
            if key == "作业成绩":
                avg_score = sum(value) / len(value) if value else 0
                print(f"  {key}: {value}, 平均分: {avg_score:.2f}")
            else:
                print(f"  {key}: {value}")

def find_longest_course_duration():
    """查找课程时长最长的课程"""
    if not study_data:
        print("没有课程数据！")
        return
    
    longest_course = max(study_data.items(), key=lambda x: x[1].get("课程时长", 0))
    
    print(f"\n课程时长最长的课程是: {longest_course[0]}")
    print(f"时长: {longest_course[1].get('课程时长', 0)} 小时")
    print("课程详细信息:")
    for key, value in longest_course[1].items():
        print(f"  {key}: {value}")

def find_most_self_study():
    """查找自学时间最多的课程"""
    if not study_data:
        print("没有课程数据！")
        return
    
    most_self_study = max(study_data.items(), key=lambda x: x[1].get("自学时间", 0))
    
    print(f"\n自学时间最多的课程是: {most_self_study[0]}")
    print(f"自学时间: {most_self_study[1].get('自学时间', 0)} 小时")
    print("课程详细信息:")
    for key, value in most_self_study[1].items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    while True:
        show_menu()
        choice = input("请选择功能(0-4): ")

        if choice == '1':
            add_data()
        elif choice == '2':
            show_all_courses()
        elif choice == '3':
            find_longest_course_duration()
        elif choice == '4':
            find_most_self_study()
        elif choice == '0':
            print("\n感谢使用学习数据管理器，再见！")
            break
        else:
            print("无效选择，请重新输入！")