#%%
#孔雨涵202456245 
import pandas as pd
import warnings
import matplotlib.pyplot as plt
warnings.filterwarnings('ignore')
# 设置中文字体，避免中文显示乱码
plt.rcParams['font.sans-serif'] = ['SimHei']  
plt.rcParams['axes.unicode_minus'] = False  

#%%
#孔雨涵202456245 
student_info = pd.read_excel('2024级会计7,8班学生名单.xlsx')
print(student_info.head())

#%%
#孔雨涵202456245 
homework_scores = pd.read_excel('2024级会计7,8班_课后作业成绩（前5次）.xlsx')
print(homework_scores.head())

#%%
#孔雨涵202456245 
# "学号 班级"列作为合并键
merged_data = pd.merge(student_info, homework_scores, on=['学号','班级'], how='inner')

#%%
#孔雨涵202456245 
print(merged_data.head())

#%% md
# # 数据清洗
#%%
#孔雨涵202456245 
duplicate_rows = merged_data.duplicated()
print("是否存在重复行：", duplicate_rows.any())
#%%
#孔雨涵202456245 
# 检查空值
null_values = merged_data.isnull()
print("是否存在空值：", null_values.any().any())
if null_values.any().any():
    print("每列的空值数量：")
    print(null_values.sum())
    
    rows_with_nulls = merged_data[merged_data.isnull().any(axis=1)]
    print("包含空值的行数：", len(rows_with_nulls))
    if len(rows_with_nulls) > 0:
        print("包含空值的行：")
        print(rows_with_nulls)

#%%
#孔雨涵202456245 
#空值填充为0分
merged_data = merged_data.fillna(0)

#%%
#孔雨涵202456245 
print("填充后是否还有空值：", merged_data.isnull().any().any())

#%%
#孔雨涵202456245 
#分数列应该在0-100之间
numeric_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns
for col in numeric_columns:
    if col not in ['学号','学校代码']:
        min_val = merged_data[col].min()
        max_val = merged_data[col].max()
        print(f"列 '{col}' 的值范围: {min_val} - {max_val}")
        
        # 检查是否有超出合理范围的值
        if min_val < 0 or max_val > 100:
            print(f"警告: 列 '{col}' 中存在可能的异常值!")
            # 显示异常值
            abnormal_values = merged_data[(merged_data[col] < 0) | (merged_data[col] > 100)]
            if len(abnormal_values) > 0:
                print("异常值所在行：")
                print(abnormal_values)

#%%
#  孔雨涵202456245 
# 处理异常值：小于0的替换为0，大于100的替换为100
for col in numeric_columns:
    if col not in ['学号','学校代码']:
        # 将小于0的值替换为0
        merged_data.loc[merged_data[col] < 0, col] = 0
        # 将大于100的值替换为100
        merged_data.loc[merged_data[col] > 100, col] = 100

# 验证是否还有异常值
print("\n处理异常值后的验证：")
for col in numeric_columns:
    if col not in ['学号','学校代码']:
        min_val = merged_data[col].min()
        max_val = merged_data[col].max()
        print(f"列 '{col}' 的值范围: {min_val} - {max_val}")

#%%
#孔雨涵202456245  
#保存清洗后的数据
merged_data.to_excel('孔雨涵202456245.xlsx', index=False)
#%% md
# # 数据分析 利用Pandas库对整合后的数据进行统计分析
#%% md
# # 个人学习成绩分析
# # 计算本人的作业平均成绩、最高成绩、最低成绩
#%%
#孔雨涵202456245 
my_student_id = 202456243
my_data = merged_data[merged_data['学号'] == my_student_id]

if len(my_data) == 0:
    print(f"未找到学号为 {my_student_id} 的学生数据")
else:
    print(f"学号为 {my_student_id} 的学生信息：")
    print(my_data[['学号']].iloc[0])
    
    score_columns = merged_data.select_dtypes(include=['float64', 'int64']).columns.tolist()
    score_columns = [col for col in score_columns if col not in ['学号', '学校代码']]
    
    my_scores = my_data[score_columns]
    print("\n本人各次作业成绩：")
    print(my_scores)
    
    # 计算平均成绩、最高成绩、最低成绩
    avg_score = my_scores.mean(axis=1).iloc[0]
    max_score = my_scores.max(axis=1).iloc[0]
    min_score = my_scores.min(axis=1).iloc[0]
    
    print(f"\n本人成绩统计：")
    print(f"平均成绩: {avg_score:.2f}")
    print(f"最高成绩: {max_score:.2f}")
    print(f"最低成绩: {min_score:.2f}")

#%% md
# # # 2. 班级学习数据分析
# # # 分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少
#%%
#孔雨涵202456245 
my_class = my_data['班级'].iloc[0]

# 筛选出同一班级的学生
class_students = merged_data[merged_data['班级'] == my_class]
print(f"班级 {my_class} 共有 {len(class_students)} 名学生")
# 计算每个学生的平均分
student_avg_scores = class_students[score_columns].mean(axis=1)
class_students['平均分'] = student_avg_scores
merged_data.loc[class_students.index, '平均分'] = student_avg_scores

# 查看班级平均分的统计信息
class_avg = student_avg_scores.mean()
class_max = student_avg_scores.max()
class_min = student_avg_scores.min()

print("班级成绩统计：")
print(f"班级平均分: {class_avg:.2f}")
print(f"班级最高成绩: {class_max:.2f}")
print(f"班级最低成绩: {class_min:.2f}")

#%% md
# # # 查找成绩优秀或需要辅导的学生
#%%
#  孔雨涵202456245 
# 按平均分排序，找出班级内成绩最好的前10名学生
top_n = min(10, len(class_students))
top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)
print(f"班级 {my_class} 成绩优秀的前{top_n}名学生：")
print(top_students[['学号', '平均分']])

# 找出班级内成绩最差的后10名学生
bottom_students = class_students.sort_values(by='平均分').head(top_n)
print(f"\n班级 {my_class} 需要辅导的后{top_n}名学生：")
print(bottom_students[['学号', '平均分']])

#%% md
# # # 分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布
#%%
#  孔雨涵202456245 
# 定义分数段
def get_score_range(score):
    if score < 60:
        return '60分以下'
    elif 60 <= score < 70:
        return '60-69分'
    elif 70 <= score < 80:
        return '70-79分'
    elif 80 <= score < 90:
        return '80-89分'
    else:
        return '90分以上'

# 应用分数段函数到班级学生
class_students['分数段'] = class_students['平均分'].apply(get_score_range)
merged_data.loc[class_students.index, '分数段'] = class_students['分数段']

# 统计班级内各分数段的学生人数
score_range_counts = class_students['分数段'].value_counts().sort_index()
print(f"班级 {my_class} 各分数段学生人数分布：")
print(score_range_counts)

# 计算班级内各分数段的百分比
score_range_percentage = score_range_counts / len(class_students) * 100
print(f"\n班级 {my_class} 各分数段学生人数百分比：")
for range_name, percentage in score_range_percentage.items():
    print(f"{range_name}: {percentage:.2f}%")

#%% md
# # # 数据可视化
#%%
#  孔雨涵202456245 
from matplotlib.gridspec import GridSpec
#%%
#  孔雨涵202456245 
# 1. 绘制班级成绩Top10的柱状图
plt.figure(figsize=(12, 6))
top_n = min(10, len(class_students))
top_students = class_students.sort_values(by='平均分', ascending=False).head(top_n)

# 将学号转换为字符串，以便在图表中正确显示
top_students_ids = [str(int(id)) for id in top_students['学号']]

# 使用字符串类型的学号作为x轴标签
bars = plt.bar(top_students_ids, top_students['平均分'], color='skyblue')
plt.title(f'班级 {my_class} 成绩Top{top_n}学生')
plt.xlabel('学号')
plt.ylabel('平均分')
plt.ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围
plt.grid(axis='y', linestyle='--', alpha=0.7)
plt.xticks(rotation=45, ha='right')

# 在柱状图上添加具体分数
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
            f'{height:.1f}', ha='center', va='bottom')
            
plt.tight_layout()
plt.savefig('班级成绩Top10柱状图.png')
plt.show()
#%%
#  孔雨涵202456245 
# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）
plt.figure(figsize=(12, 6))
my_scores_values = my_scores.iloc[0].values
plt.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)
plt.title(f'学号 {my_student_id} 的成绩趋势图')
plt.xlabel('作业序号')
plt.ylabel('分数')
plt.xticks(range(len(score_columns)), [f'作业{i+1}' for i in range(len(score_columns))], rotation=45)
plt.grid(True)
plt.ylim(0, 120)  # 设置y轴范围

# 在趋势图上添加具体分数
for i, score in enumerate(my_scores_values):
    plt.text(i, score + 2, f'{score:.1f}', ha='center')
    
plt.tight_layout()
plt.savefig('个人成绩趋势图.png')
plt.show()

#%%
#  孔雨涵202456245 
# 3. 绘制本班级不同分数段比例图（饼图）
plt.figure(figsize=(10, 8))
# 确保分数段是按照正确顺序排列的
ordered_ranges = ['60分以下', '60-69分', '70-79分', '80-89分', '90分以上']
ordered_counts = [score_range_counts.get(range_name, 0) for range_name in ordered_ranges]

# 定义更加美观的饼图颜色方案
colors = ['#FF6347', '#FFA500', '#32CD32', '#1E90FF', '#9370DB']
# 完全移除explode效果，使饼图更加紧凑
explode = None

# 绘制饼图，使用更加现代的样式
wedges, texts, autotexts = plt.pie(
    ordered_counts, 
    labels=None,  # 移除直接标签，改用图例显示
    autopct=lambda p: f'{p:.1f}%' if p >= 1 else f'{p:.2f}%',  # 小于1%的显示两位小数
    startangle=45,  # 调整起始角度
    colors=colors,
    explode=explode,
    shadow=False,
    wedgeprops={'edgecolor': 'white', 'linewidth': 1.2, 'antialiased': True},
    textprops={'fontsize': 10},
    pctdistance=0.65,  # 将百分比文字放得更靠近中心
    radius=0.8  # 稍微减小饼图半径，留出更多空间给图例
)
plt.title(f'班级 {my_class} 不同分数段学生比例', fontsize=18, fontweight='bold', pad=20)
plt.axis('equal')  # 保证饼图是圆形的

# 设置饼图文本样式
for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')
    autotext.set_fontsize(9)

# 添加增强的图例，确保小数值段也能清晰显示
percentages = [count/sum(ordered_counts)*100 for count in ordered_counts]
counts = [f"({count}人)" for count in ordered_counts]
legend_labels = [f"{r} {c} {p:.1f}%" for r, c, p in zip(ordered_ranges, counts, percentages)]
plt.legend(wedges, legend_labels, title="分数段分布", loc="center left", 
          bbox_to_anchor=(1.0, 0, 0.5, 1), fontsize=8, 
          title_fontsize=10, frameon=True, framealpha=0.8, 
          edgecolor='gray', fancybox=True)

# 添加饼图中心的文字注释
plt.annotate(f'总人数: {sum(ordered_counts)}',
            xy=(0, 0), xytext=(0, 0),
            ha='center', va='center',
            fontsize=10, fontweight='bold')

plt.tight_layout()
plt.savefig('班级分数段比例饼图.png', dpi=300, bbox_inches='tight')
plt.show()

#%%
#  孔雨涵202456245 
# 4. 按班级分组，绘制两个班的平均成绩对比图
plt.figure(figsize=(10, 6))

# 获取所有班级
all_classes = merged_data['班级'].unique()
class_avg_scores = []

# 计算每个班级的平均分
for cls in all_classes:
    cls_students = merged_data[merged_data['班级'] == cls]
    cls_avg = cls_students[score_columns].mean().mean()
    class_avg_scores.append({'班级': cls, '平均分': cls_avg})

# 创建班级平均分数据框
class_avg_df = pd.DataFrame(class_avg_scores)

# 绘制班级平均分对比条形图
colors = ['#66B2FF' if cls != my_class else '#FF9900' for cls in class_avg_df['班级']]
bars = plt.bar(class_avg_df['班级'], class_avg_df['平均分'], color=colors)
plt.title('不同班级平均成绩对比')
plt.xlabel('班级')
plt.ylabel('平均分')
plt.ylim(0, 100)
plt.grid(axis='y', linestyle='--', alpha=0.7)

# 在条形图上添加具体分数
for bar in bars:
    height = bar.get_height()
    plt.text(bar.get_x() + bar.get_width()/2., height + 0.5,
            f'{height:.2f}', ha='center', va='bottom')

plt.tight_layout()
plt.savefig('班级平均成绩对比图.png')
plt.show()

#%%
#  孔雨涵202456245 
# 5. 将所有图表整合到一张完整的图纸中
plt.figure(figsize=(20, 15))
gs = GridSpec(3, 2, figure=plt.gcf())

# 1. 绘制班级成绩Top10的柱状图
ax1 = plt.subplot(gs[0, 0])

# 确保使用字符串类型的学号作为x轴标签
top_students_ids = [str(int(id)) for id in top_students['学号']]
bars = ax1.bar(top_students_ids, top_students['平均分'], color=colors[:top_n])

ax1.set_title(f'班级 {my_class} 成绩Top{top_n}学生')
ax1.set_xlabel('学号')
ax1.set_ylabel('平均分')
ax1.set_ylim(top_students['平均分'].min() - 5, 100)  # 设置y轴范围
ax1.grid(axis='y', linestyle='--', alpha=0.7)
plt.setp(ax1.get_xticklabels(), rotation=45, ha='right')

# 在柱状图上添加具体分数
for bar in bars:
    height = bar.get_height()
    ax1.text(bar.get_x() + bar.get_width()/2., height + 0.5,
            f'{height:.1f}', ha='center', va='bottom')

# 2. 绘制学生本人成绩的趋势图（按照作业完成时间顺序）
ax2 = plt.subplot(gs[0, 1])
ax2.plot(range(len(score_columns)), my_scores_values, marker='o', linestyle='-', color='b', linewidth=2)
ax2.set_title(f'学号 {my_student_id} 的成绩趋势图')
ax2.set_xlabel('作业序号')
ax2.set_ylabel('分数')
ax2.set_xticks(range(len(score_columns)))
ax2.set_xticklabels([f'作业{i+1}' for i in range(len(score_columns))], rotation=45)
ax2.grid(True)
ax2.set_ylim(0, 120)  # 设置y轴范围

# 在趋势图上添加具体分数
for i, score in enumerate(my_scores_values):
    ax2.text(i, score + 2, f'{score:.1f}', ha='center')

# 3. 绘制班级平均分对比条形图（放在左侧）
ax3 = plt.subplot(gs[1, 0])

# 绘制班级平均分对比条形图
# 使用与班级数量相匹配的颜色
class_colors = colors[:len(class_avg_df['班级'])]
bars = ax3.bar(class_avg_df['班级'], class_avg_df['平均分'], color=class_colors)
ax3.set_title('不同班级平均成绩对比')
ax3.set_xlabel('班级')
ax3.set_ylabel('平均分')
ax3.set_ylim(0, 100)
ax3.grid(axis='y', linestyle='--', alpha=0.7)

# 在条形图上添加具体分数
for bar in bars:
    height = bar.get_height()
    ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
            f'{height:.2f}', ha='center', va='bottom')

# 4. 绘制本班级不同分数段比例图（饼图）（放在右侧）
ax4 = plt.subplot(gs[1, 1])
wedges, texts, autotexts = ax4.pie(
    ordered_counts, 
    labels=None,  # 移除直接标签，改用图例显示
    autopct=lambda p: f'{p:.1f}%' if p >= 1 else f'{p:.2f}%',  # 小于1%的显示两位小数
    startangle=45,  # 调整起始角度
    colors=colors,
    explode=explode,
    shadow=False,
    wedgeprops={'edgecolor': 'white', 'linewidth': 1.2, 'antialiased': True},
    textprops={'fontsize': 10},
    pctdistance=0.65,  # 将百分比文字放得更靠近中心
    radius=0.8  # 稍微减小饼图半径，留出更多空间给图例
)
ax4.set_title(f'班级 {my_class} 不同分数段学生比例', fontsize=14, fontweight='bold')
ax4.axis('equal')  # 保证饼图是圆形的

# 设置饼图文本样式
for autotext in autotexts:
    autotext.set_color('white')
    autotext.set_fontweight('bold')
    autotext.set_fontsize(9)

# 添加增强的图例，确保小数值段也能清晰显示
percentages = [count/sum(ordered_counts)*100 for count in ordered_counts]
counts = [f"({count}人)" for count in ordered_counts]
legend_labels = [f"{r} {c} {p:.1f}%" for r, c, p in zip(ordered_ranges, counts, percentages)]
ax4.legend(wedges, legend_labels, title="分数段分布", loc="center left", 
          bbox_to_anchor=(1.0, 0, 0.5, 1), fontsize=8, 
          title_fontsize=10, frameon=True, framealpha=0.8, 
          edgecolor='gray', fancybox=True)

# 添加饼图中心的文字注释
ax4.annotate(f'总人数: {sum(ordered_counts)}',
            xy=(0, 0), xytext=(0, 0),
            ha='center', va='center',
            fontsize=10, fontweight='bold')

# 添加总标题
plt.suptitle('学生成绩分析报告', fontsize=20, fontweight='bold')

# 调整子图布局
plt.tight_layout(rect=[0, 0, 1, 0.96])  # 为顶部标题留出空间

# 保存整合后的图表
plt.savefig('学生成绩分析报告.png', dpi=300, bbox_inches='tight')
plt.show() 
#%%
