# 地铁客流预测系统

本项目实现了基于LSTM和FNN深度学习模型的地铁客流预测系统，通过分析历史刷卡数据，预测未来时段的客流量。项目主要包括三个模块：数据处理、FNN模型预测和LSTM模型预测。

## 项目结构

```
├── merge_data.py              # 数据处理和合并模块
├── metro_flow_fnn_prediction.py  # 基于FNN的客流预测模块
├── metro_flow_lstm_prediction.py # 基于LSTM的客流预测模块
├── metroFlow.npy              # 处理后的时间片客流数据
├── line_flow_stats.csv        # 线路流量统计数据
├── merged_data_with_stations.csv # 合并后的刷卡数据与站点信息
├── 轨道站点.txt                # 原始站点信息数据
├── chunk_*.csv                # 原始刷卡数据（分块）
├── model/                     # 保存训练好的模型
│   ├── fnn_model_line_*.pth   # FNN模型权重文件
│   └── lstm_model_line_*.pth  # LSTM模型权重文件
├── fnn_results/               # FNN模型预测结果和可视化
└── lstm_results/              # LSTM模型预测结果和可视化
```

## 数据处理流程 (merge_data.py)

该模块负责数据的处理、合并和初步分析，主要功能包括：

1. **数据合并**：合并多个`chunk_*.csv`文件，将所有刷卡记录汇总
2. **站点信息整合**：读取`轨道站点.txt`，将站点信息与刷卡记录关联
3. **数据清洗和转换**：处理日期时间格式，提取小时、分钟信息
4. **统计分析**：
   - 按线路统计客流量
   - 按站点统计客流量
   - 按时段统计客流量
   - 按乘客统计乘坐次数
5. **时间片数据生成**：将数据按15分钟为一个时间片进行集计，生成`metroFlow.npy`
   - 每天划分为96个时间片（每15分钟一个时间片）
   - 生成形状为[日期数, 96]的NumPy数组

### 主要数据统计指标和可视化结果：

- `line_flow_stats.csv`和`line_flow_stats.png`：线路流量统计
- `station_flow_stats.csv`和`station_flow_stats.png`：站点流量统计
- `hour_flow_stats.csv`和`hour_flow_stats.png`：时段流量统计
- `passenger_stats.csv`和`passenger_ride_distribution.png`：乘客统计
- `hour_paid_scatter.png`和`station_flow_paid_scatter.png`：散点图关系分析
- `time_slice_flow.png`：某一天的时间片流量可视化

## FNN模型预测 (metro_flow_fnn_prediction.py)

该模块基于前馈神经网络(FNN)实现地铁客流量预测，主要特点：

1. **模型架构**：多层前馈神经网络，包含两个隐藏层（64和32个神经元）
2. **预测策略**：使用连续4个时间片（每个15分钟）的数据预测下一个时间片的客流量
3. **数据处理**：
   - 筛选流量最高的4条线路
   - 数据归一化（MinMaxScaler）
   - 划分训练集(70%)、验证集(20%)、测试集(10%)
4. **训练过程**：
   - 优化器：Adam
   - 损失函数：MSE
   - 早停策略：验证集损失10轮不改善则停止
5. **评估指标**：MSE、RMSE、MAE、R²

### 结果输出：

- 模型权重文件：`model/fnn_model_line_*.pth`
- 训练历史可视化：`fnn_results/fnn_training_history_line_*.png`
- 预测结果可视化：`fnn_results/fnn_predictions_line_*.png`
- 预测vs实际散点图：`fnn_results/fnn_scatter_line_*.png`
- 不同线路指标比较：`fnn_results/fnn_comparison_*.png`
- 验证损失比较：`fnn_results/fnn_all_validation_loss.png`

## LSTM模型预测 (metro_flow_lstm_prediction.py)

该模块基于长短期记忆网络(LSTM)实现地铁客流量预测，主要特点：

1. **模型架构**：
   - 2层LSTM网络，隐藏层大小为64
   - Dropout率0.2（防止过拟合）
   - 线性输出层
2. **预测策略**：与FNN相同，使用连续4个时间片（1小时）预测下一个时间片
3. **数据处理**：
   - 与FNN相同的数据准备流程
   - 数据形状调整为LSTM输入格式[批次大小, 序列长度, 特征维度]
4. **训练过程**：
   - 优化器：Adam
   - 损失函数：MSE
   - 早停策略：验证集损失10轮不改善则停止
5. **评估指标**：MSE、RMSE、MAE、R²

### 结果输出：

- 模型权重文件：`model/lstm_model_line_*.pth`
- 训练历史可视化：`lstm_results/lstm_training_history_line_*.png`
- 预测结果可视化：`lstm_results/lstm_predictions_line_*.png`
- 预测vs实际散点图：`lstm_results/lstm_scatter_line_*.png`
- 不同线路指标比较：`lstm_results/lstm_comparison_*.png`
- 验证损失比较：`lstm_results/lstm_all_validation_loss.png`

