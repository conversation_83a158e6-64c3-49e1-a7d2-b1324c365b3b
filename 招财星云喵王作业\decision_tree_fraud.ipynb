{"cells": [{"cell_type": "code", "execution_count": 1, "id": "initial_id", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:17:42.768311Z", "start_time": "2025-06-20T04:17:42.602812Z"}, "collapsed": true, "jupyter": {"outputs_hidden": true}}, "outputs": [], "source": ["import numpy as np\n", "import pandas as pd\n", "import os\n", "import matplotlib.pyplot as plt\n", "from sklearn.tree import DecisionTreeClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score, precision_score, recall_score, f1_score, roc_auc_score\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.preprocessing import StandardScaler\n", "\n", "# 设置支持中文的字体\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体\n", "plt.rcParams['axes.unicode_minus'] = False    # 解决负号显示为方块的问题\n"]}, {"cell_type": "code", "execution_count": 2, "id": "c0a6297e5b36da6b", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:17:45.217091Z", "start_time": "2025-06-20T04:17:45.187644Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["正在加载训练集和测试集...\n", "训练集形状: (1051, 37)\n", "测试集形状: (263, 37)\n"]}], "source": ["# 加载数据集\n", "print(\"正在加载训练集和测试集...\")\n", "train_data = pd.read_csv('train_dataset.csv')\n", "test_data = pd.read_csv('test_dataset.csv')\n", "\n", "print(f\"训练集形状: {train_data.shape}\")\n", "print(f\"测试集形状: {test_data.shape}\")\n"]}, {"cell_type": "code", "execution_count": 3, "id": "713d8826d0ca0ba0", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:17:48.094416Z", "start_time": "2025-06-20T04:17:48.042954Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集信息:\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 1051 entries, 0 to 1050\n", "Data columns (total 37 columns):\n", " #   Column                    Non-Null Count  Dtype  \n", "---  ------                    --------------  -----  \n", " 0   retainedps                1051 non-null   float64\n", " 1   undist_profit_ps          1051 non-null   float64\n", " 2   dt_eps                    1051 non-null   float64\n", " 3   diluted2_eps              1051 non-null   float64\n", " 4   eps                       1051 non-null   float64\n", " 5   bps                       1051 non-null   float64\n", " 6   asset_liability_ratio     1051 non-null   float64\n", " 7   op_income                 1051 non-null   float64\n", " 8   ROA                       1051 non-null   float64\n", " 9   surplus_rese_ps           1051 non-null   float64\n", " 10  current_ratio             1051 non-null   float64\n", " 11  revenue_ps                1051 non-null   float64\n", " 12  total_revenue_ps          1051 non-null   float64\n", " 13  assets_turn               1051 non-null   float64\n", " 14  cash_ratio                1051 non-null   float64\n", " 15  quick_ratio               1051 non-null   float64\n", " 16  ebit                      1051 non-null   float64\n", " 17  gross_margin              1051 non-null   float64\n", " 18  ebitda                    1051 non-null   float64\n", " 19  ca_turn                   1051 non-null   float64\n", " 20  YearlyReturn              1051 non-null   float64\n", " 21  ocfps                     1051 non-null   float64\n", " 22  balance_total_liab        1051 non-null   float64\n", " 23  profit_dedt               1051 non-null   float64\n", " 24  balance_total_assets      1051 non-null   float64\n", " 25  working_capital           1051 non-null   float64\n", " 26  netdebt                   1051 non-null   float64\n", " 27  capital_rese_ps           1051 non-null   float64\n", " 28  income_n_income           1051 non-null   float64\n", " 29  income_n_income_attr_p    1051 non-null   float64\n", " 30  tangible_asset            1051 non-null   float64\n", " 31  income_total_profit       1051 non-null   float64\n", " 32  balance_total_cur_assets  1051 non-null   float64\n", " 33  retained_earnings         1051 non-null   float64\n", " 34  ts_code                   1051 non-null   object \n", " 35  Year                      1051 non-null   int64  \n", " 36  <PERSON>aud                     1051 non-null   int64  \n", "dtypes: float64(34), int64(2), object(1)\n", "memory usage: 303.9+ KB\n", "None\n", "\n", "训练集前5行:\n", "   retainedps  undist_profit_ps  dt_eps  diluted2_eps     eps      bps  \\\n", "0      1.1164            0.9608  0.1100        0.1109  0.1100   3.7009   \n", "1      1.2090            1.0770 -0.3500       -0.3461 -0.3500   2.4929   \n", "2      3.0078            2.6415  1.0258        0.8814  1.0258   8.6105   \n", "3     -0.0289           -0.1596 -0.7200       -0.7119 -0.7200   2.0756   \n", "4     -4.0350           -4.2533 -4.5800       -4.5053 -4.5800  12.5961   \n", "\n", "   asset_liability_ratio     op_income       ROA  surplus_rese_ps  ...  \\\n", "0               0.471469  1.819389e+07  0.016129           0.1762  ...   \n", "1               0.672423 -3.656246e+08 -0.047306           0.1320  ...   \n", "2               0.240237  3.339408e+08  0.076192           0.3664  ...   \n", "3               0.219064 -9.316420e+07 -0.267834           0.1307  ...   \n", "4               0.296793 -7.483568e+08 -0.251521           0.2182  ...   \n", "\n", "   capital_rese_ps  income_n_income  income_n_income_attr_p  tangible_asset  \\\n", "0          1.72705     5.548743e+07            5.548743e+07    1.379907e+09   \n", "1          0.27720    -2.718577e+08           -2.593686e+08    1.172815e+09   \n", "2          4.74580     4.335897e+08            4.127845e+08    3.528523e+09   \n", "3          1.20440    -9.295003e+07           -9.295003e+07    2.213683e+08   \n", "4          1.72705    -7.265015e+08           -7.265015e+08    1.817812e+09   \n", "\n", "   income_total_profit  balance_total_cur_assets  retained_earnings  \\\n", "0         6.513511e+07              1.999424e+09       4.355837e+08   \n", "1        -3.642062e+08              4.391611e+09       9.060814e+08   \n", "2         4.877873e+08              3.061688e+09       1.408699e+09   \n", "3        -8.954797e+07              2.917267e+08      -3.776810e+06   \n", "4        -7.257310e+08              1.577525e+09      -6.506668e+08   \n", "\n", "     ts_code  Year  Fraud  \n", "0  300767.SZ  2025      1  \n", "1  300208.SZ  2019      1  \n", "2  300119.SZ  2021      1  \n", "3  300736.SZ  2019      0  \n", "4  688184.SH  2024      1  \n", "\n", "[5 rows x 37 columns]\n", "\n", "欺诈样本分布:\n", "<PERSON><PERSON>\n", "1    526\n", "0    525\n", "Name: count, dtype: int64\n", "欺诈率: 0.5005\n"]}], "source": ["# 查看数据集信息\n", "print(\"训练集信息:\")\n", "print(train_data.info())\n", "print(\"\\n训练集前5行:\")\n", "print(train_data.head())\n", "\n", "print(\"\\n欺诈样本分布:\")\n", "print(train_data['Fraud'].value_counts())\n", "print(f\"欺诈率: {train_data['Fraud'].mean():.4f}\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "94480f0a94906d53", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:17:52.908528Z", "start_time": "2025-06-20T04:17:52.801168Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练特征形状: (1051, 34)\n", "测试特征形状: (263, 34)\n"]}], "source": ["# 数据预处理\n", "def preprocess_data(df):\n", "    # 分离特征和目标变量\n", "    X = df.drop(['Fraud', 'ts_code', 'Year'], axis=1)\n", "    y = df['Fraud']\n", "    \n", "    # 处理缺失值\n", "    X = <PERSON>.fillna(X.median())\n", "    \n", "    return X, y\n", "\n", "X_train, y_train = preprocess_data(train_data)\n", "X_test, y_test = preprocess_data(test_data)\n", "\n", "print(f\"训练特征形状: {X_train.shape}\")\n", "print(f\"测试特征形状: {X_test.shape}\")\n"]}, {"cell_type": "code", "execution_count": 5, "id": "8f017819c9619f76", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:18:07.260273Z", "start_time": "2025-06-20T04:18:07.230697Z"}}, "outputs": [], "source": ["# 特征标准化\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "# 转换回DataFrame以保留列名\n", "X_train_scaled = pd.DataFrame(X_train_scaled, columns=X_train.columns)\n", "X_test_scaled = pd.DataFrame(X_test_scaled, columns=X_test.columns)\n"]}, {"cell_type": "code", "execution_count": 7, "id": "d58382d2f6ff43ad", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:20:47.540743Z", "start_time": "2025-06-20T04:20:47.495156Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练基础决策树模型...\n", "模型训练完成\n"]}], "source": ["# 基础决策树模型\n", "print(\"训练基础决策树模型...\")\n", "dt_model = DecisionTreeClassifier(random_state=42)\n", "dt_model.fit(X_train_scaled, y_train)\n", "\n", "# 在测试集上进行预测\n", "y_pred = dt_model.predict(X_test_scaled)\n", "y_pred_proba = dt_model.predict_proba(X_test_scaled)[:, 1]\n", "print(\"模型训练完成\")\n"]}, {"cell_type": "code", "execution_count": 8, "id": "4850689215f71f24", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:20:50.758214Z", "start_time": "2025-06-20T04:20:50.534568Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== 基础决策树模型评估 =====\n", "准确率: 0.6160\n", "精确率: 0.6103\n", "召回率: 0.6336\n", "F1分数: 0.6217\n", "AUC分数: 0.6160\n", "\n", "混淆矩阵:\n", "[[79 53]\n", " [48 83]]\n", "\n", "分类报告:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.62      0.60      0.61       132\n", "           1       0.61      0.63      0.62       131\n", "\n", "    accuracy                           0.62       263\n", "   macro avg       0.62      0.62      0.62       263\n", "weighted avg       0.62      0.62      0.62       263\n", "\n"]}], "source": ["# 评估基础模型\n", "print(\"\\n===== 基础决策树模型评估 =====\")\n", "print(f\"准确率: {accuracy_score(y_test, y_pred):.4f}\")\n", "print(f\"精确率: {precision_score(y_test, y_pred):.4f}\")\n", "print(f\"召回率: {recall_score(y_test, y_pred):.4f}\")\n", "print(f\"F1分数: {f1_score(y_test, y_pred):.4f}\")\n", "print(f\"AUC分数: {roc_auc_score(y_test, y_pred_proba):.4f}\")\n", "\n", "print(\"\\n混淆矩阵:\")\n", "cm = confusion_matrix(y_test, y_pred)\n", "print(cm)\n", "\n", "print(\"\\n分类报告:\")\n", "print(classification_report(y_test, y_pred))\n"]}, {"cell_type": "code", "execution_count": 9, "id": "25fe16860513ba93", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:20:57.800911Z", "start_time": "2025-06-20T04:20:57.376230Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x800 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "前10个最重要的特征:\n", "                       特征       重要性\n", "20           YearlyReturn  0.114864\n", "7               op_income  0.077114\n", "10          current_ratio  0.054858\n", "26                netdebt  0.053701\n", "6   asset_liability_ratio  0.050482\n", "21                  ocfps  0.049999\n", "15            quick_ratio  0.047086\n", "33      retained_earnings  0.044848\n", "5                     bps  0.041533\n", "17           gross_margin  0.040948\n"]}], "source": ["# 特征重要性分析\n", "feature_importance = pd.DataFrame({\n", "    '特征': X_train.columns,\n", "    '重要性': dt_model.feature_importances_\n", "})\n", "feature_importance = feature_importance.sort_values('重要性', ascending=False)\n", "\n", "# 只展示前20个最重要的特征\n", "top_features = feature_importance.head(20)\n", "\n", "plt.figure(figsize=(12, 8))\n", "plt.barh(top_features['特征'][::-1], top_features['重要性'][::-1])\n", "plt.title('决策树模型 - 特征重要性 (Top 20)')\n", "plt.xlabel('重要性')\n", "plt.ylabel('特征')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"\\n前10个最重要的特征:\")\n", "print(feature_importance.head(10))\n"]}, {"cell_type": "code", "execution_count": 10, "id": "2dafb22036d98092", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:21:03.629278Z", "start_time": "2025-06-20T04:21:03.615860Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["特征重要性已保存到 'feature_importance.csv'\n"]}], "source": ["# 保存特征重要性到CSV\n", "feature_importance.to_csv('feature_importance.csv', index=False)\n", "print(\"特征重要性已保存到 'feature_importance.csv'\")\n"]}, {"cell_type": "code", "execution_count": 11, "id": "9c884c37022d03e9", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:21:15.733081Z", "start_time": "2025-06-20T04:21:06.730010Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "开始参数调优...\n", "Fitting 5 folds for each of 224 candidates, totalling 1120 fits\n", "最佳参数: {'criterion': 'gini', 'max_depth': 3, 'min_samples_leaf': 8, 'min_samples_split': 2}\n", "最佳交叉验证分数: 0.5907\n"]}], "source": ["# 使用GridSearchCV进行参数调优\n", "print(\"\\n开始参数调优...\")\n", "param_grid = {\n", "    'max_depth': [3, 5, 7, 10, 15, 20, None],\n", "    'min_samples_split': [2, 5, 10, 20],\n", "    'min_samples_leaf': [1, 2, 4, 8],\n", "    'criterion': ['gini', 'entropy']\n", "}\n", "\n", "grid_search = GridSearchCV(\n", "    estimator=DecisionTreeClassifier(random_state=42),\n", "    param_grid=param_grid,\n", "    cv=5,\n", "    scoring='f1',\n", "    n_jobs=-1,\n", "    verbose=1\n", ")\n", "\n", "grid_search.fit(X_train_scaled, y_train)\n", "\n", "print(f\"最佳参数: {grid_search.best_params_}\")\n", "print(f\"最佳交叉验证分数: {grid_search.best_score_:.4f}\")\n"]}, {"cell_type": "code", "execution_count": 12, "id": "f4f28f1c64c12737", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:36:38.842723Z", "start_time": "2025-06-20T04:36:38.808377Z"}}, "outputs": [], "source": ["# 使用最佳参数训练模型\n", "best_dt_model = grid_search.best_estimator_\n", "best_dt_model.fit(X_train_scaled, y_train)\n", "\n", "# 在测试集上进行预测\n", "y_pred_best = best_dt_model.predict(X_test_scaled)\n", "y_pred_proba_best = best_dt_model.predict_proba(X_test_scaled)[:, 1]\n"]}, {"cell_type": "code", "execution_count": 13, "id": "6b46e299e6f2a6cf", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:36:44.613290Z", "start_time": "2025-06-20T04:36:44.583095Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== 优化后的决策树模型评估 =====\n", "准确率: 0.6236\n", "精确率: 0.6270\n", "召回率: 0.6031\n", "F1分数: 0.6148\n", "AUC分数: 0.6456\n", "\n", "混淆矩阵:\n", "[[85 47]\n", " [52 79]]\n", "\n", "分类报告:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.62      0.64      0.63       132\n", "           1       0.63      0.60      0.61       131\n", "\n", "    accuracy                           0.62       263\n", "   macro avg       0.62      0.62      0.62       263\n", "weighted avg       0.62      0.62      0.62       263\n", "\n"]}], "source": ["# 评估优化后的模型\n", "print(\"\\n===== 优化后的决策树模型评估 =====\")\n", "print(f\"准确率: {accuracy_score(y_test, y_pred_best):.4f}\")\n", "print(f\"精确率: {precision_score(y_test, y_pred_best):.4f}\")\n", "print(f\"召回率: {recall_score(y_test, y_pred_best):.4f}\")\n", "print(f\"F1分数: {f1_score(y_test, y_pred_best):.4f}\")\n", "print(f\"AUC分数: {roc_auc_score(y_test, y_pred_proba_best):.4f}\")\n", "\n", "print(\"\\n混淆矩阵:\")\n", "cm_best = confusion_matrix(y_test, y_pred_best)\n", "print(cm_best)\n", "\n", "print(\"\\n分类报告:\")\n", "print(classification_report(y_test, y_pred_best))\n"]}, {"cell_type": "code", "execution_count": 14, "id": "ce701a8f580e1880", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:36:50.763799Z", "start_time": "2025-06-20T04:36:50.492347Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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*********************************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", "text/plain": ["<Figure size 800x600 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 可视化混淆矩阵\n", "def plot_confusion_matrix(cm, title):\n", "    plt.figure(figsize=(8, 6))\n", "    plt.imshow(cm, interpolation='nearest', cmap=plt.cm.Blues)\n", "    plt.title(title)\n", "    plt.colorbar()\n", "    \n", "    classes = ['非欺诈 (0)', '欺诈 (1)']\n", "    tick_marks = np.arange(len(classes))\n", "    plt.xticks(tick_marks, classes, rotation=45)\n", "    plt.yticks(tick_marks, classes)\n", "    \n", "    # 在格子中显示数字\n", "    thresh = cm.max() / 2.\n", "    for i in range(cm.shape[0]):\n", "        for j in range(cm.shape[1]):\n", "            plt.text(j, i, format(cm[i, j], 'd'),\n", "                    horizontalalignment=\"center\",\n", "                    color=\"white\" if cm[i, j] > thresh else \"black\")\n", "    \n", "    plt.ylabel('真实标签')\n", "    plt.xlabel('预测标签')\n", "    plt.tight_layout()\n", "\n", "# 绘制基础模型的混淆矩阵\n", "plot_confusion_matrix(cm, '基础决策树模型 - 混淆矩阵')\n", "plt.show()\n", "\n", "# 绘制优化模型的混淆矩阵\n", "plot_confusion_matrix(cm_best, '优化决策树模型 - 混淆矩阵')\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 15, "id": "e2cf9f6f6261c29b", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:36:56.016486Z", "start_time": "2025-06-20T04:36:55.995099Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "===== 模型比较 =====\n", "指标              基础决策树           优化决策树          \n", "---------------------------------------------\n", "准确率             0.6160          0.6236         \n", "精确率             0.6103          0.6270         \n", "召回率             0.6336          0.6031         \n", "F1分数            0.6217          0.6148         \n", "AUC分数           0.6160          0.6456         \n"]}], "source": ["# 模型比较\n", "print(\"\\n===== 模型比较 =====\")\n", "print(f\"{'指标':<15} {'基础决策树':<15} {'优化决策树':<15}\")\n", "print(\"-\" * 45)\n", "print(f\"{'准确率':<15} {accuracy_score(y_test, y_pred):<15.4f} {accuracy_score(y_test, y_pred_best):<15.4f}\")\n", "print(f\"{'精确率':<15} {precision_score(y_test, y_pred):<15.4f} {precision_score(y_test, y_pred_best):<15.4f}\")\n", "print(f\"{'召回率':<15} {recall_score(y_test, y_pred):<15.4f} {recall_score(y_test, y_pred_best):<15.4f}\")\n", "print(f\"{'F1分数':<15} {f1_score(y_test, y_pred):<15.4f} {f1_score(y_test, y_pred_best):<15.4f}\")\n", "print(f\"{'AUC分数':<15} {roc_auc_score(y_test, y_pred_proba):<15.4f} {roc_auc_score(y_test, y_pred_proba_best):<15.4f}\")\n"]}, {"cell_type": "code", "execution_count": 16, "id": "cd5ba15504e4bef3", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:37:11.099556Z", "start_time": "2025-06-20T04:37:11.054299Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型已保存到: model\\decision_tree_fraud_model.pkl\n", "特征缩放器已保存到: model\\dt_scaler.pkl\n"]}], "source": ["# 保存模型\n", "import joblib\n", "\n", "model_dir = 'model'\n", "if not os.path.exists(model_dir):\n", "    os.makedirs(model_dir)\n", "\n", "# 保存优化后的模型\n", "model_path = os.path.join(model_dir, 'decision_tree_fraud_model.pkl')\n", "joblib.dump(best_dt_model, model_path)\n", "print(f\"模型已保存到: {model_path}\")\n", "\n", "# 保存scaler以便后续使用\n", "scaler_path = os.path.join(model_dir, 'dt_scaler.pkl')\n", "joblib.dump(scaler, scaler_path)\n", "print(f\"特征缩放器已保存到: {scaler_path}\")\n"]}, {"cell_type": "code", "execution_count": 17, "id": "eb558b097b80085b", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T04:37:24.662723Z", "start_time": "2025-06-20T04:37:24.059644Z"}}, "outputs": [{"data": {"image/png": "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***************************************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", "text/plain": ["<Figure size 2000x1000 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["决策树模型训练和评估完成。\n"]}], "source": ["# 可视化决策树（限制深度，便于可视化）\n", "from sklearn import tree\n", "\n", "# 创建一个深度有限的决策树用于可视化\n", "viz_model = DecisionTreeClassifier(max_depth=3, random_state=42)\n", "viz_model.fit(X_train_scaled, y_train)\n", "\n", "plt.figure(figsize=(20, 10))\n", "tree.plot_tree(viz_model, \n", "               feature_names=X_train.columns,\n", "               class_names=['非欺诈', '欺诈'],\n", "               filled=True, \n", "               rounded=True)\n", "plt.title('决策树可视化 (限制深度为3)')\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "print(\"决策树模型训练和评估完成。\")"]}, {"cell_type": "code", "execution_count": null, "id": "e7a87392cab81b9a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}