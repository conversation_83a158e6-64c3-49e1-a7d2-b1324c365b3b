import pygame
from pygame.sprite import Sprite

class Bullet(Sprite):
    """管理飞船所发射子弹的类"""
    
    def __init__(self, ai_game):
        """在飞船当前位置创建一个子弹对象"""
        super().__init__()
        self.screen = ai_game.screen
        self.settings = ai_game.settings
        self.color = self.settings.bullet_color
        
        # 在(0,0)处创建一个表示子弹的矩形，再设置正确的位置
        self.rect = pygame.Rect(0, 0, self.settings.bullet_width,
            self.settings.bullet_height)
        self.rect.midtop = ai_game.ship.rect.midtop
        
        # 存储用小数表示的子弹位置
        self.y = float(self.rect.y)
        
        # 子弹光晕效果的变量
        self.glow_size = self.settings.bullet_width * 2
        self.glow_color = (255, 200, 100)  # 橙黄色光晕
        
    def update(self):
        """向上移动子弹"""
        # 更新表示子弹位置的小数值
        self.y -= self.settings.bullet_speed
        # 更新表示子弹的rect的位置
        self.rect.y = self.y
        
    def draw_bullet(self):
        """在屏幕上绘制子弹"""
        # 先绘制发光效果
        glow_rect = pygame.Rect(
            self.rect.centerx - self.glow_size // 2,
            self.rect.centery - self.glow_size // 2,
            self.glow_size, self.glow_size
        )
        
        # 使用半透明效果创建光晕
        s = pygame.Surface((self.glow_size, self.glow_size), pygame.SRCALPHA)
        pygame.draw.circle(s, (*self.glow_color, 100), (self.glow_size//2, self.glow_size//2), self.glow_size//2)
        self.screen.blit(s, glow_rect)
        
        # 然后绘制实际子弹
        pygame.draw.rect(self.screen, self.color, self.rect) 