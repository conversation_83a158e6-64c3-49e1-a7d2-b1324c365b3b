#%% md
# # 逻辑回归案例分析
#%% md
# ## 泰坦尼克号生存分析
#%% md
# ### 变量解释
#  
#  
# 
# | 变量名    | 变量描述 | 
# |-----------|-------------|
# | PassengerID    | 乘客id  |  
# | Survived  |标记乘客是否幸存，幸存（1），死亡（0）|
# | Pclass   |标记乘客所属船层，第一层（1），第二层（2），第三层（3）         | 
# | Name | 乘客名字           |
# | Sex     | 乘客性别，男male、女female  |
# | Age    |  乘客年龄 |
# | Sibsp  | 船上兄弟姐妹和配偶的数量 |
# | Parch    |船上父母和孩子的数量 |
# | Ticket   | 乘客的船票号码|
# | Fare  | 乘客为船票付了多少钱|
# | Cabin    |乘客住在哪个船舱|
# |Embarked  |乘客从哪个地方登上泰坦尼克号 |
# 
#  
#%%
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
#%%
titanic_train=pd.read_csv("F:/Python/files/titanic_train.csv")
#%% md
# ### 查看数据
#%%
titanic_train.head()#前5个样本
#%% md
# ### 描述性统计
# - 分类型变量：Survived、Sex、Embarked、Pclass
# - 数值型变量： Age、Fare、SibSp、Parch
#%%
titanic_train.info()
#%% md
# **comments：**
# - Age、Cabin、embarked存在缺失值，需要进行处理。
#%% md
# - 对数值型变量进行分析
#%%
titanic_train.describe().T
#%% md
# **comments：**
# - 训练集有891个数据
# - 变量Survived是分类变量（0或1），样本存活率约为38%。
#%% md
# - 对文字型变量进行分析
#%%
titanic_train.describe(include=['O']).T
#%% md
# **comments：**
# - 65%为男性
# - 有三个登船口。大部分通过s口登船
#%% md
# ### 交叉分析
# 用分类类型的自变量和因变量Survived分别做交叉分析
#%% md
# #### Survived vs. Pclass
#%%
titanic_train[['Pclass', 'Survived']].groupby(['Pclass'],as_index=False).mean().sort_values(by='Survived',ascending=False)
#%% md
# **comments:**
# - 第一层的乘客生还率为62.96%，第三层仅有24.24%
#%% md
# #### Survived vs. Sex
#%%
titanic_train[["Sex", "Survived"]].groupby(['Sex'], as_index=False).mean().sort_values(by='Survived', ascending=False)
#%% md
# **comments:**
# - 女性的生存率为74.20%高于男性
#%% md
# #### Survived vs. Embarked
#%%
titanic_train[["Embarked", "Survived"]].groupby(['Embarked'], as_index=False).mean().sort_values(by='Survived', ascending=False)
#%% md
# **comments:**
# - 从C口登船的生存率为55.35%，而登船人数最多的S口的生存率仅有33.70%
#%% md
# ### 数据可视化分析
#%% md
# #### Survived vs. Age
#%%
import seaborn as sns
import matplotlib.pyplot as plt
%matplotlib inline
age_map = sns.FacetGrid(titanic_train, col='Survived')
age_map.map(plt.hist, 'Age', bins=20)
#%% md
# **comments:**
# - 四岁以下的婴儿存活率较高。
# - 大量15-25岁的人没有存活下来
# - 大多数乘客的年龄在15~35岁之间。
#%% md
# #### Survived vs. Fare
#%%
fare_map = sns.FacetGrid(titanic_train, col='Survived')
fare_map.map(plt.hist, 'Fare', bins=20)
#%% md
# **comments：**
# - 票价低的乘客生存率更低
# - 票价高的乘客的生存率相对高一些
#%% md
# #### Survived vs. SipSp
#%%
sipsp_map = sns.FacetGrid(titanic_train, col='Survived')
sipsp_map.map(plt.hist, 'SibSp', bins=20)
#%% md
# **comments:**
# - 两变量间无明显关系
#%% md
# #### Survived vs. Parch
#%%
parch_map = sns.FacetGrid(titanic_train, col='Survived')
parch_map.map(plt.hist, 'Parch', bins=20)
#%% md
# **comments:**
# - 两变量间无明显关系
#%% md
# ## 处理数据
# 
# - 将分类型变量变为数值型变量
# - 将存在缺失值的变量进行缺失值处理
#   - Age、Embarked、Fare 进行缺失值的替换
#   - 删除无关变量
#%% md
# 对**Sex**进行处理，将分类型变量转换为数值型变量
#%%
titanic_train['Sex'] = titanic_train['Sex'].map( {'female': 1, 'male': 0} ).astype(int)
titanic_train.head()
#%% md
# - Age和Fare的缺失值用中位数填充`median()`
# - Embarked的缺失值用众数进行填充`mode()[0]`
# - 删除PassengerID、Cabin、Name和Ticket四个无关变量
# 
# 
# - 均值填充`mean()`
# - 删除空值`dropna()`
#%%
titanic_train['Age'].fillna(titanic_train['Age'].median(), inplace = True)
titanic_train['Embarked'].fillna(titanic_train['Embarked'].mode()[0], inplace = True)
titanic_train['Fare'].fillna(titanic_train['Fare'].median(), inplace =True)
titanic_train.drop(['PassengerId','Cabin', 'Ticket','Name'], axis=1, inplace = True)

titanic_train.info()

#%% md
# - 将SipSp和Parch变量合并为家庭数量
#%%
titanic_train['FamilySize'] =titanic_train['SibSp'] + titanic_train['Parch'] + 1
#%% md
# -  对登船口进行数值型转换
#%%
#编码
from sklearn.preprocessing import LabelEncoder
label = LabelEncoder()
titanic_train['Embarked_Code'] = label.fit_transform(titanic_train['Embarked'])
#%%
titanic_train.head()
#%% md
# ### 模型开发
#%% md
# - 选择数据
#%%
Target = ['Survived']
titanic_train_x_bin = ['Sex','Pclass', 'Embarked_Code', 'FamilySize', 'Age', 'Fare']
titanic_train_xy_bin = Target + titanic_train_x_bin
print('Bin X Y: ', titanic_train_xy_bin, '\n')
#%%
#Model Algorithms
from sklearn import linear_model
#Common Model Helpers
from sklearn import feature_selection
from sklearn import model_selection
from sklearn import metrics
#%% md
# - 对训练集进行分割。70%的训练集，30%的测试集
#%%
#Visualization
import matplotlib as mpl
import matplotlib.pyplot as plt
import matplotlib.pylab as pylab
import seaborn as sns
train1_x_bin,test1_x_bin,train1_y_bin,test1_y_bin =model_selection.train_test_split(titanic_train[titanic_train_x_bin],titanic_train[Target],test_size=0.3,random_state = 0)
print("Data1 Shape: {}".format(titanic_train.shape))
print("Train1 X Shape: {}".format(train1_x_bin.shape))
print("Test1 X Shape: {}".format(test1_x_bin.shape))
train1_x_bin.head()
#%% md
# #### 建模
#%%
from sklearn.linear_model import LogisticRegression
logreg=LogisticRegression(C=1e9)
#%%
from sklearn import linear_model
logreg = linear_model.LogisticRegression(C=1e9)
logreg.fit(train1_x_bin, train1_y_bin)
Y_pred = logreg.predict(test1_x_bin)
#%%
print(logreg.intercept_) #截距
coef=pd.DataFrame(logreg.coef_).T  #参数
columns=pd.DataFrame(train1_x_bin.columns,columns=['A'])
result = pd.concat([columns,coef], axis=1)
result = result.rename(columns={'A': 'Attribute', 0: 'Coefficients'})
result
#%%
import statsmodels.api as sm
log_reg = sm.Logit(train1_y_bin,sm.add_constant(train1_x_bin)).fit()
log_reg.summary()
#%% md
# **comments：**
# - Fare的P值大于0.05，接受原假设。即表示船票价格与生存率无关，无统计学意义。需要删去该变量。
# - Embarked的P值略大于0.05，在后续建模中需要进行进一步的讨论。
#%%
Target1 = ['Survived']
titanic_train_x_bin1 = ['Sex','Pclass', 'FamilySize', 'Age', 'Embarked_Code']
titanic_train_xy_bin1 = Target1 + titanic_train_x_bin1
print('Bin X Y: ', titanic_train_xy_bin1, '\n')
#%%
#Visualization
import matplotlib as mpl
import matplotlib.pyplot as plt
import matplotlib.pylab as pylab
import seaborn as sns
train2_x_bin,test2_x_bin,train2_y_bin,test2_y_bin =model_selection.train_test_split(titanic_train[titanic_train_x_bin1],titanic_train[Target1],test_size=0.3,random_state = 0)
print("Data1 Shape: {}".format(titanic_train.shape))
print("Train1 Shape: {}".format(train2_x_bin.shape))
print("Test1 Shape: {}".format(test2_x_bin.shape))
train2_x_bin.head()
#%%
from sklearn import linear_model
logreg = linear_model.LogisticRegression(C=1e9)
logreg.fit(train2_x_bin, train2_y_bin)
Y_pred = logreg.predict(test2_x_bin)
#%%
print(logreg.intercept_) #截距
coef=pd.DataFrame(logreg.coef_).T  #参数
columns=pd.DataFrame(train2_x_bin.columns,columns=['A'])
result = pd.concat([columns,coef], axis=1)
result = result.rename(columns={'A': 'Attribute', 0: 'Coefficients'})
result
#%%
import statsmodels.api as sm
log_reg = sm.Logit(train2_y_bin,sm.add_constant(train2_x_bin)).fit()
log_reg.summary()
#%% md
# ### 模型
# $$\log \left({p\over 1-p}\right) = 2.7863X_1-1.0939X_2-0.2497X_3-0.04X_4-0.268X_5+3.0067$$
# 
# $$ X_1为乘客性别$$
# $$X_2为乘客所属船层$$
# $$X_3为家庭成员数量$$
# $$X_4为乘客年龄$$
# $$X_5为登船口$$
# 
# 
# 
#%% md
# ### 模型解释
#  - $e^{2.7863}=16.2241$表示在其余条件相同的情况下，女性（1）存活的概率是男性（0）的16.22倍
#  - $\beta_2=-1.0939$表示在其余条件相同的情况下，船层每增加一层，生存率越低
#  - $e^{-0.2497}=0.7704$表示在其余条件相同的情况下，家庭成员数量每增加1，存活的概率提高77.9%
#  - $e^{-0.04}=0.9610$表示在其余条件相同的情况下,乘客年龄每增加1，存活的概率提高96.07%
#  - $\beta_5=-0.2497$表示在其余条件相同的情况下，登船口越高，生存率越低
# 
#%% md
# ### 模型评估
#%%
acc_train = round(logreg.score(train2_x_bin, train2_y_bin) * 100, 2)
acc_train
#%% md
# **comments：**
# - 模型的准确率为79.61%
#%% md
# ### 对测试集进行模型性能评估
#%%
# View summary of common classification metrics
print("------------------------Metrices---------------------")
print(metrics.classification_report(y_true = test2_y_bin,y_pred = Y_pred))

#%% md
# **comments：**
# - 模型的准确率为80%
# - 生存样本的召回率为75%
#%%
from sklearn.metrics import roc_curve, auc
import matplotlib.pyplot as plt
probs = logreg.predict_proba(test2_x_bin)
preds = probs[:,1]
#---find the FPR, TPR, and threshold---
fpr, tpr, threshold = roc_curve(test2_y_bin, preds)
#---find the area under the curve---
roc_auc = auc(fpr, tpr)
plt.plot(fpr, tpr, 'b', label = 'AUC = %0.2f' % roc_auc)
plt.plot([0, 1], [0, 1],'r--')
plt.xlim([0, 1])
plt.ylim([0, 1])
plt.ylabel('True Positive Rate (TPR)')
plt.xlabel('False Positive Rate (FPR)')
plt.title('Receiver Operating Characteristic (ROC)')
plt.legend(loc = 'lower right')
plt.show()

#%% md
# **comments：**
# - AUC=0.85，这个模型有一定的预测价值。是个较为优质的模型。
#%%
