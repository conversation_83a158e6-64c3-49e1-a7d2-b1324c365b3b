# 超声波流量计模拟与计算系统

## 项目概述

本项目实现了一个基于超声波时差法的流量计模拟系统。系统能够：
1. 模拟超声波在流体中的传播
2. 计算流体流速和流量
3. 在未知流体温度的情况下，估算温度并修正流速计算

## 理论背景

超声波流量表的基本原理：超声波脉冲穿过管道，当声速和流速方向一致时，超声波脉冲的速度和流速会叠加；当方向相反时，超声波脉冲的速度和流速会相减。

- D 是管道的直径
- L 是超声波在管道中的传播距离
- θ 是L和管道直径的夹角
- v 是流体的流速

## 数学模型

### 问题1：已知声速的流速计算

当超声波的传播速度 c 已知时，流速 v 可以通过以下公式计算：

```
v ≈ (c² * Δt) / (2 * L * sin(θ))
```

其中：
- c 是声速（米/秒）
- Δt 是顺流和逆流的时间差（秒）
- L 是传播距离（米）
- θ 是传播路径与管道直径的夹角（弧度）

流量计算公式：
```
Q = v * A
```

其中：
- Q 是体积流量（立方米/秒）
- v 是流速（米/秒）
- A 是管道横截面积（平方米）

### 问题2：未知声速的修正模型

当流体温度未知时，声速 c 也未知。此时，我们可以：

1. 通过测量的顺流时间 t_ab 和逆流时间 t_ba 估算声速：
```
c = (L/2) * (t_ab + t_ba) / (t_ab * t_ba)
```

2. 直接从 t_ab 和 t_ba 计算流速：
```
v = (L / (2 * sin(θ))) * (t_ba - t_ab) / (t_ab * t_ba)
```

3. 根据估算的声速，反推流体温度（以水为例）：
```
T = f⁻¹(c)  # 声速与温度的反函数
```

## 代码结构

项目包含两个主要文件：

### 1. flowmeter_model.py

包含 `UltrasonicFlowmeter` 类，实现了流量计的核心功能：

- `__init__`: 初始化流量计几何参数
- `calculate_velocity_problem1`: 问题1的流速计算
- `calculate_flow_volume`: 流量计算
- `estimate_sound_speed`: 声速估算
- `calculate_velocity_problem2`: 问题2的修正流速计算
- `get_sound_speed_in_water`: 根据温度计算水中声速
- `estimate_temperature`: 根据声速估算温度

### 2. main.py

演示和验证流量计模型的主程序：

- `simulate_transit_times`: 模拟测量时间
- `main`: 主函数，演示模型的使用和对比结果

## 使用方法

1. 确保安装了Python 3.x和必要的库（math, random）
2. 运行主程序：
```bash
python main.py
```

## 输出示例

```
--- 场景模拟 ---
真实水温: 25.0°C
真实流速: 1.2000 m/s
模拟测量得到的顺流时间 t_ab: 0.000080123 s
模拟测量得到的逆流时间 t_ba: 0.000080232 s
--------------------

--- 问题 1: 理想模型计算 (假设温度为20°C) ---
假设声速 (基于 20.0°C): 1482.3227 m/s
计算得到的流速: 1.1431 m/s
与真实流速 (1.2000 m/s) 的误差: 0.0569 m/s
--------------------

--- 问题 2: 修正模型计算 (温度未知) ---
步骤 a: 估算出的实际声速: 1496.6797 m/s
步骤 b: 计算得到的修正流速: 1.1653 m/s
       与真实流速 (1.2000 m/s) 的误差: 0.0347 m/s
步骤 c: 估算出的水温: 25.0000°C
       与真实水温 (25.0°C) 的误差: 0.0000°C
--------------------

--- 流量计算 ---
在 60 秒内，总流量为: 0.5491 立方米
--------------------
```

## 结果分析

1. **问题1模型**：当假设温度为20°C（而实际为25°C）时，计算出的流速有一定误差。这是因为温度假设不准确导致声速假设不准确。

2. **问题2模型**：通过直接从测量时间估算声速，修正模型显著提高了流速计算的准确性。同时，它还能准确估算出流体温度。

## 注意事项

1. 模拟中加入了随机噪声以模拟真实测量环境。噪声水平会影响结果的准确性。

2. 温度估算使用搜索算法，其精度取决于搜索步长（当前为0.01°C）。

3. 声速与温度的关系使用多项式模型，适用于0-100°C范围内的水。对其他流体需要修改相应公式。

## 可视化图表

项目包含一个可视化脚本 `visualize.py`，可以生成以下图表：

1. **温度与声速的关系图**：展示水中声速如何随温度变化
2. **温度误差影响图**：展示温度假设误差对流速计算的影响
3. **噪声影响比较图**：比较不同噪声水平下两种模型的误差
4. **流量计几何示意图**：直观展示超声波流量计的结构和参数
