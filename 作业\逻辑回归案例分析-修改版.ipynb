{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 逻辑回归案例分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 泰坦尼克号生存分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 变量解释\n", " \n", " \n", "\n", "| 变量名    | 变量描述 | \n", "|-----------|-------------|\n", "| PassengerID    | 乘客id  |  \n", "| Survived  |标记乘客是否幸存，幸存（1），死亡（0）|\n", "| Pclass   |标记乘客所属船层，第一层（1），第二层（2），第三层（3）         | \n", "| Name | 乘客名字           |\n", "| Sex     | 乘客性别，男male、女female  |\n", "| Age    |  乘客年龄 |\n", "| Sibsp  | 船上兄弟姐妹和配偶的数量 |\n", "| Parch    |船上父母和孩子的数量 |\n", "| Ticket   | 乘客的船票号码|\n", "| Fare  | 乘客为船票付了多少钱|\n", "| Cabin    |乘客住在哪个船舱|\n", "|Embarked  |乘客从哪个地方登上泰坦尼克号 |\n", "\n", " "]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["titanic_train=pd.read_csv(\"F:/Python/files/titanic_train.csv\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 查看数据"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PassengerId</th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Name</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Ticket</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>A/5 21171</td>\n", "      <td>7.2500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>female</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>PC 17599</td>\n", "      <td>71.2833</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>female</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>STON/O2. 3101282</td>\n", "      <td>7.9250</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>female</td>\n", "      <td>35.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>113803</td>\n", "      <td>53.1000</td>\n", "      <td>C123</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>male</td>\n", "      <td>35.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>373450</td>\n", "      <td>8.0500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   PassengerId  Survived  Pclass  \\\n", "0            1         0       3   \n", "1            2         1       1   \n", "2            3         1       3   \n", "3            4         1       1   \n", "4            5         0       3   \n", "\n", "                                                Name     Sex   Age  SibSp  \\\n", "0                            <PERSON><PERSON>, Mr. <PERSON>    male  22.0      1   \n", "1  C<PERSON>ngs, Mrs. <PERSON> (Florence Briggs Th...  female  38.0      1   \n", "2                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>  female  26.0      0   \n", "3       <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)  female  35.0      1   \n", "4                           <PERSON>, Mr. <PERSON>    male  35.0      0   \n", "\n", "   Parch            Ticket     Fare Cabin Embarked  \n", "0      0         A/5 21171   7.2500   NaN        S  \n", "1      0          PC 17599  71.2833   C85        C  \n", "2      0  STON/O2. 3101282   7.9250   NaN        S  \n", "3      0            113803  53.1000  C123        S  \n", "4      0            373450   8.0500   NaN        S  "]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_train.head()#前5个样本"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 描述性统计\n", "- 分类型变量：Survived、Sex、Embarked、Pclass\n", "- 数值型变量： Age、Fare、SibSp、Parch"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 891 entries, 0 to 890\n", "Data columns (total 12 columns):\n", " #   Column       Non-Null Count  Dtype  \n", "---  ------       --------------  -----  \n", " 0   PassengerId  891 non-null    int64  \n", " 1   Survived     891 non-null    int64  \n", " 2   Pclass       891 non-null    int64  \n", " 3   Name         891 non-null    object \n", " 4   Sex          891 non-null    object \n", " 5   Age          714 non-null    float64\n", " 6   SibSp        891 non-null    int64  \n", " 7   Parch        891 non-null    int64  \n", " 8   Ticket       891 non-null    object \n", " 9   Fare         891 non-null    float64\n", " 10  Cabin        204 non-null    object \n", " 11  Embarked     889 non-null    object \n", "dtypes: float64(2), int64(5), object(5)\n", "memory usage: 83.7+ KB\n"]}], "source": ["titanic_train.info()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments：**\n", "- Age、<PERSON><PERSON><PERSON>、embarked存在缺失值，需要进行处理。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 对数值型变量进行分析"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"scrolled": true}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>mean</th>\n", "      <th>std</th>\n", "      <th>min</th>\n", "      <th>25%</th>\n", "      <th>50%</th>\n", "      <th>75%</th>\n", "      <th>max</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>PassengerId</th>\n", "      <td>891.0</td>\n", "      <td>446.000000</td>\n", "      <td>257.353842</td>\n", "      <td>1.00</td>\n", "      <td>223.5000</td>\n", "      <td>446.0000</td>\n", "      <td>668.5</td>\n", "      <td>891.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Survived</th>\n", "      <td>891.0</td>\n", "      <td>0.383838</td>\n", "      <td>0.486592</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1.0</td>\n", "      <td>1.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Pclass</th>\n", "      <td>891.0</td>\n", "      <td>2.308642</td>\n", "      <td>0.836071</td>\n", "      <td>1.00</td>\n", "      <td>2.0000</td>\n", "      <td>3.0000</td>\n", "      <td>3.0</td>\n", "      <td>3.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Age</th>\n", "      <td>714.0</td>\n", "      <td>29.699118</td>\n", "      <td>14.526497</td>\n", "      <td>0.42</td>\n", "      <td>20.1250</td>\n", "      <td>28.0000</td>\n", "      <td>38.0</td>\n", "      <td>80.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>SibSp</th>\n", "      <td>891.0</td>\n", "      <td>0.523008</td>\n", "      <td>1.102743</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>1.0</td>\n", "      <td>8.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Parch</th>\n", "      <td>891.0</td>\n", "      <td>0.381594</td>\n", "      <td>0.806057</td>\n", "      <td>0.00</td>\n", "      <td>0.0000</td>\n", "      <td>0.0000</td>\n", "      <td>0.0</td>\n", "      <td>6.0000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Fare</th>\n", "      <td>891.0</td>\n", "      <td>32.204208</td>\n", "      <td>49.693429</td>\n", "      <td>0.00</td>\n", "      <td>7.9104</td>\n", "      <td>14.4542</td>\n", "      <td>31.0</td>\n", "      <td>512.3292</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["             count        mean         std   min       25%       50%    75%  \\\n", "PassengerId  891.0  446.000000  257.353842  1.00  223.5000  446.0000  668.5   \n", "Survived     891.0    0.383838    0.486592  0.00    0.0000    0.0000    1.0   \n", "Pclass       891.0    2.308642    0.836071  1.00    2.0000    3.0000    3.0   \n", "Age          714.0   29.699118   14.526497  0.42   20.1250   28.0000   38.0   \n", "SibSp        891.0    0.523008    1.102743  0.00    0.0000    0.0000    1.0   \n", "Parch        891.0    0.381594    0.806057  0.00    0.0000    0.0000    0.0   \n", "Fare         891.0   32.204208   49.693429  0.00    7.9104   14.4542   31.0   \n", "\n", "                  max  \n", "PassengerId  891.0000  \n", "Survived       1.0000  \n", "Pclass         3.0000  \n", "Age           80.0000  \n", "SibSp          8.0000  \n", "Parch          6.0000  \n", "Fare         512.3292  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_train.describe().T"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments：**\n", "- 训练集有891个数据\n", "- 变量Survived是分类变量（0或1），样本存活率约为38%。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 对文字型变量进行分析"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>unique</th>\n", "      <th>top</th>\n", "      <th>freq</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Name</th>\n", "      <td>891</td>\n", "      <td>891</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Sex</th>\n", "      <td>891</td>\n", "      <td>2</td>\n", "      <td>male</td>\n", "      <td>577</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Ticket</th>\n", "      <td>891</td>\n", "      <td>681</td>\n", "      <td>347082</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <td>204</td>\n", "      <td>147</td>\n", "      <td>B96 B98</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Embarked</th>\n", "      <td>889</td>\n", "      <td>3</td>\n", "      <td>S</td>\n", "      <td>644</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         count unique                      top freq\n", "Name       891    891  <PERSON><PERSON>, Mr. <PERSON>    1\n", "Sex        891      2                     male  577\n", "Ticket     891    681                   347082    7\n", "Cabin      204    147                  B96 B98    4\n", "Embarked   889      3                        S  644"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_train.describe(include=['O']).T"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments：**\n", "- 65%为男性\n", "- 有三个登船口。大部分通过s口登船"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 交叉分析\n", "用分类类型的自变量和因变量Survived分别做交叉分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Survived vs. P<PERSON>lass"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Pclass</th>\n", "      <th>Survived</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0.629630</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>0.472826</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>0.242363</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Pclass  Survived\n", "0       1  0.629630\n", "1       2  0.472826\n", "2       3  0.242363"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_train[['Pclass', 'Survived']].groupby(['Pclass'],as_index=False).mean().sort_values(by='Survived',ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments:**\n", "- 第一层的乘客生还率为62.96%，第三层仅有24.24%"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Survived vs. Sex"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sex</th>\n", "      <th>Survived</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>female</td>\n", "      <td>0.742038</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>male</td>\n", "      <td>0.188908</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["      Sex  Survived\n", "0  female  0.742038\n", "1    male  0.188908"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_train[[\"Sex\", \"Survived\"]].groupby(['Sex'], as_index=False).mean().sort_values(by='Survived', ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments:**\n", "- 女性的生存率为74.20%高于男性"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Survived vs. Embarked"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Embarked</th>\n", "      <th>Survived</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>C</td>\n", "      <td>0.553571</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Q</td>\n", "      <td>0.389610</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>S</td>\n", "      <td>0.336957</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Embarked  Survived\n", "0        C  0.553571\n", "1        Q  0.389610\n", "2        S  0.336957"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_train[[\"Embarked\", \"Survived\"]].groupby(['Embarked'], as_index=False).mean().sort_values(by='Survived', ascending=False)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments:**\n", "- 从C口登船的生存率为55.35%，而登船人数最多的S口的生存率仅有33.70%"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 数据可视化分析"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Survived vs. Age"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x1c353a10e50>"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 600x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import seaborn as sns\n", "import matplotlib.pyplot as plt\n", "%matplotlib inline\n", "age_map = sns.FacetGrid(titanic_train, col='Survived')\n", "age_map.map(plt.hist, 'Age', bins=20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments:**\n", "- 四岁以下的婴儿存活率较高。\n", "- 大量15-25岁的人没有存活下来\n", "- 大多数乘客的年龄在15~35岁之间。"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Survived vs. <PERSON><PERSON>"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x1c353a04190>"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 600x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fare_map = sns.FacetGrid(titanic_train, col='Survived')\n", "fare_map.map(plt.hist, 'Fare', bins=20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments：**\n", "- 票价低的乘客生存率更低\n", "- 票价高的乘客的生存率相对高一些"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Survived vs. SipSp"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x1c3539f2a00>"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 600x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["sipsp_map = sns.FacetGrid(titanic_train, col='Survived')\n", "sipsp_map.map(plt.hist, 'SibSp', bins=20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments:**\n", "- 两变量间无明显关系"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Survived vs. <PERSON>rch"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["<seaborn.axisgrid.FacetGrid at 0x1c36b6fc5e0>"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 600x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["parch_map = sns.FacetGrid(titanic_train, col='Survived')\n", "parch_map.map(plt.hist, 'Parch', bins=20)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments:**\n", "- 两变量间无明显关系"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 处理数据\n", "\n", "- 将分类型变量变为数值型变量\n", "- 将存在缺失值的变量进行缺失值处理\n", "  - Age、Embarked、Fare 进行缺失值的替换\n", "  - 删除无关变量"]}, {"cell_type": "markdown", "metadata": {}, "source": ["对**Sex**进行处理，将分类型变量转换为数值型变量"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>PassengerId</th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Name</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Ticket</th>\n", "      <th>Fare</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>Embarked</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON>, Mr. <PERSON></td>\n", "      <td>0</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>A/5 21171</td>\n", "      <td>7.2500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>s, Mrs. <PERSON> (Florence Briggs Th...</td>\n", "      <td>1</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>PC 17599</td>\n", "      <td>71.2833</td>\n", "      <td>C85</td>\n", "      <td>C</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON></td>\n", "      <td>1</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>STON/O2. 3101282</td>\n", "      <td>7.9250</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)</td>\n", "      <td>1</td>\n", "      <td>35.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>113803</td>\n", "      <td>53.1000</td>\n", "      <td>C123</td>\n", "      <td>S</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>5</td>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td><PERSON>, Mr. <PERSON></td>\n", "      <td>0</td>\n", "      <td>35.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>373450</td>\n", "      <td>8.0500</td>\n", "      <td>NaN</td>\n", "      <td>S</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   PassengerId  Survived  Pclass  \\\n", "0            1         0       3   \n", "1            2         1       1   \n", "2            3         1       3   \n", "3            4         1       1   \n", "4            5         0       3   \n", "\n", "                                                Name  Sex   Age  SibSp  Parch  \\\n", "0                            <PERSON><PERSON>, <PERSON><PERSON> <PERSON>    0  22.0      1      0   \n", "1  C<PERSON>ngs, Mrs. <PERSON> (Florence Briggs Th...    1  38.0      1      0   \n", "2                             <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>    1  26.0      0      0   \n", "3       <PERSON><PERSON><PERSON>, Mrs. <PERSON> (<PERSON>)    1  35.0      1      0   \n", "4                           <PERSON>, Mr. <PERSON>    0  35.0      0      0   \n", "\n", "             Ticket     Fare Cabin Embarked  \n", "0         A/5 21171   7.2500   NaN        S  \n", "1          PC 17599  71.2833   C85        C  \n", "2  STON/O2. 3101282   7.9250   NaN        S  \n", "3            113803  53.1000  C123        S  \n", "4            373450   8.0500   NaN        S  "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_train['Sex'] = titanic_train['Sex'].map( {'female': 1, 'male': 0} ).astype(int)\n", "titanic_train.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- Age和Fare的缺失值用中位数填充`median()`\n", "- Embarked的缺失值用众数进行填充`mode()[0]`\n", "- 删除PassengerID、<PERSON><PERSON><PERSON>、Name和Ticket四个无关变量\n", "\n", "\n", "- 均值填充`mean()`\n", "- 删除空值`dropna()`"]}, {"cell_type": "code", "execution_count": 15, "metadata": {"scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 891 entries, 0 to 890\n", "Data columns (total 8 columns):\n", " #   Column    Non-Null Count  Dtype  \n", "---  ------    --------------  -----  \n", " 0   Survived  891 non-null    int64  \n", " 1   Pclass    891 non-null    int64  \n", " 2   Sex       891 non-null    int32  \n", " 3   Age       891 non-null    float64\n", " 4   SibSp     891 non-null    int64  \n", " 5   Parch     891 non-null    int64  \n", " 6   Fare      891 non-null    float64\n", " 7   Embarked  891 non-null    object \n", "dtypes: float64(2), int32(1), int64(4), object(1)\n", "memory usage: 52.3+ KB\n"]}], "source": ["titanic_train['Age'].fillna(titanic_train['Age'].median(), inplace = True)\n", "titanic_train['Embarked'].fillna(titanic_train['Embarked'].mode()[0], inplace = True)\n", "titanic_train['Fare'].fillna(titanic_train['Fare'].median(), inplace =True)\n", "titanic_train.drop(['PassengerId','Cabin', 'Ticket','Name'], axis=1, inplace = True)\n", "\n", "titanic_train.info()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 将SipSp和Parch变量合并为家庭数量"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["titanic_train['FamilySize'] =titanic_train['SibSp'] + titanic_train['Parch'] + 1"]}, {"cell_type": "markdown", "metadata": {}, "source": ["-  对登船口进行数值型转换"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["#编码\n", "from sklearn.preprocessing import LabelEncoder\n", "label = LabelEncoder()\n", "titanic_train['Embarked_Code'] = label.fit_transform(titanic_train['Embarked'])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Survived</th>\n", "      <th>Pclass</th>\n", "      <th>Sex</th>\n", "      <th>Age</th>\n", "      <th>SibSp</th>\n", "      <th>Parch</th>\n", "      <th>Fare</th>\n", "      <th>Embarked</th>\n", "      <th>FamilySize</th>\n", "      <th>Embarked_Code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>22.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>7.2500</td>\n", "      <td>S</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>38.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>71.2833</td>\n", "      <td>C</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>26.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>7.9250</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>35.0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>53.1000</td>\n", "      <td>S</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>35.0</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>8.0500</td>\n", "      <td>S</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Survived  Pclass  Sex   Age  SibSp  Parch     Fare Embarked  FamilySize  \\\n", "0         0       3    0  22.0      1      0   7.2500        S           2   \n", "1         1       1    1  38.0      1      0  71.2833        C           2   \n", "2         1       3    1  26.0      0      0   7.9250        S           1   \n", "3         1       1    1  35.0      1      0  53.1000        S           2   \n", "4         0       3    0  35.0      0      0   8.0500        S           1   \n", "\n", "   Embarked_Code  \n", "0              2  \n", "1              0  \n", "2              2  \n", "3              2  \n", "4              2  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["titanic_train.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 模型开发"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 选择数据"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bin X Y:  ['Survived', 'Sex', 'Pclass', 'Embarked_Code', 'FamilySize', 'Age', 'Fare'] \n", "\n"]}], "source": ["Target = ['Survived']\n", "titanic_train_x_bin = ['Sex','Pclass', 'Embarked_Code', 'FamilySize', 'Age', 'Fare']\n", "titanic_train_xy_bin = Target + titanic_train_x_bin\n", "print('Bin X Y: ', titanic_train_xy_bin, '\\n')"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [], "source": ["#Model Algorithms\n", "from sklearn import linear_model\n", "#Common Model Helpers\n", "from sklearn import feature_selection\n", "from sklearn import model_selection\n", "from sklearn import metrics"]}, {"cell_type": "markdown", "metadata": {}, "source": ["- 对训练集进行分割。70%的训练集，30%的测试集"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data1 Shape: (891, 10)\n", "Train1 X Shape: (623, 6)\n", "Test1 X Shape: (268, 6)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sex</th>\n", "      <th>Pclass</th>\n", "      <th>Embarked_Code</th>\n", "      <th>FamilySize</th>\n", "      <th>Age</th>\n", "      <th>Fare</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>857</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>51.0</td>\n", "      <td>26.5500</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>49.0</td>\n", "      <td>76.7292</td>\n", "    </tr>\n", "    <tr>\n", "      <th>386</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "      <td>1.0</td>\n", "      <td>46.9000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>54.0</td>\n", "      <td>77.2875</td>\n", "    </tr>\n", "    <tr>\n", "      <th>578</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "      <td>28.0</td>\n", "      <td>14.4583</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Sex  Pclass  Embarked_Code  FamilySize   Age     Fare\n", "857    0       1              2           1  51.0  26.5500\n", "52     1       1              0           2  49.0  76.7292\n", "386    0       3              2           8   1.0  46.9000\n", "124    0       1              2           2  54.0  77.2875\n", "578    1       3              0           2  28.0  14.4583"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["#Visualization\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import matplotlib.pylab as pylab\n", "import seaborn as sns\n", "train1_x_bin,test1_x_bin,train1_y_bin,test1_y_bin =model_selection.train_test_split(titanic_train[titanic_train_x_bin],titanic_train[Target],test_size=0.3,random_state = 0)\n", "print(\"Data1 Shape: {}\".format(titanic_train.shape))\n", "print(\"Train1 X Shape: {}\".format(train1_x_bin.shape))\n", "print(\"Test1 X Shape: {}\".format(test1_x_bin.shape))\n", "train1_x_bin.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### 建模"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LogisticRegression\n", "logreg=LogisticRegression(C=1e9)"]}, {"cell_type": "code", "execution_count": 23, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\sklearn\\utils\\validation.py:1111: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().\n", "  y = column_or_1d(y, warn=True)\n"]}], "source": ["from sklearn import linear_model\n", "logreg = linear_model.LogisticRegression(C=1e9)\n", "logreg.fit(train1_x_bin, train1_y_bin)\n", "Y_pred = logreg.predict(test1_x_bin)"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[2.85485069]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Attribute</th>\n", "      <th>Coefficients</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Sex</td>\n", "      <td>2.786576</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Pclass</td>\n", "      <td>-1.050459</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Embarked_Code</td>\n", "      <td>-0.250959</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>FamilySize</td>\n", "      <td>-0.260951</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Age</td>\n", "      <td>-0.039827</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td><PERSON>e</td>\n", "      <td>0.001384</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Attribute  Coefficients\n", "0            Sex      2.786576\n", "1         Pclass     -1.050459\n", "2  Embarked_Code     -0.250959\n", "3     FamilySize     -0.260951\n", "4            Age     -0.039827\n", "5           Fare      0.001384"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["print(logreg.intercept_) #截距\n", "coef=pd.DataFrame(logreg.coef_).T  #参数\n", "columns=pd.DataFrame(train1_x_bin.columns,columns=['A'])\n", "result = pd.concat([columns,coef], axis=1)\n", "result = result.rename(columns={'A': 'Attribute', 0: 'Coefficients'})\n", "result"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 0.443830\n", "         Iterations 6\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Logit Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>       <td>Survived</td>     <th>  No. Observations:  </th>  <td>   623</td>  \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                 <td>Logit</td>      <th>  Df Residuals:      </th>  <td>   616</td>  \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>                 <td>MLE</td>       <th>  Df Model:          </th>  <td>     6</td>  \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>            <td><PERSON><PERSON>, 01 Nov 2022</td> <th>  Pseudo R-squ.:     </th>  <td>0.3356</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                <td>16:17:13</td>     <th>  Log-Likelihood:    </th> <td> -276.51</td> \n", "</tr>\n", "<tr>\n", "  <th>converged:</th>             <td>True</td>       <th>  LL-Null:           </th> <td> -416.19</td> \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>     <td>nonrobust</td>    <th>  LLR p-value:       </th> <td>2.139e-57</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "        <td></td>           <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>         <td>    2.8549</td> <td>    0.624</td> <td>    4.578</td> <td> 0.000</td> <td>    1.633</td> <td>    4.077</td>\n", "</tr>\n", "<tr>\n", "  <th>Sex</th>           <td>    2.7865</td> <td>    0.239</td> <td>   11.637</td> <td> 0.000</td> <td>    2.317</td> <td>    3.256</td>\n", "</tr>\n", "<tr>\n", "  <th>Pclass</th>        <td>   -1.0505</td> <td>    0.162</td> <td>   -6.487</td> <td> 0.000</td> <td>   -1.368</td> <td>   -0.733</td>\n", "</tr>\n", "<tr>\n", "  <th>Embarked_Code</th> <td>   -0.2510</td> <td>    0.141</td> <td>   -1.775</td> <td> 0.076</td> <td>   -0.528</td> <td>    0.026</td>\n", "</tr>\n", "<tr>\n", "  <th>FamilySize</th>    <td>   -0.2609</td> <td>    0.082</td> <td>   -3.194</td> <td> 0.001</td> <td>   -0.421</td> <td>   -0.101</td>\n", "</tr>\n", "<tr>\n", "  <th>Age</th>           <td>   -0.0398</td> <td>    0.009</td> <td>   -4.292</td> <td> 0.000</td> <td>   -0.058</td> <td>   -0.022</td>\n", "</tr>\n", "<tr>\n", "  <th>Fare</th>          <td>    0.0014</td> <td>    0.003</td> <td>    0.545</td> <td> 0.586</td> <td>   -0.004</td> <td>    0.006</td>\n", "</tr>\n", "</table>"], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                           Logit Regression Results                           \n", "==============================================================================\n", "Dep. Variable:               Survived   No. Observations:                  623\n", "Model:                          Logit   Df Residuals:                      616\n", "Method:                           MLE   Df Model:                            6\n", "Date:                Tu<PERSON>, 01 Nov 2022   Pseudo R-squ.:                  0.3356\n", "Time:                        16:17:13   Log-Likelihood:                -276.51\n", "converged:                       True   LL-Null:                       -416.19\n", "Covariance Type:            nonrobust   LLR p-value:                 2.139e-57\n", "=================================================================================\n", "                    coef    std err          z      P>|z|      [0.025      0.975]\n", "---------------------------------------------------------------------------------\n", "const             2.8549      0.624      4.578      0.000       1.633       4.077\n", "Sex               2.7865      0.239     11.637      0.000       2.317       3.256\n", "Pclass           -1.0505      0.162     -6.487      0.000      -1.368      -0.733\n", "Embarked_Code    -0.2510      0.141     -1.775      0.076      -0.528       0.026\n", "FamilySize       -0.2609      0.082     -3.194      0.001      -0.421      -0.101\n", "Age              -0.0398      0.009     -4.292      0.000      -0.058      -0.022\n", "Fare              0.0014      0.003      0.545      0.586      -0.004       0.006\n", "=================================================================================\n", "\"\"\""]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["import statsmodels.api as sm\n", "log_reg = sm.Logit(train1_y_bin,sm.add_constant(train1_x_bin)).fit()\n", "log_reg.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments：**\n", "- Fare的P值大于0.05，接受原假设。即表示船票价格与生存率无关，无统计学意义。需要删去该变量。\n", "- Embarked的P值略大于0.05，在后续建模中需要进行进一步的讨论。"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Bin X Y:  ['Survived', 'Sex', 'Pclass', 'FamilySize', 'Age', 'Embarked_Code'] \n", "\n"]}], "source": ["Target1 = ['Survived']\n", "titanic_train_x_bin1 = ['Sex','Pclass', 'FamilySize', 'Age', 'Embarked_Code']\n", "titanic_train_xy_bin1 = Target1 + titanic_train_x_bin1\n", "print('Bin X Y: ', titanic_train_xy_bin1, '\\n')"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Data1 Shape: (891, 10)\n", "Train1 Shape: (623, 5)\n", "Test1 Shape: (268, 5)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Sex</th>\n", "      <th>Pclass</th>\n", "      <th>FamilySize</th>\n", "      <th>Age</th>\n", "      <th>Embarked_Code</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>857</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>51.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>49.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>386</th>\n", "      <td>0</td>\n", "      <td>3</td>\n", "      <td>8</td>\n", "      <td>1.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>54.0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>578</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>2</td>\n", "      <td>28.0</td>\n", "      <td>0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Sex  Pclass  FamilySize   Age  Embarked_Code\n", "857    0       1           1  51.0              2\n", "52     1       1           2  49.0              0\n", "386    0       3           8   1.0              2\n", "124    0       1           2  54.0              2\n", "578    1       3           2  28.0              0"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["#Visualization\n", "import matplotlib as mpl\n", "import matplotlib.pyplot as plt\n", "import matplotlib.pylab as pylab\n", "import seaborn as sns\n", "train2_x_bin,test2_x_bin,train2_y_bin,test2_y_bin =model_selection.train_test_split(titanic_train[titanic_train_x_bin1],titanic_train[Target1],test_size=0.3,random_state = 0)\n", "print(\"Data1 Shape: {}\".format(titanic_train.shape))\n", "print(\"Train1 Shape: {}\".format(train2_x_bin.shape))\n", "print(\"Test1 Shape: {}\".format(test2_x_bin.shape))\n", "train2_x_bin.head()"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\anaconda3\\lib\\site-packages\\sklearn\\utils\\validation.py:1111: DataConversionWarning: A column-vector y was passed when a 1d array was expected. Please change the shape of y to (n_samples, ), for example using ravel().\n", "  y = column_or_1d(y, warn=True)\n"]}], "source": ["from sklearn import linear_model\n", "logreg = linear_model.LogisticRegression(C=1e9)\n", "logreg.fit(train2_x_bin, train2_y_bin)\n", "Y_pred = logreg.predict(test2_x_bin)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[3.00670003]\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Attribute</th>\n", "      <th>Coefficients</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Sex</td>\n", "      <td>2.786259</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Pclass</td>\n", "      <td>-1.093910</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>FamilySize</td>\n", "      <td>-0.249688</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Age</td>\n", "      <td>-0.040007</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Embarked_Code</td>\n", "      <td>-0.268038</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       Attribute  Coefficients\n", "0            Sex      2.786259\n", "1         Pclass     -1.093910\n", "2     FamilySize     -0.249688\n", "3            Age     -0.040007\n", "4  Embarked_Code     -0.268038"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["print(logreg.intercept_) #截距\n", "coef=pd.DataFrame(logreg.coef_).T  #参数\n", "columns=pd.DataFrame(train2_x_bin.columns,columns=['A'])\n", "result = pd.concat([columns,coef], axis=1)\n", "result = result.rename(columns={'A': 'Attribute', 0: 'Coefficients'})\n", "result"]}, {"cell_type": "code", "execution_count": 30, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Optimization terminated successfully.\n", "         Current function value: 0.444077\n", "         Iterations 6\n"]}, {"data": {"text/html": ["<table class=\"simpletable\">\n", "<caption>Logit Regression Results</caption>\n", "<tr>\n", "  <th>Dep. Variable:</th>       <td>Survived</td>     <th>  No. Observations:  </th>  <td>   623</td>  \n", "</tr>\n", "<tr>\n", "  <th>Model:</th>                 <td>Logit</td>      <th>  Df Residuals:      </th>  <td>   617</td>  \n", "</tr>\n", "<tr>\n", "  <th>Method:</th>                 <td>MLE</td>       <th>  Df Model:          </th>  <td>     5</td>  \n", "</tr>\n", "<tr>\n", "  <th>Date:</th>            <td><PERSON><PERSON>, 01 Nov 2022</td> <th>  Pseudo R-squ.:     </th>  <td>0.3353</td>  \n", "</tr>\n", "<tr>\n", "  <th>Time:</th>                <td>16:17:13</td>     <th>  Log-Likelihood:    </th> <td> -276.66</td> \n", "</tr>\n", "<tr>\n", "  <th>converged:</th>             <td>True</td>       <th>  LL-Null:           </th> <td> -416.19</td> \n", "</tr>\n", "<tr>\n", "  <th>Covariance Type:</th>     <td>nonrobust</td>    <th>  LLR p-value:       </th> <td>3.159e-58</td>\n", "</tr>\n", "</table>\n", "<table class=\"simpletable\">\n", "<tr>\n", "        <td></td>           <th>coef</th>     <th>std err</th>      <th>z</th>      <th>P>|z|</th>  <th>[0.025</th>    <th>0.975]</th>  \n", "</tr>\n", "<tr>\n", "  <th>const</th>         <td>    3.0067</td> <td>    0.561</td> <td>    5.364</td> <td> 0.000</td> <td>    1.908</td> <td>    4.105</td>\n", "</tr>\n", "<tr>\n", "  <th>Sex</th>           <td>    2.7863</td> <td>    0.239</td> <td>   11.638</td> <td> 0.000</td> <td>    2.317</td> <td>    3.255</td>\n", "</tr>\n", "<tr>\n", "  <th>Pclass</th>        <td>   -1.0939</td> <td>    0.142</td> <td>   -7.728</td> <td> 0.000</td> <td>   -1.371</td> <td>   -0.816</td>\n", "</tr>\n", "<tr>\n", "  <th>FamilySize</th>    <td>   -0.2497</td> <td>    0.079</td> <td>   -3.165</td> <td> 0.002</td> <td>   -0.404</td> <td>   -0.095</td>\n", "</tr>\n", "<tr>\n", "  <th>Age</th>           <td>   -0.0400</td> <td>    0.009</td> <td>   -4.317</td> <td> 0.000</td> <td>   -0.058</td> <td>   -0.022</td>\n", "</tr>\n", "<tr>\n", "  <th>Embarked_Code</th> <td>   -0.2680</td> <td>    0.138</td> <td>   -1.944</td> <td> 0.052</td> <td>   -0.538</td> <td>    0.002</td>\n", "</tr>\n", "</table>"], "text/plain": ["<class 'statsmodels.iolib.summary.Summary'>\n", "\"\"\"\n", "                           Logit Regression Results                           \n", "==============================================================================\n", "Dep. Variable:               Survived   No. Observations:                  623\n", "Model:                          Logit   Df Residuals:                      617\n", "Method:                           MLE   Df Model:                            5\n", "Date:                Tu<PERSON>, 01 Nov 2022   Pseudo R-squ.:                  0.3353\n", "Time:                        16:17:13   Log-Likelihood:                -276.66\n", "converged:                       True   LL-Null:                       -416.19\n", "Covariance Type:            nonrobust   LLR p-value:                 3.159e-58\n", "=================================================================================\n", "                    coef    std err          z      P>|z|      [0.025      0.975]\n", "---------------------------------------------------------------------------------\n", "const             3.0067      0.561      5.364      0.000       1.908       4.105\n", "Sex               2.7863      0.239     11.638      0.000       2.317       3.255\n", "Pclass           -1.0939      0.142     -7.728      0.000      -1.371      -0.816\n", "FamilySize       -0.2497      0.079     -3.165      0.002      -0.404      -0.095\n", "Age              -0.0400      0.009     -4.317      0.000      -0.058      -0.022\n", "Embarked_Code    -0.2680      0.138     -1.944      0.052      -0.538       0.002\n", "=================================================================================\n", "\"\"\""]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["import statsmodels.api as sm\n", "log_reg = sm.Logit(train2_y_bin,sm.add_constant(train2_x_bin)).fit()\n", "log_reg.summary()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 模型\n", "$$\\log \\left({p\\over 1-p}\\right) = 2.7863X_1-1.0939X_2-0.2497X_3-0.04X_4-0.268X_5*******$$\n", "\n", "$$ X_1为乘客性别$$\n", "$$X_2为乘客所属船层$$\n", "$$X_3为家庭成员数量$$\n", "$$X_4为乘客年龄$$\n", "$$X_5为登船口$$\n", "\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 模型解释\n", " - $e^{2.7863}=16.2241$表示在其余条件相同的情况下，女性（1）存活的概率是男性（0）的16.22倍\n", " - $\\beta_2=-1.0939$表示在其余条件相同的情况下，船层每增加一层，生存率越低\n", " - $e^{-0.2497}=0.7704$表示在其余条件相同的情况下，家庭成员数量每增加1，存活的概率提高77.9%\n", " - $e^{-0.04}=0.9610$表示在其余条件相同的情况下,乘客年龄每增加1，存活的概率提高96.07%\n", " - $\\beta_5=-0.2497$表示在其余条件相同的情况下，登船口越高，生存率越低\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 模型评估"]}, {"cell_type": "code", "execution_count": 31, "metadata": {}, "outputs": [{"data": {"text/plain": ["79.61"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["acc_train = round(logreg.score(train2_x_bin, train2_y_bin) * 100, 2)\n", "acc_train"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments：**\n", "- 模型的准确率为79.61%"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 对测试集进行模型性能评估"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["------------------------Metrices---------------------\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.85      0.83      0.84       168\n", "           1       0.72      0.75      0.74       100\n", "\n", "    accuracy                           0.80       268\n", "   macro avg       0.78      0.79      0.79       268\n", "weighted avg       0.80      0.80      0.80       268\n", "\n"]}], "source": ["# View summary of common classification metrics\n", "print(\"------------------------Metrices---------------------\")\n", "print(metrics.classification_report(y_true = test2_y_bin,y_pred = Y_pred))\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments：**\n", "- 模型的准确率为80%\n", "- 生存样本的召回率为75%"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.metrics import roc_curve, auc\n", "import matplotlib.pyplot as plt\n", "probs = logreg.predict_proba(test2_x_bin)\n", "preds = probs[:,1]\n", "#---find the FPR, TPR, and threshold---\n", "fpr, tpr, threshold = roc_curve(test2_y_bin, preds)\n", "#---find the area under the curve---\n", "roc_auc = auc(fpr, tpr)\n", "plt.plot(fpr, tpr, 'b', label = 'AUC = %0.2f' % roc_auc)\n", "plt.plot([0, 1], [0, 1],'r--')\n", "plt.xlim([0, 1])\n", "plt.ylim([0, 1])\n", "plt.ylabel('True Positive Rate (TPR)')\n", "plt.xlabel('False Positive Rate (FPR)')\n", "plt.title('Receiver Operating Characteristic (ROC)')\n", "plt.legend(loc = 'lower right')\n", "plt.show()\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["**comments：**\n", "- AUC=0.85，这个模型有一定的预测价值。是个较为优质的模型。"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}