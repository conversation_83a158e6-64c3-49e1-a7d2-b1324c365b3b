#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pandas as pd
import os

def excel_to_csv(excel_file, csv_file=None):
    """
    将Excel文件转换为CSV格式
    
    参数:
    excel_file: Excel文件路径
    csv_file: 输出CSV文件路径，如果为None，则使用相同的文件名，但扩展名为.csv
    
    返回:
    输出的CSV文件路径
    """
    # 如果没有指定CSV文件名，则使用Excel文件名，但扩展名改为.csv
    if csv_file is None:
        file_name = os.path.splitext(excel_file)[0]
        csv_file = f"{file_name}.csv"
    
    # 读取Excel文件
    print(f"正在读取Excel文件: {excel_file}")
    df = pd.read_excel(excel_file)
    
    # 将数据保存为CSV
    print(f"正在将数据保存为CSV: {csv_file}")
    df.to_csv(csv_file, index=False, encoding='utf-8')
    
    print(f"转换完成！CSV文件已保存为: {csv_file}")
    return csv_file

if __name__ == "__main__":
    # 指定Excel文件路径
    excel_file = "bankdata.xlsx"
    
    # 转换为CSV
    csv_file = excel_to_csv(excel_file)
    
    # 显示数据预览
    print("\n数据预览:")
    df = pd.read_csv(csv_file)
    print(df.head())
    print(f"\n总行数: {len(df)}") 