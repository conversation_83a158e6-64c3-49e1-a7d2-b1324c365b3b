<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="159">
            <item index="0" class="java.lang.String" itemvalue="pandas" />
            <item index="1" class="java.lang.String" itemvalue="seaborn" />
            <item index="2" class="java.lang.String" itemvalue="joblib" />
            <item index="3" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="4" class="java.lang.String" itemvalue="faker" />
            <item index="5" class="java.lang.String" itemvalue="matplotlib" />
            <item index="6" class="java.lang.String" itemvalue="peewee" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="9" class="java.lang.String" itemvalue="flask" />
            <item index="10" class="java.lang.String" itemvalue="werkzeug" />
            <item index="11" class="java.lang.String" itemvalue="selenium" />
            <item index="12" class="java.lang.String" itemvalue="flask-login" />
            <item index="13" class="java.lang.String" itemvalue="lxml" />
            <item index="14" class="java.lang.String" itemvalue="webdriver-manager" />
            <item index="15" class="java.lang.String" itemvalue="requests" />
            <item index="16" class="java.lang.String" itemvalue="pymysql" />
            <item index="17" class="java.lang.String" itemvalue="mysqlclient" />
            <item index="18" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="19" class="java.lang.String" itemvalue="openai" />
            <item index="20" class="java.lang.String" itemvalue="mysql-connector-python" />
            <item index="21" class="java.lang.String" itemvalue="pymongo" />
            <item index="22" class="java.lang.String" itemvalue="snownlp" />
            <item index="23" class="java.lang.String" itemvalue="email-validator" />
            <item index="24" class="java.lang.String" itemvalue="flask-wtf" />
            <item index="25" class="java.lang.String" itemvalue="flask-cors" />
            <item index="26" class="java.lang.String" itemvalue="plotly" />
            <item index="27" class="java.lang.String" itemvalue="dash" />
            <item index="28" class="java.lang.String" itemvalue="beautifulsoup4" />
            <item index="29" class="java.lang.String" itemvalue="pillow" />
            <item index="30" class="java.lang.String" itemvalue="torch" />
            <item index="31" class="java.lang.String" itemvalue="openpyxl" />
            <item index="32" class="java.lang.String" itemvalue="protobuf" />
            <item index="33" class="java.lang.String" itemvalue="bs4" />
            <item index="34" class="java.lang.String" itemvalue="threadpoolctl" />
            <item index="35" class="java.lang.String" itemvalue="Brlapi" />
            <item index="36" class="java.lang.String" itemvalue="PyYAML" />
            <item index="37" class="java.lang.String" itemvalue="ufw" />
            <item index="38" class="java.lang.String" itemvalue="pycups" />
            <item index="39" class="java.lang.String" itemvalue="ubuntu-pro-client" />
            <item index="40" class="java.lang.String" itemvalue="pycairo" />
            <item index="41" class="java.lang.String" itemvalue="pycparser" />
            <item index="42" class="java.lang.String" itemvalue="macaroonbakery" />
            <item index="43" class="java.lang.String" itemvalue="PyGObject" />
            <item index="44" class="java.lang.String" itemvalue="distro-info" />
            <item index="45" class="java.lang.String" itemvalue="systemd-python" />
            <item index="46" class="java.lang.String" itemvalue="PyNaCl" />
            <item index="47" class="java.lang.String" itemvalue="certifi" />
            <item index="48" class="java.lang.String" itemvalue="oauthlib" />
            <item index="49" class="java.lang.String" itemvalue="soupsieve" />
            <item index="50" class="java.lang.String" itemvalue="language-selector" />
            <item index="51" class="java.lang.String" itemvalue="pyparsing" />
            <item index="52" class="java.lang.String" itemvalue="unattended-upgrades" />
            <item index="53" class="java.lang.String" itemvalue="pydantic" />
            <item index="54" class="java.lang.String" itemvalue="lazr.restfulclient" />
            <item index="55" class="java.lang.String" itemvalue="asgiref" />
            <item index="56" class="java.lang.String" itemvalue="cryptography" />
            <item index="57" class="java.lang.String" itemvalue="kiwisolver" />
            <item index="58" class="java.lang.String" itemvalue="reportlab" />
            <item index="59" class="java.lang.String" itemvalue="wordcloud" />
            <item index="60" class="java.lang.String" itemvalue="pexpect" />
            <item index="61" class="java.lang.String" itemvalue="lazr.uri" />
            <item index="62" class="java.lang.String" itemvalue="python-apt" />
            <item index="63" class="java.lang.String" itemvalue="SecretStorage" />
            <item index="64" class="java.lang.String" itemvalue="contourpy" />
            <item index="65" class="java.lang.String" itemvalue="fonttools" />
            <item index="66" class="java.lang.String" itemvalue="pyxdg" />
            <item index="67" class="java.lang.String" itemvalue="ssh-import-id" />
            <item index="68" class="java.lang.String" itemvalue="pydantic_core" />
            <item index="69" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="70" class="java.lang.String" itemvalue="idna" />
            <item index="71" class="java.lang.String" itemvalue="pymacaroons" />
            <item index="72" class="java.lang.String" itemvalue="distro" />
            <item index="73" class="java.lang.String" itemvalue="PyJWT" />
            <item index="74" class="java.lang.String" itemvalue="defer" />
            <item index="75" class="java.lang.String" itemvalue="netifaces" />
            <item index="76" class="java.lang.String" itemvalue="ptyprocess" />
            <item index="77" class="java.lang.String" itemvalue="more-itertools" />
            <item index="78" class="java.lang.String" itemvalue="cffi" />
            <item index="79" class="java.lang.String" itemvalue="httplib2" />
            <item index="80" class="java.lang.String" itemvalue="pyspark" />
            <item index="81" class="java.lang.String" itemvalue="py4j" />
            <item index="82" class="java.lang.String" itemvalue="apturl" />
            <item index="83" class="java.lang.String" itemvalue="wadllib" />
            <item index="84" class="java.lang.String" itemvalue="importlib-metadata" />
            <item index="85" class="java.lang.String" itemvalue="jeepney" />
            <item index="86" class="java.lang.String" itemvalue="python-debian" />
            <item index="87" class="java.lang.String" itemvalue="sqlparse" />
            <item index="88" class="java.lang.String" itemvalue="django-ninja" />
            <item index="89" class="java.lang.String" itemvalue="cupshelpers" />
            <item index="90" class="java.lang.String" itemvalue="launchpadlib" />
            <item index="91" class="java.lang.String" itemvalue="zipp" />
            <item index="92" class="java.lang.String" itemvalue="louis" />
            <item index="93" class="java.lang.String" itemvalue="urllib3" />
            <item index="94" class="java.lang.String" itemvalue="djangorestframework" />
            <item index="95" class="java.lang.String" itemvalue="django-cors-headers" />
            <item index="96" class="java.lang.String" itemvalue="ubuntu-drivers-common" />
            <item index="97" class="java.lang.String" itemvalue="annotated-types" />
            <item index="98" class="java.lang.String" itemvalue="scipy" />
            <item index="99" class="java.lang.String" itemvalue="six" />
            <item index="100" class="java.lang.String" itemvalue="importlib_resources" />
            <item index="101" class="java.lang.String" itemvalue="xkit" />
            <item index="102" class="java.lang.String" itemvalue="tzdata" />
            <item index="103" class="java.lang.String" itemvalue="packaging" />
            <item index="104" class="java.lang.String" itemvalue="chardet" />
            <item index="105" class="java.lang.String" itemvalue="django-rest-framework" />
            <item index="106" class="java.lang.String" itemvalue="pyRFC3339" />
            <item index="107" class="java.lang.String" itemvalue="colorama" />
            <item index="108" class="java.lang.String" itemvalue="Django" />
            <item index="109" class="java.lang.String" itemvalue="typing_extensions" />
            <item index="110" class="java.lang.String" itemvalue="dbus-python" />
            <item index="111" class="java.lang.String" itemvalue="olefile" />
            <item index="112" class="java.lang.String" itemvalue="xdg" />
            <item index="113" class="java.lang.String" itemvalue="command-not-found" />
            <item index="114" class="java.lang.String" itemvalue="pytz" />
            <item index="115" class="java.lang.String" itemvalue="keyring" />
            <item index="116" class="java.lang.String" itemvalue="transformers" />
            <item index="117" class="java.lang.String" itemvalue="nltk" />
            <item index="118" class="java.lang.String" itemvalue="rouge" />
            <item index="119" class="java.lang.String" itemvalue="tqdm" />
            <item index="120" class="java.lang.String" itemvalue="deeplabcut" />
            <item index="121" class="java.lang.String" itemvalue="opencv-python" />
            <item index="122" class="java.lang.String" itemvalue="geopy" />
            <item index="123" class="java.lang.String" itemvalue="folium" />
            <item index="124" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="125" class="java.lang.String" itemvalue="Flask-WTF" />
            <item index="126" class="java.lang.String" itemvalue="Flask-Login" />
            <item index="127" class="java.lang.String" itemvalue="Flask" />
            <item index="128" class="java.lang.String" itemvalue="pygame" />
            <item index="129" class="java.lang.String" itemvalue="soundfile" />
            <item index="130" class="java.lang.String" itemvalue="librosa" />
            <item index="131" class="java.lang.String" itemvalue="torchvision" />
            <item index="132" class="java.lang.String" itemvalue="pytorch-grad-cam" />
            <item index="133" class="java.lang.String" itemvalue="ipython" />
            <item index="134" class="java.lang.String" itemvalue="pyyaml" />
            <item index="135" class="java.lang.String" itemvalue="ultralytics" />
            <item index="136" class="java.lang.String" itemvalue="Pillow" />
            <item index="137" class="java.lang.String" itemvalue="pydicom" />
            <item index="138" class="java.lang.String" itemvalue="tensorboard" />
            <item index="139" class="java.lang.String" itemvalue="thop" />
            <item index="140" class="java.lang.String" itemvalue="psutil" />
            <item index="141" class="java.lang.String" itemvalue="blinker" />
            <item index="142" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="143" class="java.lang.String" itemvalue="Flask-MySQLdb" />
            <item index="144" class="java.lang.String" itemvalue="cycler" />
            <item index="145" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="146" class="java.lang.String" itemvalue="itsdangerous" />
            <item index="147" class="java.lang.String" itemvalue="click" />
            <item index="148" class="java.lang.String" itemvalue="Jinja2" />
            <item index="149" class="java.lang.String" itemvalue="jieba" />
            <item index="150" class="java.lang.String" itemvalue="pysolr" />
            <item index="151" class="java.lang.String" itemvalue="WTForms" />
            <item index="152" class="java.lang.String" itemvalue="email_validator" />
            <item index="153" class="java.lang.String" itemvalue="torchaudio" />
            <item index="154" class="java.lang.String" itemvalue="PyQt5" />
            <item index="155" class="java.lang.String" itemvalue="validators" />
            <item index="156" class="java.lang.String" itemvalue="wtforms" />
            <item index="157" class="java.lang.String" itemvalue="tensorflow" />
            <item index="158" class="java.lang.String" itemvalue="xlrd" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>