{"cells": [{"cell_type": "code", "id": "initial_id", "metadata": {"collapsed": true, "ExecuteTime": {"end_time": "2025-06-30T23:10:49.456118Z", "start_time": "2025-06-30T23:10:49.442119Z"}}, "source": ["# 姓名:罗婉晴\n", "# 学号:23550512032"], "outputs": [], "execution_count": 1}, {"metadata": {}, "cell_type": "markdown", "source": "# # # 银行贷款违约风险分析", "id": "94ca15755a114caa"}, {"metadata": {}, "cell_type": "markdown", "source": ["# # ## 数据变量解释\n", "# # \n", "# # | 变量名 | 变量描述 |\n", "# # |--------|----------|\n", "# # | 年龄   | 客户年龄 |\n", "# # | 教育   | 客户教育水平，数值越大表示教育水平越高 |\n", "# # | 工龄   | 客户工作年限 |\n", "# # | 地址   | 客户居住地址稳定性，数值越大表示地址越稳定 |\n", "# # | 收入   | 客户收入水平 |\n", "# # | 负债率 | 客户负债占收入的比例 |\n", "# # | 信用卡负债 | 客户信用卡负债金额 |\n", "# # | 其他负债 | 客户其他类型负债金额 |\n", "# # | 违约   | 是否违约，1表示违约，0表示未违约 |"], "id": "c6743d3e7d779b6d"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:10:56.262508Z", "start_time": "2025-06-30T23:10:56.258506Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n"], "id": "277a24bf99017baf", "outputs": [], "execution_count": 2}, {"metadata": {}, "cell_type": "markdown", "source": "# # ## 1. 数据加载与探索性分析", "id": "e7fb289900bc032b"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:10:58.503506Z", "start_time": "2025-06-30T23:10:58.410672Z"}}, "cell_type": "code", "source": ["# 加载数据\n", "bank_data = pd.read_csv(\"bankdata.csv\")\n", "\n", "# 显示前几行数据\n", "print(\"数据前5行：\")\n", "print(bank_data.head())\n"], "id": "e6e83493d8beb77d", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据前5行：\n", "   年龄  教育  工龄  地址   收入   负债率      信用卡负债      其他负债  违约\n", "0  41   3  17  12  176   9.3  11.359392  5.008608   1\n", "1  27   1  10   6   31  17.3   1.362202  4.000798   0\n", "2  40   1  15  14   55   5.5   0.856075  2.168925   0\n", "3  41   1  15  14  120   2.9   2.658720  0.821280   0\n", "4  24   2   2   0   28  17.3   1.787436  3.056564   1\n"]}], "execution_count": 3}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 1.1 描述性统计分析", "id": "1df7eb280377caf4"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:00.413428Z", "start_time": "2025-06-30T23:11:00.293310Z"}}, "cell_type": "code", "source": ["# 查看数据基本信息\n", "print(\"\\n数据基本信息：\")\n", "bank_data.info()\n", "\n", "# 查看数据的统计描述\n", "print(\"\\n数据统计描述：\")\n", "print(bank_data.describe().T)\n", "\n", "# 查看目标变量分布\n", "print(\"\\n违约情况分布：\")\n", "print(bank_data['违约'].value_counts())\n", "print(f\"违约率: {bank_data['违约'].mean()*100:.2f}%\")\n"], "id": "b9e557e42bdea034", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数据基本信息：\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 700 entries, 0 to 699\n", "Data columns (total 9 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   年龄      700 non-null    int64  \n", " 1   教育      700 non-null    int64  \n", " 2   工龄      700 non-null    int64  \n", " 3   地址      700 non-null    int64  \n", " 4   收入      700 non-null    int64  \n", " 5   负债率     700 non-null    float64\n", " 6   信用卡负债   700 non-null    float64\n", " 7   其他负债    700 non-null    float64\n", " 8   违约      700 non-null    int64  \n", "dtypes: float64(3), int64(6)\n", "memory usage: 49.3 KB\n", "\n", "数据统计描述：\n", "       count       mean        std        min        25%        50%  \\\n", "年龄     700.0  34.860000   7.997342  20.000000  29.000000  34.000000   \n", "教育     700.0   1.722857   0.928206   1.000000   1.000000   1.000000   \n", "工龄     700.0   8.388571   6.658039   0.000000   3.000000   7.000000   \n", "地址     700.0   8.278571   6.824877   0.000000   3.000000   7.000000   \n", "收入     700.0  45.601429  36.814226  14.000000  24.000000  34.000000   \n", "负债率    700.0  10.260571   6.827234   0.400000   5.000000   8.600000   \n", "信用卡负债  700.0   1.553553   2.117197   0.011696   0.369059   0.854869   \n", "其他负债   700.0   3.058209   3.287555   0.045584   1.044178   1.987567   \n", "违约     700.0   0.261429   0.439727   0.000000   0.000000   0.000000   \n", "\n", "             75%        max  \n", "年龄     40.000000   56.00000  \n", "教育      2.000000    5.00000  \n", "工龄     12.000000   31.00000  \n", "地址     12.000000   34.00000  \n", "收入     55.000000  446.00000  \n", "负债率    14.125000   41.30000  \n", "信用卡负债   1.901955   20.56131  \n", "其他负债    3.923065   27.03360  \n", "违约      1.000000    1.00000  \n", "\n", "违约情况分布：\n", "违约\n", "0    517\n", "1    183\n", "Name: count, dtype: int64\n", "违约率: 26.14%\n"]}], "execution_count": 4}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：**\n", "# # - 数据集包含700个样本，9个变量，无缺失值\n", "# # - 违约率为约26.14%\n", "# "], "id": "fdbe609a520eb954"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 1.2 数据可视化分析", "id": "c1d37e62b20a5706"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:05.288006Z", "start_time": "2025-06-30T23:11:04.245029Z"}}, "cell_type": "code", "source": ["# 设置图形大小\n", "plt.figure(figsize=(12, 10))\n", "\n", "# 绘制违约与非违约样本数量对比图\n", "plt.subplot(2, 2, 1)\n", "sns.countplot(x='违约', data=bank_data)\n", "plt.title('违约与非违约样本数量对比')\n", "plt.xlabel('是否违约')\n", "plt.ylabel('样本数量')\n", "\n", "# 绘制年龄分布与违约关系\n", "plt.subplot(2, 2, 2)\n", "sns.boxplot(x='违约', y='年龄', data=bank_data)\n", "plt.title('年龄与违约关系')\n", "plt.xlabel('是否违约')\n", "plt.ylabel('年龄')\n", "\n", "# 绘制收入分布与违约关系\n", "plt.subplot(2, 2, 3)\n", "sns.boxplot(x='违约', y='收入', data=bank_data)\n", "plt.title('收入与违约关系')\n", "plt.xlabel('是否违约')\n", "plt.ylabel('收入')\n", "\n", "# 绘制负债率分布与违约关系\n", "plt.subplot(2, 2, 4)\n", "sns.boxplot(x='违约', y='负债率', data=bank_data)\n", "plt.title('负债率与违约关系')\n", "plt.xlabel('是否违约')\n", "plt.ylabel('负债率')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "5428ad3d01b8f1bb", "outputs": [{"data": {"text/plain": ["<Figure size 1200x1000 with 4 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 5}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：**\n", "# # - 违约样本占比较少，约为26%，数据存在不平衡\n", "# # - 年龄与违约关系不明显，但违约客户的年龄中位数略低于非违约客户\n", "# # - 违约客户的收入中位数略低于非违约客户\n", "# # - 违约客户的负债率明显高于非违约客户，表明负债率可能是违约的重要影响因素\n", "# "], "id": "fc0da63b4910e9f8"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 1.3 变量相关性分析", "id": "9785ce89d180c4fe"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:08.482556Z", "start_time": "2025-06-30T23:11:08.213701Z"}}, "cell_type": "code", "source": ["# 计算相关系数矩阵\n", "corr_matrix = bank_data.corr()\n", "\n", "# 绘制热力图\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt='.2f')\n", "plt.title('变量相关性热力图')\n", "plt.show()\n", "\n", "# 各变量与违约的相关性\n", "print(\"\\n各变量与违约的相关性：\")\n", "print(corr_matrix['违约'].sort_values(ascending=False))\n"], "id": "8d5285632ed92d2e", "outputs": [{"data": {"text/plain": ["<Figure size 1000x800 with 2 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "各变量与违约的相关性：\n", "违约       1.000000\n", "负债率      0.389575\n", "信用卡负债    0.244740\n", "其他负债     0.145713\n", "教育       0.114676\n", "收入      -0.070970\n", "年龄      -0.137657\n", "地址      -0.164451\n", "工龄      -0.282978\n", "Name: 违约, dtype: float64\n"]}], "execution_count": 6}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：**\n", "# # - 负债率与违约呈正相关，相关系数较高，说明负债率越高，违约风险越大\n", "# # - 信用卡负债和其他负债与违约也有一定正相关性\n", "# # - 收入与违约呈负相关，说明收入越高，违约风险越低\n", "# "], "id": "58dccec12d3a365"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ## 2. 数据预处理", "id": "4f9becb20b780800"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 2.1 检查缺失值", "id": "ff0ea907105ff6ae"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:12.016281Z", "start_time": "2025-06-30T23:11:12.007280Z"}}, "cell_type": "code", "source": ["# 检查缺失值\n", "print(\"缺失值检查：\")\n", "print(bank_data.isnull().sum())\n"], "id": "9b52a8d6915d154d", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["缺失值检查：\n", "年龄       0\n", "教育       0\n", "工龄       0\n", "地址       0\n", "收入       0\n", "负债率      0\n", "信用卡负债    0\n", "其他负债     0\n", "违约       0\n", "dtype: int64\n"]}], "execution_count": 7}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：** 数据集中没有缺失值，不需要进行缺失值处理。\n", "# "], "id": "7e4e92e1b1e5d310"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 2.2 特征工程", "id": "a6fa5be36e13b913"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:14.841149Z", "start_time": "2025-06-30T23:11:14.812370Z"}}, "cell_type": "code", "source": ["# 创建新特征：总负债\n", "bank_data['总负债'] = bank_data['信用卡负债'] + bank_data['其他负债']\n", "print(\"创建新特征后的数据：\")\n", "print(bank_data.head())\n", "\n", "# 创建新特征：收入负债比\n", "bank_data['收入负债比'] = bank_data['总负债'] / bank_data['收入']\n", "print(\"创建收入负债比特征后的数据：\")\n", "print(bank_data.head())\n", "\n", "# 创建新特征：信用卡负债占比\n", "bank_data['信用卡负债占比'] = bank_data['信用卡负债'] / bank_data['总负债']\n", "# 处理可能的除零情况\n", "bank_data['信用卡负债占比'] = bank_data['信用卡负债占比'].fillna(0)\n", "print(\"创建信用卡负债占比特征后的数据：\")\n", "print(bank_data.head())\n"], "id": "2dab2a84141bceda", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建新特征后的数据：\n", "   年龄  教育  工龄  地址   收入   负债率      信用卡负债      其他负债  违约     总负债\n", "0  41   3  17  12  176   9.3  11.359392  5.008608   1  16.368\n", "1  27   1  10   6   31  17.3   1.362202  4.000798   0   5.363\n", "2  40   1  15  14   55   5.5   0.856075  2.168925   0   3.025\n", "3  41   1  15  14  120   2.9   2.658720  0.821280   0   3.480\n", "4  24   2   2   0   28  17.3   1.787436  3.056564   1   4.844\n", "创建收入负债比特征后的数据：\n", "   年龄  教育  工龄  地址   收入   负债率      信用卡负债      其他负债  违约     总负债  收入负债比\n", "0  41   3  17  12  176   9.3  11.359392  5.008608   1  16.368  0.093\n", "1  27   1  10   6   31  17.3   1.362202  4.000798   0   5.363  0.173\n", "2  40   1  15  14   55   5.5   0.856075  2.168925   0   3.025  0.055\n", "3  41   1  15  14  120   2.9   2.658720  0.821280   0   3.480  0.029\n", "4  24   2   2   0   28  17.3   1.787436  3.056564   1   4.844  0.173\n", "创建信用卡负债占比特征后的数据：\n", "   年龄  教育  工龄  地址   收入   负债率      信用卡负债      其他负债  违约     总负债  收入负债比  信用卡负债占比\n", "0  41   3  17  12  176   9.3  11.359392  5.008608   1  16.368  0.093    0.694\n", "1  27   1  10   6   31  17.3   1.362202  4.000798   0   5.363  0.173    0.254\n", "2  40   1  15  14   55   5.5   0.856075  2.168925   0   3.025  0.055    0.283\n", "3  41   1  15  14  120   2.9   2.658720  0.821280   0   3.480  0.029    0.764\n", "4  24   2   2   0   28  17.3   1.787436  3.056564   1   4.844  0.173    0.369\n"]}], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：**\n", "# # - 创建了三个新特征：总负债、收入负债比和信用卡负债占比\n", "# # - 这些新特征可能会对违约预测提供额外的信息\n", "# "], "id": "8582d574a8ed30a5"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ## 3. 建立逻辑回归模型", "id": "2ac010899840a73d"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 3.1 准备训练和测试数据", "id": "715e888f6e6d53d7"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:18.947233Z", "start_time": "2025-06-30T23:11:18.503912Z"}}, "cell_type": "code", "source": ["# 选择特征变量和目标变量\n", "X = bank_data[['年龄', '教育', '工龄', '地址', '收入', '负债率', '总负债', '收入负债比', '信用卡负债占比']]\n", "y = bank_data['违约']\n", "\n", "# 数据标准化\n", "from sklearn.preprocessing import StandardScaler\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "X_scaled = pd.DataFrame(X_scaled, columns=X.columns)\n", "\n", "# 划分训练集和测试集\n", "from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.3, random_state=42)\n", "\n", "print(\"训练集大小:\", X_train.shape)\n", "print(\"测试集大小:\", X_test.shape)\n"], "id": "741ddb6d65eaf13a", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集大小: (490, 9)\n", "测试集大小: (210, 9)\n"]}], "execution_count": 9}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 3.2 建立基础逻辑回归模型", "id": "f189becbb676f6d8"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:21.226835Z", "start_time": "2025-06-30T23:11:20.935846Z"}}, "cell_type": "code", "source": ["from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc\n", "\n", "# 创建逻辑回归模型\n", "model1 = LogisticRegression(random_state=42)\n", "model1.fit(X_train, y_train)\n", "\n", "# 在测试集上进行预测\n", "y_pred1 = model1.predict(X_test)\n", "y_prob1 = model1.predict_proba(X_test)[:, 1]\n", "\n", "# 输出模型系数\n", "coef_df = pd.DataFrame({\n", "    '特征': X.columns,\n", "    '系数': model1.coef_[0]\n", "})\n", "print(\"模型系数：\")\n", "print(coef_df.sort_values(by='系数', ascending=False))\n", "\n", "# 计算并输出模型评估指标\n", "print(\"\\n模型评估报告：\")\n", "print(classification_report(y_test, y_pred1))\n", "\n", "# 混淆矩阵\n", "conf_matrix = confusion_matrix(y_test, y_pred1)\n", "print(\"\\n混淆矩阵：\")\n", "print(conf_matrix)\n", "\n", "# 计算准确率\n", "accuracy1 = model1.score(X_test, y_test)\n", "print(f\"\\n模型准确率: {accuracy1:.4f}\")\n"], "id": "64785e15ea214e18", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型系数：\n", "        特征        系数\n", "6      总负债  0.995230\n", "8  信用卡负债占比  0.468648\n", "0       年龄  0.282677\n", "7    收入负债比  0.214726\n", "5      负债率  0.214726\n", "1       教育  0.007596\n", "4       收入 -0.021344\n", "3       地址 -0.587918\n", "2       工龄 -1.594953\n", "\n", "模型评估报告：\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.88      0.93      0.90       161\n", "           1       0.72      0.57      0.64        49\n", "\n", "    accuracy                           0.85       210\n", "   macro avg       0.80      0.75      0.77       210\n", "weighted avg       0.84      0.85      0.84       210\n", "\n", "\n", "混淆矩阵：\n", "[[150  11]\n", " [ 21  28]]\n", "\n", "模型准确率: 0.8476\n"]}], "execution_count": 10}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：**\n", "# # - 负债率、收入负债比和信用卡负债占比对违约预测有较大的正向影响\n", "# # - 收入和教育水平对违约预测有较大的负向影响\n", "# # - 模型在测试集上的准确率为约84.76%\n", "# "], "id": "5dc500368bfd9688"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 3.3 绘制ROC曲线", "id": "2514957f839a5dea"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:25.542652Z", "start_time": "2025-06-30T23:11:25.223074Z"}}, "cell_type": "code", "source": ["# 计算ROC曲线\n", "fpr1, tpr1, _ = roc_curve(y_test, y_prob1)\n", "roc_auc1 = auc(fpr1, tpr1)\n", "\n", "# 绘制ROC曲线\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(fpr1, tpr1, color='blue', lw=2, label=f'逻辑回归模型 ROC曲线 (AUC = {roc_auc1:.2f})')\n", "plt.plot([0, 1], [0, 1], color='gray', linestyle='--')\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('假正例率 (FPR)')\n", "plt.ylabel('真正例率 (TPR)')\n", "plt.title('逻辑回归模型ROC曲线')\n", "plt.legend(loc=\"lower right\")\n", "plt.show()\n"], "id": "d77c217dc7d372c9", "outputs": [{"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 11}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：**\n", "# # - 逻辑回归模型的AUC值为约0.88，表明模型有较好的区分能力\n", "# # - ROC曲线明显优于随机猜测（对角线）\n", "# "], "id": "e23eaf532e03bad0"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:30.995745Z", "start_time": "2025-06-30T23:11:30.378951Z"}}, "cell_type": "code", "source": ["# 创建随机森林分类器\n", "from sklearn.ensemble import RandomForestClassifier\n", "\n", "model2 = RandomForestClassifier(n_estimators=100, random_state=42)\n", "model2.fit(X_train, y_train)\n", "\n", "# 在测试集上进行预测\n", "y_pred2 = model2.predict(X_test)\n", "y_prob2 = model2.predict_proba(X_test)[:, 1]\n", "\n", "# 输出特征重要性\n", "feature_importance = pd.DataFrame({\n", "    '特征': X.columns,\n", "    '重要性': model2.feature_importances_\n", "})\n", "print(\"随机森林模型特征重要性：\")\n", "print(feature_importance.sort_values(by='重要性', ascending=False))\n", "\n", "# 计算并输出模型评估指标\n", "print(\"\\n随机森林模型评估报告：\")\n", "print(classification_report(y_test, y_pred2))\n", "\n", "# 混淆矩阵\n", "conf_matrix2 = confusion_matrix(y_test, y_pred2)\n", "print(\"\\n随机森林模型混淆矩阵：\")\n", "print(conf_matrix2)\n", "\n", "# 计算准确率\n", "accuracy2 = model2.score(X_test, y_test)\n", "print(f\"\\n随机森林模型准确率: {accuracy2:.4f}\")\n", "\n", "# 计算ROC曲线\n", "fpr2, tpr2, _ = roc_curve(y_test, y_prob2)\n", "roc_auc2 = auc(fpr2, tpr2)\n"], "id": "9239715ba07f4c0e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["随机森林模型特征重要性：\n", "        特征       重要性\n", "2       工龄  0.160672\n", "7    收入负债比  0.147637\n", "5      负债率  0.132481\n", "6      总负债  0.131178\n", "8  信用卡负债占比  0.112348\n", "4       收入  0.094477\n", "0       年龄  0.093145\n", "3       地址  0.092914\n", "1       教育  0.035149\n", "\n", "随机森林模型评估报告：\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.85      0.92      0.88       161\n", "           1       0.63      0.45      0.52        49\n", "\n", "    accuracy                           0.81       210\n", "   macro avg       0.74      0.68      0.70       210\n", "weighted avg       0.80      0.81      0.80       210\n", "\n", "\n", "随机森林模型混淆矩阵：\n", "[[148  13]\n", " [ 27  22]]\n", "\n", "随机森林模型准确率: 0.8095\n"]}], "execution_count": 12}, {"metadata": {}, "cell_type": "markdown", "source": "# # ### 4.2 比较两个模型的ROC曲线", "id": "c2491c22c40c6965"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:34.140474Z", "start_time": "2025-06-30T23:11:33.993756Z"}}, "cell_type": "code", "source": ["# 绘制两个模型的ROC曲线\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(fpr1, tpr1, color='blue', lw=2, label=f'逻辑回归模型 (AUC = {roc_auc1:.2f})')\n", "plt.plot(fpr2, tpr2, color='red', lw=2, label=f'随机森林模型 (AUC = {roc_auc2:.2f})')\n", "plt.plot([0, 1], [0, 1], color='gray', linestyle='--')\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('假正例率 (FPR)')\n", "plt.ylabel('真正例率 (TPR)')\n", "plt.title('两个模型ROC曲线比较')\n", "plt.legend(loc=\"lower right\")\n", "plt.show()\n"], "id": "3c437dcca6e9fbd8", "outputs": [{"data": {"text/plain": ["<Figure size 800x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 13}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:37.699406Z", "start_time": "2025-06-30T23:11:37.669405Z"}}, "cell_type": "code", "source": ["# 比较两个模型的性能\n", "print(\"模型性能比较：\")\n", "print(f\"逻辑回归模型准确率: {accuracy1:.4f}, AUC: {roc_auc1:.4f}\")\n", "print(f\"随机森林模型准确率: {accuracy2:.4f}, AUC: {roc_auc2:.4f}\")\n", "\n", "# 比较两个模型的召回率\n", "from sklearn.metrics import recall_score\n", "recall1 = recall_score(y_test, y_pred1)\n", "recall2 = recall_score(y_test, y_pred2)\n", "\n", "print(f\"逻辑回归模型违约样本召回率: {recall1:.4f}\")\n", "print(f\"随机森林模型违约样本召回率: {recall2:.4f}\")\n", "\n", "# 比较两个模型的精确率\n", "from sklearn.metrics import precision_score\n", "precision1 = precision_score(y_test, y_pred1)\n", "precision2 = precision_score(y_test, y_pred2)\n", "\n", "print(f\"逻辑回归模型违约样本精确率: {precision1:.4f}\")\n", "print(f\"随机森林模型违约样本精确率: {precision2:.4f}\")\n"], "id": "5fa59520d4692b6b", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型性能比较：\n", "逻辑回归模型准确率: 0.8476, AUC: 0.8824\n", "随机森林模型准确率: 0.8095, AUC: 0.7943\n", "逻辑回归模型违约样本召回率: 0.5714\n", "随机森林模型违约样本召回率: 0.4490\n", "逻辑回归模型违约样本精确率: 0.7179\n", "随机森林模型违约样本精确率: 0.6286\n"]}], "execution_count": 14}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：**\n", "# # - 逻辑回归模型和随机森林模型在准确率上表现不同\n", "# # - 随机森林模型通常能更好地捕捉特征间的非线性关系\n", "# # - 随机森林模型的特征重要性排序与逻辑回归模型的系数排序有所不同\n", "# # - 在银行贷款违约风险控制中，需要根据业务需求选择合适的模型\n", "# "], "id": "be74c975fd49afcd"}, {"metadata": {}, "cell_type": "markdown", "source": "# # ## 7. 模型应用与风险预测", "id": "2c0de74bd82327b9"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-30T23:11:57.396227Z", "start_time": "2025-06-30T23:11:57.377227Z"}}, "cell_type": "code", "source": ["# 创建一个示例客户数据\n", "example_customer = pd.DataFrame({\n", "    '年龄': [30],\n", "    '教育': [2],\n", "    '工龄': [5],\n", "    '地址': [3],\n", "    '收入': [40],\n", "    '负债率': [20],\n", "    '总负债': [8],\n", "    '收入负债比': [0.2],\n", "    '信用卡负债占比': [0.5]\n", "})\n", "\n", "# 标准化示例数据\n", "example_customer_scaled = scaler.transform(example_customer)\n", "\n", "# 使用两个模型进行预测\n", "prob1 = model1.predict_proba(example_customer_scaled)[0, 1]\n", "prob2 = model2.predict_proba(example_customer_scaled)[0, 1]\n", "\n", "print(f\"示例客户违约概率 (逻辑回归模型): {prob1:.4f}\")\n", "print(f\"示例客户违约概率 (随机森林模型): {prob2:.4f}\")\n", "\n", "# 根据不同阈值进行决策\n", "thresholds = [0.3, 0.4, 0.5]\n", "for threshold in thresholds:\n", "    decision1 = \"违约\" if prob1 > threshold else \"不违约\"\n", "    decision2 = \"违约\" if prob2 > threshold else \"不违约\"\n", "    print(f\"阈值 {threshold}: 逻辑回归模型预测 {decision1}, 随机森林模型预测 {decision2}\")\n"], "id": "214b111881b42caf", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["示例客户违约概率 (逻辑回归模型): 0.7877\n", "示例客户违约概率 (随机森林模型): 0.4500\n", "阈值 0.3: 逻辑回归模型预测 违约, 随机森林模型预测 违约\n", "阈值 0.4: 逻辑回归模型预测 违约, 随机森林模型预测 违约\n", "阈值 0.5: 逻辑回归模型预测 违约, 随机森林模型预测 不违约\n"]}, {"name": "stderr", "output_type": "stream", "text": ["H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but LogisticRegression was fitted with feature names\n", "  warnings.warn(\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but RandomForestClassifier was fitted with feature names\n", "  warnings.warn(\n"]}], "execution_count": 15}, {"metadata": {}, "cell_type": "markdown", "source": ["# # **结论：**\n", "# # - 示例客户在逻辑回归模型中的违约概率为约0.7877\n", "# # - 示例客户在随机森林模型中的违约概率可能不同\n", "# # - 不同的决策阈值会导致不同的贷款决策\n", "# # - 银行可以根据自身的风险偏好和成本-收益分析来选择适当的模型和阈值\n", "# "], "id": "f77abd7cc7352cdb"}, {"metadata": {}, "cell_type": "markdown", "source": ["# # ## 8. 总结与建议\n", "# # \n", "# # **模型结果总结：**\n", "# # \n", "# # 1. 数据分析显示，负债率、收入和教育水平是影响客户违约的重要因素\n", "# # 2. 创建的新特征（总负债、收入负债比和信用卡负债占比）对模型有显著贡献\n", "# # 3. 逻辑回归模型提供了良好的基线性能，特别是在解释性方面\n", "# # \n", "# # **业务建议：**\n", "\n", "# # 1. 重点关注客户的负债率和收入负债比，这是预测违约的关键指标\n", "# # 2. 对于教育水平较低、收入较低且负债率高的客户，应采取更谨慎的贷款策略\n", "# # 3. 建议根据银行的风险承受能力设置合适的概率阈值\n", "# # \n", "# # **模型改进方向：**\n", "# # 1. 收集更多的客户特征，如信用记录、职业稳定性等\n", "# # 2. 尝试更复杂的集成学习模型，如梯度提升树、XGBoost等\n", "# # 3. 考虑特征之间的交互作用，创建更有意义的交互特征 "], "id": "8dca95319bd173e6"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "ee6a29d557f04cab"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}