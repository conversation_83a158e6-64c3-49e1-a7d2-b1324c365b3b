#%% md
# # （一）数据预处理
# 通过分析，将所需数据表进行合并，并对合并后的数据表进行清洗，查看数据是否存在重复、空值、特殊符号等，并用已学过知识进行处理。
#%%
#导入pandas
import pandas as pd


#%%
# 读取学生信息表

#%%
# 读取作业完成情况表

#%%
# 合并数据表
# 以学号为键进行合并

#%%
# 查看合并后的数据

#%%

#%% md
# ## 数据清洗
#%%
# 检查重复行

#%%
# 检查空值

#%%
# 由于课后作业表中的空值是学生没有回答的作业，所以空值填充为0分

#%%
 #验证是否还有空值
    
#%% md
# # （二）数据分析
#%% md
# ### 1. 个人学习成绩分析
# ### （1）计算本人的作业平均成绩、最高成绩、最低成绩
#%%
#学生本人学号、姓名
#筛选出学生本人课后作业成绩数据

#%%
#查看列索引用于选取列名

#%%
# 计算本人5次作业的平均成绩、最高成绩、最低成绩

#%%
# 输出结果

#%% md
# ### （2）个人成绩差异比较，从“作业成绩统计表”中提取自己的作业成绩序列，计算每个成绩与平均成绩的离差（离差=作业成绩-平均成绩），并对离差结果进行分析。（进阶）
#%%

#%%

#%% md
# ### 2.班级学习数据分析
# （1）分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少；   
# （2）查找成绩优秀或需要辅导的学生（top10和tail10） ；   
# （3）分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布。  
# （4）学习数据比较（进阶分析），例如分析任务参度与作业成
#%% md
# ### （1）分析所在班级每个同学课后作业的平均分，以及班级里平均分中最高和最低分别是多少；
#%% md
# ### （2）查找成绩优秀和需要辅导的学生（top10和tail10） ；
#%% md
# ### （3）分析不同分数段（如60分以下、60-69分、70-79分、80-89分、90分以上）的学生人数分布。
#%% md
# ### （4）学习数据比较，例如分析作业难易程度，即那些作业是难度高的等。（进阶）
#%%

#%%

#%%

#%% md
# ## （三）数据可视化
# 使用matplotlib库对分析结果进行可视化展示，包括但不限于以下内容：   
# （1）绘制班级成绩Top10的柱状图；     
# （2）绘制学生本人成绩的趋势图（按照作业成绩前后的折线图）；   
# （3）绘制本班级不同分数段比例图（饼图）。（如60分以下、60-69分、70-79分、80-89分、90分以上）
#%%
import matplotlib.pyplot as plt
plt.rcParams['font.family']='KaiTi'
#%% md
# ### （1）绘制班级成绩Top10的柱状图；
# 
#%% md
# ### （2）绘制学生本人成绩的趋势图（按照作业成绩前后的折线图）；
#%% md
# ### （3）绘制本班级不同分数段比例图（饼图）。（如60分以下、60-69分、70-79分、80-89分、90分以上）
#%% md
# ### （4）绘制两个班的平均成绩对比图，在同一个画布里显示每一章的平均分； （进阶）
#%%

#%%

#%%
