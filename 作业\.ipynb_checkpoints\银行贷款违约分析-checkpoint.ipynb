{"cells": [{"cell_type": "code", "execution_count": 1, "id": "408013b70e6e5ae8", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T14:58:55.984530Z", "start_time": "2025-06-20T14:58:55.971932Z"}}, "outputs": [], "source": ["# 姓名:罗婉晴\n", "# 学号:23550512032"]}, {"cell_type": "markdown", "id": "f35636759b6d6fc4", "metadata": {}, "source": ["# # 银行贷款违约风险分析"]}, {"cell_type": "markdown", "id": "cfd49e02578deefb", "metadata": {}, "source": ["# ## 数据变量解释\n", "# \n", "# | 变量名 | 变量描述 |\n", "# |--------|----------|\n", "# | 年龄   | 客户年龄 |\n", "# | 教育   | 客户教育水平，数值越大表示教育水平越高 |\n", "# | 工龄   | 客户工作年限 |\n", "# | 地址   | 客户居住地址稳定性，数值越大表示地址越稳定 |\n", "# | 收入   | 客户收入水平 |\n", "# | 负债率 | 客户负债占收入的比例 |\n", "# | 信用卡负债 | 客户信用卡负债金额 |\n", "# | 其他负债 | 客户其他类型负债金额 |\n", "# | 违约   | 是否违约，1表示违约，0表示未违约 |"]}, {"cell_type": "code", "execution_count": 1, "id": "b6df756f08fad910", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:12:20.798213Z", "start_time": "2025-06-20T07:12:20.787168Z"}}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n"]}, {"cell_type": "markdown", "id": "176252d41c60c08b", "metadata": {}, "source": ["# ## 1. 数据加载与探索性分析"]}, {"cell_type": "code", "execution_count": 2, "id": "c39524a164e9cf0d", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:13:10.724602Z", "start_time": "2025-06-20T07:13:10.698437Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据前5行：\n", "   年龄  教育  工龄  地址   收入   负债率      信用卡负债      其他负债  违约\n", "0  41   3  17  12  176   9.3  11.359392  5.008608   1\n", "1  27   1  10   6   31  17.3   1.362202  4.000798   0\n", "2  40   1  15  14   55   5.5   0.856075  2.168925   0\n", "3  41   1  15  14  120   2.9   2.658720  0.821280   0\n", "4  24   2   2   0   28  17.3   1.787436  3.056564   1\n"]}], "source": ["# 加载数据\n", "bank_data = pd.read_csv(\"bankdata.csv\")\n", "\n", "# 显示前几行数据\n", "print(\"数据前5行：\")\n", "print(bank_data.head())\n"]}, {"cell_type": "markdown", "id": "615a7144833482c7", "metadata": {}, "source": ["# ### 1.1 描述性统计分析"]}, {"cell_type": "code", "execution_count": 3, "id": "f95dfa6aeefe8cac", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:13:18.313131Z", "start_time": "2025-06-20T07:13:18.272947Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数据基本信息：\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 700 entries, 0 to 699\n", "Data columns (total 9 columns):\n", " #   Column  Non-Null Count  Dtype  \n", "---  ------  --------------  -----  \n", " 0   年龄      700 non-null    int64  \n", " 1   教育      700 non-null    int64  \n", " 2   工龄      700 non-null    int64  \n", " 3   地址      700 non-null    int64  \n", " 4   收入      700 non-null    int64  \n", " 5   负债率     700 non-null    float64\n", " 6   信用卡负债   700 non-null    float64\n", " 7   其他负债    700 non-null    float64\n", " 8   违约      700 non-null    int64  \n", "dtypes: float64(3), int64(6)\n", "memory usage: 49.3 KB\n", "\n", "数据统计描述：\n", "       count       mean        std        min        25%        50%  \\\n", "年龄     700.0  34.860000   7.997342  20.000000  29.000000  34.000000   \n", "教育     700.0   1.722857   0.928206   1.000000   1.000000   1.000000   \n", "工龄     700.0   8.388571   6.658039   0.000000   3.000000   7.000000   \n", "地址     700.0   8.278571   6.824877   0.000000   3.000000   7.000000   \n", "收入     700.0  45.601429  36.814226  14.000000  24.000000  34.000000   \n", "负债率    700.0  10.260571   6.827234   0.400000   5.000000   8.600000   \n", "信用卡负债  700.0   1.553553   2.117197   0.011696   0.369059   0.854869   \n", "其他负债   700.0   3.058209   3.287555   0.045584   1.044178   1.987567   \n", "违约     700.0   0.261429   0.439727   0.000000   0.000000   0.000000   \n", "\n", "             75%        max  \n", "年龄     40.000000   56.00000  \n", "教育      2.000000    5.00000  \n", "工龄     12.000000   31.00000  \n", "地址     12.000000   34.00000  \n", "收入     55.000000  446.00000  \n", "负债率    14.125000   41.30000  \n", "信用卡负债   1.901955   20.56131  \n", "其他负债    3.923065   27.03360  \n", "违约      1.000000    1.00000  \n", "\n", "违约情况分布：\n", "违约\n", "0    517\n", "1    183\n", "Name: count, dtype: int64\n", "违约率: 26.14%\n"]}], "source": ["# 查看数据基本信息\n", "print(\"\\n数据基本信息：\")\n", "bank_data.info()\n", "\n", "# 查看数据的统计描述\n", "print(\"\\n数据统计描述：\")\n", "print(bank_data.describe().T)\n", "\n", "# 查看目标变量分布\n", "print(\"\\n违约情况分布：\")\n", "print(bank_data['违约'].value_counts())\n", "print(f\"违约率: {bank_data['违约'].mean()*100:.2f}%\")\n"]}, {"cell_type": "markdown", "id": "c425f4591bab3995", "metadata": {}, "source": ["# **结论：**\n", "# - 数据集包含700个样本，9个变量，无缺失值\n", "# - 违约率为约26.14%\n"]}, {"cell_type": "markdown", "id": "83de1ac032f9f884", "metadata": {}, "source": ["# ### 1.2 数据可视化分析"]}, {"cell_type": "code", "execution_count": 4, "id": "d87774ab478328fd", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:14:44.404692Z", "start_time": "2025-06-20T07:14:43.927159Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 设置图形大小\n", "plt.figure(figsize=(12, 10))\n", "\n", "# 绘制违约与非违约样本数量对比图\n", "plt.subplot(2, 2, 1)\n", "sns.countplot(x='违约', data=bank_data)\n", "plt.title('违约与非违约样本数量对比')\n", "plt.xlabel('是否违约')\n", "plt.ylabel('样本数量')\n", "\n", "# 绘制年龄分布与违约关系\n", "plt.subplot(2, 2, 2)\n", "sns.boxplot(x='违约', y='年龄', data=bank_data)\n", "plt.title('年龄与违约关系')\n", "plt.xlabel('是否违约')\n", "plt.ylabel('年龄')\n", "\n", "# 绘制收入分布与违约关系\n", "plt.subplot(2, 2, 3)\n", "sns.boxplot(x='违约', y='收入', data=bank_data)\n", "plt.title('收入与违约关系')\n", "plt.xlabel('是否违约')\n", "plt.ylabel('收入')\n", "\n", "# 绘制负债率分布与违约关系\n", "plt.subplot(2, 2, 4)\n", "sns.boxplot(x='违约', y='负债率', data=bank_data)\n", "plt.title('负债率与违约关系')\n", "plt.xlabel('是否违约')\n", "plt.ylabel('负债率')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "597c703a7cf5032e", "metadata": {}, "source": ["# **结论：**\n", "# - 违约样本占比较少，约为26%，数据存在不平衡\n", "# - 年龄与违约关系不明显，但违约客户的年龄中位数略低于非违约客户\n", "# - 违约客户的收入中位数略低于非违约客户\n", "# - 违约客户的负债率明显高于非违约客户，表明负债率可能是违约的重要影响因素\n"]}, {"cell_type": "markdown", "id": "ead355167e67d2c9", "metadata": {}, "source": ["# ### 1.3 变量相关性分析"]}, {"cell_type": "code", "execution_count": 5, "id": "b28ab7fa574b63f", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:15:38.583576Z", "start_time": "2025-06-20T07:15:38.289556Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x800 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "各变量与违约的相关性：\n", "违约       1.000000\n", "负债率      0.389575\n", "信用卡负债    0.244740\n", "其他负债     0.145713\n", "教育       0.114676\n", "收入      -0.070970\n", "年龄      -0.137657\n", "地址      -0.164451\n", "工龄      -0.282978\n", "Name: 违约, dtype: float64\n"]}], "source": ["# 计算相关系数矩阵\n", "corr_matrix = bank_data.corr()\n", "\n", "# 绘制热力图\n", "plt.figure(figsize=(10, 8))\n", "sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt='.2f')\n", "plt.title('变量相关性热力图')\n", "plt.show()\n", "\n", "# 各变量与违约的相关性\n", "print(\"\\n各变量与违约的相关性：\")\n", "print(corr_matrix['违约'].sort_values(ascending=False))\n"]}, {"cell_type": "markdown", "id": "7fc5dcbb2f838ee5", "metadata": {}, "source": ["# **结论：**\n", "# - 负债率与违约呈正相关，相关系数较高，说明负债率越高，违约风险越大\n", "# - 信用卡负债和其他负债与违约也有一定正相关性\n", "# - 收入与违约呈负相关，说明收入越高，违约风险越低\n"]}, {"cell_type": "markdown", "id": "9a2bed761c5e0b1a", "metadata": {}, "source": ["# ## 2. 数据预处理"]}, {"cell_type": "markdown", "id": "c6af77ee62b1d5f9", "metadata": {}, "source": ["# ### 2.1 检查缺失值"]}, {"cell_type": "code", "execution_count": 5, "id": "4cb1a6c010f75c55", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:16:06.722507Z", "start_time": "2025-06-20T07:16:06.710546Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["缺失值检查：\n", "年龄       0\n", "教育       0\n", "工龄       0\n", "地址       0\n", "收入       0\n", "负债率      0\n", "信用卡负债    0\n", "其他负债     0\n", "违约       0\n", "dtype: int64\n"]}], "source": ["# 检查缺失值\n", "print(\"缺失值检查：\")\n", "print(bank_data.isnull().sum())\n"]}, {"cell_type": "markdown", "id": "42ec581931b042a3", "metadata": {}, "source": ["# **结论：** 数据集中没有缺失值，不需要进行缺失值处理。\n"]}, {"cell_type": "markdown", "id": "a2111b03d0d35fc4", "metadata": {}, "source": ["# ### 2.2 特征工程"]}, {"cell_type": "code", "execution_count": 6, "id": "eabf020580d80752", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:16:16.201817Z", "start_time": "2025-06-20T07:16:16.175305Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["创建新特征后的数据：\n", "   年龄  教育  工龄  地址   收入   负债率      信用卡负债      其他负债  违约     总负债\n", "0  41   3  17  12  176   9.3  11.359392  5.008608   1  16.368\n", "1  27   1  10   6   31  17.3   1.362202  4.000798   0   5.363\n", "2  40   1  15  14   55   5.5   0.856075  2.168925   0   3.025\n", "3  41   1  15  14  120   2.9   2.658720  0.821280   0   3.480\n", "4  24   2   2   0   28  17.3   1.787436  3.056564   1   4.844\n", "创建收入负债比特征后的数据：\n", "   年龄  教育  工龄  地址   收入   负债率      信用卡负债      其他负债  违约     总负债  收入负债比\n", "0  41   3  17  12  176   9.3  11.359392  5.008608   1  16.368  0.093\n", "1  27   1  10   6   31  17.3   1.362202  4.000798   0   5.363  0.173\n", "2  40   1  15  14   55   5.5   0.856075  2.168925   0   3.025  0.055\n", "3  41   1  15  14  120   2.9   2.658720  0.821280   0   3.480  0.029\n", "4  24   2   2   0   28  17.3   1.787436  3.056564   1   4.844  0.173\n", "创建信用卡负债占比特征后的数据：\n", "   年龄  教育  工龄  地址   收入   负债率      信用卡负债      其他负债  违约     总负债  收入负债比  信用卡负债占比\n", "0  41   3  17  12  176   9.3  11.359392  5.008608   1  16.368  0.093    0.694\n", "1  27   1  10   6   31  17.3   1.362202  4.000798   0   5.363  0.173    0.254\n", "2  40   1  15  14   55   5.5   0.856075  2.168925   0   3.025  0.055    0.283\n", "3  41   1  15  14  120   2.9   2.658720  0.821280   0   3.480  0.029    0.764\n", "4  24   2   2   0   28  17.3   1.787436  3.056564   1   4.844  0.173    0.369\n"]}], "source": ["# 创建新特征：总负债\n", "bank_data['总负债'] = bank_data['信用卡负债'] + bank_data['其他负债']\n", "print(\"创建新特征后的数据：\")\n", "print(bank_data.head())\n", "\n", "# 创建新特征：收入负债比\n", "bank_data['收入负债比'] = bank_data['总负债'] / bank_data['收入']\n", "print(\"创建收入负债比特征后的数据：\")\n", "print(bank_data.head())\n", "\n", "# 创建新特征：信用卡负债占比\n", "bank_data['信用卡负债占比'] = bank_data['信用卡负债'] / bank_data['总负债']\n", "# 处理可能的除零情况\n", "bank_data['信用卡负债占比'] = bank_data['信用卡负债占比'].fillna(0)\n", "print(\"创建信用卡负债占比特征后的数据：\")\n", "print(bank_data.head())\n"]}, {"cell_type": "markdown", "id": "efa8b3b05e02d580", "metadata": {}, "source": ["# **结论：**\n", "# - 创建了三个新特征：总负债、收入负债比和信用卡负债占比\n", "# - 这些新特征可能会对违约预测提供额外的信息\n"]}, {"cell_type": "markdown", "id": "100dd24a72ad0db4", "metadata": {}, "source": ["# ## 3. 建立逻辑回归模型"]}, {"cell_type": "markdown", "id": "9eeefe949a93d629", "metadata": {}, "source": ["# ### 3.1 准备训练和测试数据"]}, {"cell_type": "code", "execution_count": 7, "id": "a56d2c5038661716", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:16:28.878727Z", "start_time": "2025-06-20T07:16:28.713748Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集大小: (490, 9)\n", "测试集大小: (210, 9)\n"]}], "source": ["# 选择特征变量和目标变量\n", "X = bank_data[['年龄', '教育', '工龄', '地址', '收入', '负债率', '总负债', '收入负债比', '信用卡负债占比']]\n", "y = bank_data['违约']\n", "\n", "# 数据标准化\n", "from sklearn.preprocessing import StandardScaler\n", "scaler = StandardScaler()\n", "X_scaled = scaler.fit_transform(X)\n", "X_scaled = pd.DataFrame(X_scaled, columns=X.columns)\n", "\n", "# 划分训练集和测试集\n", "from sklearn.model_selection import train_test_split\n", "X_train, X_test, y_train, y_test = train_test_split(X_scaled, y, test_size=0.3, random_state=42)\n", "\n", "print(\"训练集大小:\", X_train.shape)\n", "print(\"测试集大小:\", X_test.shape)\n"]}, {"cell_type": "markdown", "id": "f50dacb1a56985fc", "metadata": {}, "source": ["# ### 3.2 建立基础逻辑回归模型"]}, {"cell_type": "code", "execution_count": 8, "id": "7f9c7a8f0eed6c60", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:16:33.952372Z", "start_time": "2025-06-20T07:16:33.731260Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型系数：\n", "        特征        系数\n", "6      总负债  0.995230\n", "8  信用卡负债占比  0.468648\n", "0       年龄  0.282677\n", "7    收入负债比  0.214726\n", "5      负债率  0.214726\n", "1       教育  0.007596\n", "4       收入 -0.021344\n", "3       地址 -0.587918\n", "2       工龄 -1.594953\n", "\n", "模型评估报告：\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.88      0.93      0.90       161\n", "           1       0.72      0.57      0.64        49\n", "\n", "    accuracy                           0.85       210\n", "   macro avg       0.80      0.75      0.77       210\n", "weighted avg       0.84      0.85      0.84       210\n", "\n", "\n", "混淆矩阵：\n", "[[150  11]\n", " [ 21  28]]\n", "\n", "模型准确率: 0.8476\n"]}], "source": ["from sklearn.linear_model import LogisticRegression\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc\n", "\n", "# 创建逻辑回归模型\n", "model1 = LogisticRegression(random_state=42)\n", "model1.fit(X_train, y_train)\n", "\n", "# 在测试集上进行预测\n", "y_pred1 = model1.predict(X_test)\n", "y_prob1 = model1.predict_proba(X_test)[:, 1]\n", "\n", "# 输出模型系数\n", "coef_df = pd.DataFrame({\n", "    '特征': X.columns,\n", "    '系数': model1.coef_[0]\n", "})\n", "print(\"模型系数：\")\n", "print(coef_df.sort_values(by='系数', ascending=False))\n", "\n", "# 计算并输出模型评估指标\n", "print(\"\\n模型评估报告：\")\n", "print(classification_report(y_test, y_pred1))\n", "\n", "# 混淆矩阵\n", "conf_matrix = confusion_matrix(y_test, y_pred1)\n", "print(\"\\n混淆矩阵：\")\n", "print(conf_matrix)\n", "\n", "# 计算准确率\n", "accuracy1 = model1.score(X_test, y_test)\n", "print(f\"\\n模型准确率: {accuracy1:.4f}\")\n"]}, {"cell_type": "markdown", "id": "3516d955b318ea37", "metadata": {}, "source": ["# **结论：**\n", "# - 负债率、收入负债比和信用卡负债占比对违约预测有较大的正向影响\n", "# - 收入和教育水平对违约预测有较大的负向影响\n", "# - 模型在测试集上的准确率为约84.76%\n"]}, {"cell_type": "markdown", "id": "322296e15a88dd11", "metadata": {}, "source": ["# ### 3.3 绘制ROC曲线"]}, {"cell_type": "code", "execution_count": 9, "id": "5e838c0733681b43", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:17:20.156589Z", "start_time": "2025-06-20T07:17:20.012672Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 计算ROC曲线\n", "fpr1, tpr1, _ = roc_curve(y_test, y_prob1)\n", "roc_auc1 = auc(fpr1, tpr1)\n", "\n", "# 绘制ROC曲线\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(fpr1, tpr1, color='blue', lw=2, label=f'基础模型 ROC曲线 (AUC = {roc_auc1:.2f})')\n", "plt.plot([0, 1], [0, 1], color='gray', linestyle='--')\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('假正例率 (FPR)')\n", "plt.ylabel('真正例率 (TPR)')\n", "plt.title('基础逻辑回归模型ROC曲线')\n", "plt.legend(loc=\"lower right\")\n", "plt.show()\n"]}, {"cell_type": "markdown", "id": "4d238b82e62a9a2d", "metadata": {}, "source": ["# **结论：**\n", "# - 基础模型的AUC值为约0.88，表明模型有较好的区分能力\n", "# - ROC曲线明显优于随机猜测（对角线）\n"]}, {"cell_type": "code", "execution_count": 10, "id": "636bbafb28137db2", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:22:31.617974Z", "start_time": "2025-06-20T07:22:31.579006Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["改进模型系数：\n", "        特征        系数\n", "6      总负债  1.007934\n", "8  信用卡负债占比  0.485886\n", "0       年龄  0.285528\n", "7    收入负债比  0.221050\n", "5      负债率  0.221050\n", "1       教育  0.028862\n", "4       收入 -0.057478\n", "3       地址 -0.571315\n", "2       工龄 -1.624886\n", "\n", "改进模型评估报告：\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.92      0.76      0.83       161\n", "           1       0.50      0.80      0.61        49\n", "\n", "    accuracy                           0.77       210\n", "   macro avg       0.71      0.78      0.72       210\n", "weighted avg       0.83      0.77      0.78       210\n", "\n", "\n", "改进模型混淆矩阵：\n", "[[122  39]\n", " [ 10  39]]\n", "\n", "改进模型准确率: 0.7667\n"]}], "source": ["# 创建带类别权重的逻辑回归模型\n", "model2 = LogisticRegression(class_weight='balanced', random_state=42)\n", "model2.fit(X_train, y_train)\n", "\n", "# 在测试集上进行预测\n", "y_pred2 = model2.predict(X_test)\n", "y_prob2 = model2.predict_proba(X_test)[:, 1]\n", "\n", "# 输出模型系数\n", "coef_df2 = pd.DataFrame({\n", "    '特征': X.columns,\n", "    '系数': model2.coef_[0]\n", "})\n", "print(\"改进模型系数：\")\n", "print(coef_df2.sort_values(by='系数', ascending=False))\n", "\n", "# 计算并输出模型评估指标\n", "print(\"\\n改进模型评估报告：\")\n", "print(classification_report(y_test, y_pred2))\n", "\n", "# 混淆矩阵\n", "conf_matrix2 = confusion_matrix(y_test, y_pred2)\n", "print(\"\\n改进模型混淆矩阵：\")\n", "print(conf_matrix2)\n", "\n", "# 计算准确率\n", "accuracy2 = model2.score(X_test, y_test)\n", "print(f\"\\n改进模型准确率: {accuracy2:.4f}\")\n", "\n", "# 计算ROC曲线\n", "fpr2, tpr2, _ = roc_curve(y_test, y_prob2)\n", "roc_auc2 = auc(fpr2, tpr2)\n"]}, {"cell_type": "markdown", "id": "5d61dd0ca7f25b44", "metadata": {}, "source": ["# ### 4.2 比较两个模型的ROC曲线"]}, {"cell_type": "code", "execution_count": 11, "id": "a7dfc0890247f1f6", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:22:36.533107Z", "start_time": "2025-06-20T07:22:36.304743Z"}}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 800x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# 绘制两个模型的ROC曲线\n", "plt.figure(figsize=(8, 6))\n", "plt.plot(fpr1, tpr1, color='blue', lw=2, label=f'基础模型 (AUC = {roc_auc1:.2f})')\n", "plt.plot(fpr2, tpr2, color='red', lw=2, label=f'平衡模型 (AUC = {roc_auc2:.2f})')\n", "plt.plot([0, 1], [0, 1], color='gray', linestyle='--')\n", "plt.xlim([0.0, 1.0])\n", "plt.ylim([0.0, 1.05])\n", "plt.xlabel('假正例率 (FPR)')\n", "plt.ylabel('真正例率 (TPR)')\n", "plt.title('两个模型ROC曲线比较')\n", "plt.legend(loc=\"lower right\")\n", "plt.show()\n"]}, {"cell_type": "code", "execution_count": 12, "id": "5dc28f69be61d1ea", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:27:49.952461Z", "start_time": "2025-06-20T07:27:49.924394Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["模型性能比较：\n", "基础模型准确率: 0.8476, AUC: 0.8824\n", "平衡模型准确率: 0.7667, AUC: 0.8821\n", "基础模型违约样本召回率: 0.5714\n", "平衡模型违约样本召回率: 0.7959\n", "基础模型违约样本精确率: 0.7179\n", "平衡模型违约样本精确率: 0.5000\n"]}], "source": ["# 比较两个模型的性能\n", "print(\"模型性能比较：\")\n", "print(f\"基础模型准确率: {accuracy1:.4f}, AUC: {roc_auc1:.4f}\")\n", "print(f\"平衡模型准确率: {accuracy2:.4f}, AUC: {roc_auc2:.4f}\")\n", "\n", "# 比较两个模型的召回率\n", "from sklearn.metrics import recall_score\n", "recall1 = recall_score(y_test, y_pred1)\n", "recall2 = recall_score(y_test, y_pred2)\n", "\n", "print(f\"基础模型违约样本召回率: {recall1:.4f}\")\n", "print(f\"平衡模型违约样本召回率: {recall2:.4f}\")\n", "\n", "# 比较两个模型的精确率\n", "from sklearn.metrics import precision_score\n", "precision1 = precision_score(y_test, y_pred1)\n", "precision2 = precision_score(y_test, y_pred2)\n", "\n", "print(f\"基础模型违约样本精确率: {precision1:.4f}\")\n", "print(f\"平衡模型违约样本精确率: {precision2:.4f}\")\n"]}, {"cell_type": "markdown", "id": "1117545e18e1281f", "metadata": {}, "source": ["# **结论：**\n", "# - 基础模型的准确率略高，但对违约样本的召回率较低\n", "# - 平衡模型的召回率明显提高，能更好地识别出违约风险高的客户\n", "# - 平衡模型的精确率略低，意味着会将一些非违约客户错误地分类为违约\n", "# - 在银行贷款违约风险控制中，漏掉高风险客户的代价通常高于错误地拒绝低风险客户，因此平衡模型可能更适合实际应用\n"]}, {"cell_type": "markdown", "id": "6aca0a8809798319", "metadata": {}, "source": ["# ## 7. 模型应用与风险预测"]}, {"cell_type": "code", "execution_count": 18, "id": "d3e462531bf01d7d", "metadata": {"ExecuteTime": {"end_time": "2025-06-20T07:28:34.211526Z", "start_time": "2025-06-20T07:28:34.168170Z"}}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["示例客户违约概率 (基础模型): 0.7877\n", "示例客户违约概率 (平衡模型): 0.9099\n", "阈值 0.3: 基础模型预测 违约, 平衡模型预测 违约\n", "阈值 0.4: 基础模型预测 违约, 平衡模型预测 违约\n", "阈值 0.5: 基础模型预测 违约, 平衡模型预测 违约\n"]}, {"name": "stderr", "output_type": "stream", "text": ["H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but LogisticRegression was fitted with feature names\n", "  warnings.warn(\n", "H:\\yolo疲劳驾驶检测\\.venv\\lib\\site-packages\\sklearn\\utils\\validation.py:2739: UserWarning: X does not have valid feature names, but LogisticRegression was fitted with feature names\n", "  warnings.warn(\n"]}], "source": ["# 创建一个示例客户数据\n", "example_customer = pd.DataFrame({\n", "    '年龄': [30],\n", "    '教育': [2],\n", "    '工龄': [5],\n", "    '地址': [3],\n", "    '收入': [40],\n", "    '负债率': [20],\n", "    '总负债': [8],\n", "    '收入负债比': [0.2],\n", "    '信用卡负债占比': [0.5]\n", "})\n", "\n", "# 标准化示例数据\n", "example_customer_scaled = scaler.transform(example_customer)\n", "\n", "# 使用两个模型进行预测\n", "prob1 = model1.predict_proba(example_customer_scaled)[0, 1]\n", "prob2 = model2.predict_proba(example_customer_scaled)[0, 1]\n", "\n", "print(f\"示例客户违约概率 (基础模型): {prob1:.4f}\")\n", "print(f\"示例客户违约概率 (平衡模型): {prob2:.4f}\")\n", "\n", "# 根据不同阈值进行决策\n", "thresholds = [0.3, 0.4, 0.5]\n", "for threshold in thresholds:\n", "    decision1 = \"违约\" if prob1 > threshold else \"不违约\"\n", "    decision2 = \"违约\" if prob2 > threshold else \"不违约\"\n", "    print(f\"阈值 {threshold}: 基础模型预测 {decision1}, 平衡模型预测 {decision2}\")\n"]}, {"cell_type": "markdown", "id": "55d5fbedb85a4de1", "metadata": {}, "source": ["# **结论：**\n", "# - 示例客户在基础模型中的违约概率为约0.7877\n", "# - 示例客户在平衡模型中的违约概率为约0.9099\n", "# - 不同的决策阈值会导致不同的贷款决策\n", "# - 银行可以根据自身的风险偏好和成本-收益分析来选择适当的阈值\n"]}, {"cell_type": "markdown", "id": "de2aea77fb205567", "metadata": {}, "source": ["# ## 8. 总结与建议\n", "# \n", "# **模型结果总结：**\n", "# \n", "# 1. 数据分析显示，负债率、收入和教育水平是影响客户违约的重要因素\n", "# 2. 创建的新特征（总负债、收入负债比和信用卡负债占比）对模型有显著贡献\n", "# 3. 基础模型准确率较高，但对违约样本的识别能力有限\n", "# 4. 平衡模型提高了对违约样本的识别能力，但可能会增加误判率\n", "# \n", "# **业务建议：**\n", "# \n", "# 1. 重点关注客户的负债率和收入负债比，这是预测违约的关键指标\n", "# 2. 对于教育水平较低、收入较低且负债率高的客户，应采取更谨慎的贷款策略\n", "# 3. 可以根据业务需求选择合适的模型：\n", "#    - 如果银行更关注整体准确率，可以选择基础模型\n", "#    - 如果银行更关注识别高风险客户，应选择平衡模型\n", "# 4. 建议使用平衡模型，并根据银行的风险承受能力设置合适的概率阈值\n", "# \n", "# **模型改进方向：**\n", "# \n", "# 1. 收集更多的客户特征，如信用记录、职业稳定性等\n", "# 2. 尝试更复杂的模型，如随机森林、梯度提升树等\n", "# 3. 考虑特征之间的交互作用，创建更有意义的交互特征 "]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.9"}}, "nbformat": 4, "nbformat_minor": 5}