{"cells": [{"metadata": {}, "cell_type": "markdown", "source": "# # 银行客户流失分析", "id": "efca28a6a2d614af"}, {"metadata": {}, "cell_type": "markdown", "source": ["# ## 数据变量解释\n", "# \n", "# | 变量名               | 变量描述                                |\n", "# |---------------------|----------------------------------------|\n", "# | RowNumber           | 行号（无实际意义）                       |\n", "# | CustomerId          | 客户ID                                  |\n", "# | Surname             | 客户姓氏                                |\n", "# | CreditScore         | 信用评分                                |\n", "# | Geography           | 客户所在国家/地区                        |\n", "# | Gender              | 性别                                    |\n", "# | Age                 | 年龄                                    |\n", "# | Tenure              | 客户在银行的存续时间（年）                |\n", "# | Balance             | 账户余额                                |\n", "# | NumOfProducts       | 客户使用的银行产品数量                   |\n", "# | HasCrCard           | 是否有信用卡（1=有，0=无）               |\n", "# | IsActiveMember      | 是否为活跃会员（1=是，0=否）             |\n", "# | EstimatedSalary     | 估计薪资                                |\n", "# | Exited              | 是否已流失（1=是，0=否）                 |\n", "# | Complain            | 是否有投诉（1=是，0=否）                 |\n", "# | Satisfaction Score  | 满意度评分（1-5分）                      |\n", "# | Card Type           | 卡类型                                  |\n", "# | Point Earned        | 积分                                    |"], "id": "7fdf8d0af8eced39"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:38:38.906705Z", "start_time": "2025-06-20T08:38:38.893002Z"}}, "cell_type": "code", "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "\n", "# 设置中文显示\n", "plt.rcParams['font.sans-serif'] = ['SimHei']  # 用来正常显示中文标签\n", "plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号\n"], "id": "84f08dc8e553c021", "outputs": [], "execution_count": 1}, {"metadata": {}, "cell_type": "markdown", "source": "# ## 1. 数据加载与探索性分析", "id": "ef3d83cddd64d093"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:38:41.113903Z", "start_time": "2025-06-20T08:38:41.080111Z"}}, "cell_type": "code", "source": ["# 加载数据\n", "churn_data = pd.read_csv(\"Customer-Churn-Records.csv\")\n", "\n", "# 显示前几行数据\n", "print(\"数据前5行：\")\n", "print(churn_data.head())\n"], "id": "1a27aa8957e2257e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["数据前5行：\n", "   RowNumber  CustomerId   Surname  CreditScore Geography  Gender  Age  \\\n", "0          1    15634602  <PERSON><PERSON><PERSON>          619    France  Female   42   \n", "1          2    15647311      Hill          608     Spain  Female   41   \n", "2          3    15619304      Onio          502    France  Female   42   \n", "3          4    15701354      <PERSON><PERSON>          699    France  Female   39   \n", "4          5    15737888  <PERSON>          850     Spain  Female   43   \n", "\n", "   Tenure    Balance  NumOfProducts  HasCrCard  IsActiveMember  \\\n", "0       2       0.00              1          1               1   \n", "1       1   83807.86              1          0               1   \n", "2       8  159660.80              3          1               0   \n", "3       1       0.00              2          0               0   \n", "4       2  125510.82              1          1               1   \n", "\n", "   EstimatedSalary  Exited  Complain  Satisfaction Score Card Type  \\\n", "0        101348.88       1         1                   2   DIAMOND   \n", "1        112542.58       0         1                   3   DIAMOND   \n", "2        113931.57       1         1                   3   DIAMOND   \n", "3         93826.63       0         0                   5      GOLD   \n", "4         79084.10       0         0                   5      GOLD   \n", "\n", "   Point Earned  \n", "0           464  \n", "1           456  \n", "2           377  \n", "3           350  \n", "4           425  \n"]}], "execution_count": 2}, {"metadata": {}, "cell_type": "markdown", "source": "# ### 1.1 描述性统计分析", "id": "b92834ee5203577b"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:38:45.041495Z", "start_time": "2025-06-20T08:38:45.001845Z"}}, "cell_type": "code", "source": ["# 查看数据基本信息\n", "print(\"\\n数据基本信息：\")\n", "churn_data.info()\n", "\n", "# 查看数据的统计描述\n", "print(\"\\n数据统计描述：\")\n", "print(churn_data.describe().T)\n", "\n", "# 查看目标变量分布\n", "print(\"\\n流失情况分布：\")\n", "print(churn_data['Exited'].value_counts())\n", "print(f\"流失率: {churn_data['Exited'].mean()*100:.2f}%\")\n"], "id": "6012d9e66c97b24f", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "数据基本信息：\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 10000 entries, 0 to 9999\n", "Data columns (total 18 columns):\n", " #   Column              Non-Null Count  Dtype  \n", "---  ------              --------------  -----  \n", " 0   RowNumber           10000 non-null  int64  \n", " 1   CustomerId          10000 non-null  int64  \n", " 2   Surname             10000 non-null  object \n", " 3   CreditScore         10000 non-null  int64  \n", " 4   Geography           10000 non-null  object \n", " 5   Gender              10000 non-null  object \n", " 6   Age                 10000 non-null  int64  \n", " 7   Tenure              10000 non-null  int64  \n", " 8   Balance             10000 non-null  float64\n", " 9   NumOfProducts       10000 non-null  int64  \n", " 10  HasCrCard           10000 non-null  int64  \n", " 11  IsActiveMember      10000 non-null  int64  \n", " 12  EstimatedSalary     10000 non-null  float64\n", " 13  Exited              10000 non-null  int64  \n", " 14  <PERSON>mp<PERSON>            10000 non-null  int64  \n", " 15  Satisfaction Score  10000 non-null  int64  \n", " 16  Card Type           10000 non-null  object \n", " 17  Point Earned        10000 non-null  int64  \n", "dtypes: float64(2), int64(12), object(4)\n", "memory usage: 1.4+ MB\n", "\n", "数据统计描述：\n", "                      count          mean           std          min  \\\n", "RowNumber           10000.0  5.000500e+03   2886.895680         1.00   \n", "CustomerId          10000.0  1.569094e+07  71936.186123  15565701.00   \n", "CreditScore         10000.0  6.505288e+02     96.653299       350.00   \n", "Age                 10000.0  3.892180e+01     10.487806        18.00   \n", "Tenure              10000.0  5.012800e+00      2.892174         0.00   \n", "Balance             10000.0  7.648589e+04  62397.405202         0.00   \n", "NumOfProducts       10000.0  1.530200e+00      0.581654         1.00   \n", "HasCrCard           10000.0  7.055000e-01      0.455840         0.00   \n", "IsActiveMember      10000.0  5.151000e-01      0.499797         0.00   \n", "EstimatedSalary     10000.0  1.000902e+05  57510.492818        11.58   \n", "Exited              10000.0  2.038000e-01      0.402842         0.00   \n", "Complain            10000.0  2.044000e-01      0.403283         0.00   \n", "Satisfaction Score  10000.0  3.013800e+00      1.405919         1.00   \n", "Point Earned        10000.0  6.065151e+02    225.924839       119.00   \n", "\n", "                            25%           50%           75%          max  \n", "RowNumber               2500.75  5.000500e+03  7.500250e+03     10000.00  \n", "CustomerId          15628528.25  1.569074e+07  1.575323e+07  15815690.00  \n", "CreditScore              584.00  6.520000e+02  7.180000e+02       850.00  \n", "Age                       32.00  3.700000e+01  4.400000e+01        92.00  \n", "Tenure                     3.00  5.000000e+00  7.000000e+00        10.00  \n", "Balance                    0.00  9.719854e+04  1.276442e+05    250898.09  \n", "NumOfProducts              1.00  1.000000e+00  2.000000e+00         4.00  \n", "HasCrCard                  0.00  1.000000e+00  1.000000e+00         1.00  \n", "IsActiveMember             0.00  1.000000e+00  1.000000e+00         1.00  \n", "EstimatedSalary        51002.11  1.001939e+05  1.493882e+05    199992.48  \n", "Exited                     0.00  0.000000e+00  0.000000e+00         1.00  \n", "Complain                   0.00  0.000000e+00  0.000000e+00         1.00  \n", "Satisfaction Score         2.00  3.000000e+00  4.000000e+00         5.00  \n", "Point Earned             410.00  6.050000e+02  8.010000e+02      1000.00  \n", "\n", "流失情况分布：\n", "Exited\n", "0    7962\n", "1    2038\n", "Name: count, dtype: int64\n", "流失率: 20.38%\n"]}], "execution_count": 3}, {"metadata": {}, "cell_type": "markdown", "source": ["# **结论：**\n", "# - 数据集包含10,000个客户样本和18个变量\n", "# - 客户流失率约为20.38%，表明数据存在一定的不平衡性\n"], "id": "6c36890979237671"}, {"metadata": {}, "cell_type": "markdown", "source": "# ### 1.2 类别变量分析", "id": "aac06857ba39906a"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:39:34.039374Z", "start_time": "2025-06-20T08:39:33.687994Z"}}, "cell_type": "code", "source": ["# 分析地理分布\n", "plt.figure(figsize=(12, 5))\n", "\n", "plt.subplot(1, 2, 1)\n", "geo_counts = churn_data['Geography'].value_counts()\n", "plt.pie(geo_counts, labels=geo_counts.index, autopct='%1.1f%%', startangle=90)\n", "plt.title('客户地理分布')\n", "\n", "# 分析性别分布\n", "plt.subplot(1, 2, 2)\n", "gender_counts = churn_data['Gender'].value_counts()\n", "plt.pie(gender_counts, labels=gender_counts.index, autopct='%1.1f%%', startangle=90)\n", "plt.title('客户性别分布')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 分析卡类型分布\n", "plt.figure(figsize=(10, 6))\n", "card_counts = churn_data['Card Type'].value_counts()\n", "sns.barplot(x=card_counts.index, y=card_counts.values)\n", "plt.title('卡类型分布')\n", "plt.xticks(rotation=45)\n", "plt.ylabel('客户数量')\n", "plt.show()\n", "\n", "# 分析满意度评分分布\n", "plt.figure(figsize=(8, 5))\n", "sat_counts = churn_data['Satisfaction Score'].value_counts().sort_index()\n", "sns.barplot(x=sat_counts.index, y=sat_counts.values)\n", "plt.title('客户满意度评分分布')\n", "plt.xlabel('满意度评分')\n", "plt.ylabel('客户数量')\n", "plt.show()\n"], "id": "916909f92fc53697", "outputs": [{"data": {"text/plain": ["<Figure size 1200x500 with 2 Axes>"], "image/png": "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**********************************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"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 800x500 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 4}, {"metadata": {}, "cell_type": "markdown", "source": ["# **结论：**\n", "# - 客户主要分布在三个国家/地区，其中法国客户占比最高\n", "# - 男女客户比例基本平衡，略有差异\n", "# - 客户满意度评分分布较为均匀，评分3分的客户数量最多\n"], "id": "1fc22ad311fd3dd7"}, {"metadata": {}, "cell_type": "markdown", "source": "# ### 1.3 流失与非流失客户特征对比", "id": "2e8f870fc1e0eb56"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:40:21.918309Z", "start_time": "2025-06-20T08:40:21.478909Z"}}, "cell_type": "code", "source": ["# 设置图形大小\n", "plt.figure(figsize=(16, 12))\n", "\n", "# 分析年龄与流失关系\n", "plt.subplot(2, 2, 1)\n", "sns.boxplot(x='Exited', y='Age', data=churn_data)\n", "plt.title('年龄与客户流失关系')\n", "plt.xlabel('是否流失')\n", "plt.ylabel('年龄')\n", "\n", "# 分析信用评分与流失关系\n", "plt.subplot(2, 2, 2)\n", "sns.boxplot(x='Exited', y='CreditScore', data=churn_data)\n", "plt.title('信用评分与客户流失关系')\n", "plt.xlabel('是否流失')\n", "plt.ylabel('信用评分')\n", "\n", "# 分析账户余额与流失关系\n", "plt.subplot(2, 2, 3)\n", "sns.boxplot(x='Exited', y='Balance', data=churn_data)\n", "plt.title('账户余额与客户流失关系')\n", "plt.xlabel('是否流失')\n", "plt.ylabel('账户余额')\n", "\n", "# 分析存续时间与流失关系\n", "plt.subplot(2, 2, 4)\n", "sns.boxplot(x='Exited', y='Tenure', data=churn_data)\n", "plt.title('客户存续时间与流失关系')\n", "plt.xlabel('是否流失')\n", "plt.ylabel('存续时间（年）')\n", "\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "6b6cb8f6312eea25", "outputs": [{"data": {"text/plain": ["<Figure size 1600x1200 with 4 Axes>"], "image/png": "iVBORw0KGgoAAAANSUhEUgAABjUAAASlCAYAAADgeltjAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjMsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvZiW1igAAAAlwSFlzAAAPYQAAD2EBqD+naQAA6f9JREFUeJzs3Qm4lGXZOPCbwyYiIrK5AK6JiUqlhKK5l5m7hKnlluWuKZpJi6bS51IuueOSKam5oCmW9TcrzVxzadFwT9AUEGRVRIX/db99c745hwOCwpkzZ36/65rrnXc5c54ZjuNzv/dzP0+b+fPnzw8AAAAAAIAWrq7SDQAAAAAAAFgckhoAAAAAAEBVkNQAAAAAAACqgqQGAAAAAABQFSQ1AAAAAACAqiCpAQAAAAAAVAVJDQAAAAAAoCpIagAAAAAAAFVBUgOABUyZMiWq1QcffBBvvfXWUnmd1ujNN9+sdBMAAKBVWBpx0/z582PevHnR2kydOrVVvi+gZZDUAKjyG9Q33njjR75R/ZnPfCb++c9/LnD8gAMOiP/5n//50J8/55xz4q9//WssLXfeeWeMGDHiY73GcccdF9///vcX69oXXnihyeOzZs2KnXfeOf71r399pDaMHTs2brnlliX+uYcffrjo/JfL/V/96leLTLJksDB37tyYPXt2kdAZP358/OMf/4j777+/+PvIf6fDDz88Nt9889hqq63i0Ucf/UjvCwCA5jVnzpyiX9dY9v///ve/L/Jns284Y8aMxXpk/7ex7EtefvnlDfqlL774YkyYMCFeffXVBo88lucmTpy40PaUn8v+61133VX8zOK4/vrriz7x0vK3v/0tTjrppKLtH9Xo0aNj9913L/rhHyY/n3feeWeh8Uu+v4/iqaeeiosuumix2lAu33dTfz833HBDk38L5QmY/F1vv/12TJ8+PV577bUiZnrooYfitttuiwsvvDCGDx8eO+ywQxF7jBo16iO9L4AP0+5DrwCgxcqOdAYanTp1ij322GOxfuaxxx6Ld999N7bccsto27ZtdO7cuehgd+jQodjPoCaDi7XXXnuRr5M/c/XVV8d6660Xm2666RK1Ozu/GaA1lWTIzvD2228fa6211gLne/fuHSussMIiX3uXXXaJb33rW0Xg0Ldv34VeN23atBg2bFh8+tOfjrPOOitWWmmlopPevn374nfk8R/+8Ifxy1/+Mtq0aVOce++994qfzc+qXH5e+TPZvvTggw/Gf/7zn+L1F1cmLU455ZRYc80144orrqg//vvf/z5+8IMfRK9evWLIkCFN/tw222wTkyZNqj/WsWPHItgYMGBA9OvXr2jXZpttFrvuumvxPldeeeXFbhcAAJVz3333xbHHHlvcbN5kk03qj//mN7+J6667Lu65556ir9qUo446qrjZvDiy7/3b3/62wbHsr+dN6uz7fv3rXy8GIJ155pnRrl274ndmYiIfuZ995eyX7rfffkWftilHHHFErLvuusWAm7q6uvjZz35W/N5zzz33Q9t3++23xyc+8YnFjnnKEzFNVXFPnjw57rjjjlh11VVjt912W+D8iiuuGD179lzka3/xi18s4ohMzuy1116LvPbb3/520ZZ87xtuuGERV5Tir+zL/+hHPyper/Q733///eKx3HLLNXidfC8ZZ2Q/P/373/8u4sGjjz46lsQll1wSf/nLX4p/04wPSq912mmnFXFFJlqakgmL/Nsrydgp/+3zc+zfv38Rd6y//vrFQKqMObp06bJE7QJYXJIaAFXqpZdeimuuuSY++9nPxnbbbVckIxY2gj87xKuttlr9Dfe8NpMaGUzsueeeRaf6qquuKkbT/PrXv46NNtqoGP2Vo7OyQ9qUUnVIdrQ/bIRVvn6fPn3qExInnHBCPPnkkwu9PgOwplx88cXx+c9/fpG/a9ttt42DDjooPkx23jOQygRIVjFkkJiBYWPZKS+XAcMxxxzT4NiVV14Zzz//fIwZM6bYz8CuFBwsrgxoMojItufrDB06tD6Ay3/fphIapZ+76aabim1+vssvv3z8+c9/jm9+85vxyU9+MkaOHFlcl0FmXgMAQPXIG8gbb7xxrLPOOg2qCgYPHhx//OMf45lnnomuXbs2GNxSujGefdJMRnznO9+pP58337O/XN6fzZH+mRxp7Atf+ELRPz311FOLn8lq7vJ+9tlnn11UfedAq5KFVRdn//Tpp5+OE088sf5YqR++zz77xKBBgxb5OWTskXHPh8UdGfdkP7w02Ojaa69tUG3SWJ5r6vz+++//odXf+Tnne1jUQKqSn/70p0VC4MADDywq0/MzbSzjs3L5fss/25T/5qeffnrce++90b1796J/nwmYhSW2FiarVHbcccciSZX/jqW4I9/TYYcdttCfy8FWGcvl31zGHTn9Vg6cykF2mZzK2DBj0GzPkrYJYElIagBUoRw1dfzxxxed9ueee67+Rn8mMPJcdmzLkxrZuXzkkUeKY9nxzQAnZVIjRxZlwqEkb5BnYJEVGBns3HrrrcXIm4WVjzfVIS+XgU22KW/8Z9Ik5aikTKbkyKZSNUJOk5SjutZYY40FXiPfZ45GalwhkSPXDj300CZ/byZ8Gsug48gjj6zfz2qM0vRdGfjsu+++RQc925GVEfkZl5R/lo3L+n/3u9/Fj3/84/pj2YEvfcbliZ18H9nhL5fl3flZ5nvLgCgDqBzBlkFrjsJ64okn4tJLLy3287PMdjSuYiklrFJOT5DBYgawGXCk/BvJZMznPve5YpRY4/cAAEDLk5XHmWzI/nYO7sl+evYxsw9f6nNmoiFlHzH7itm/zRv5qXF/dFEWNvglK49zwFP2P3Mkfw6AamoAUo7Wz75zVk3n1EPlsl0XXHBBEQuUD9TZeuutiwFUJ598clGtXZ6cKZfvLfvr+f5Lg4iakm3IuOOQQw4p+v2lSoLVV189/vCHPxT7b7zxRhHjZN84f39TMibI5FDjKvVPfepTsbjyvf785z+v389YIwdUPfDAA0UCJwdUZcyWn2cmrjKxUh7rZOzQ1HoUmXjIf5NMaKT8W2j875yfd1bmZ9zROLGQcUHGAvlz3/jGN4p/14wz8rPLf4NMemWiIn93vkZW75f/bWT1RanqO6tGMgGScdpPfvKT4nVz8Fz+HeTrZfKlPM4EWJokNQCqTHYQc4TMuHHj4owzzoi99967/twvfvGLorNc6rQ3lh3PvDFfHgjl62WHNTurWXKeHf2cGzY7wbmuxFe/+tXiNXNapMZBVpYTl5IlS6J8ztf8fTnNU5aA52iwxr70pS8VneT8XZkUKFfqwJeXQC9Mvk55oJDvO99/Bjn5KO/o5wio/J0ZaDz77LPFiLD8+abk6KmcU7ap4C4TRuWyZL7xsZzL9uCDD17gNcuVJ2Kaeo2Ubbz77ruLIPZ73/tefWl+Bn75d5KJjwzcJDQAAKrDZZddVtygzgFGX/nKVxZ73biSJRkpX35tKUGSCYHyyuU8lsmVXJevNFipJNfZy75oU1M5ZUV4xi5NJSTyxnf2W3NqqpyCtampZl9//fXiJn+uWZeVyB817sjXyKqV7H83NTAqb8BnkiGn581Yoam4I6saMsmzKPk7Gg/GSpkoyWl2U8YZmYDJ+C0TOz169IiZM2cWU99mxXVTcrBTro2Xj8YV5jn1U2M59Vj5tLMZS2VVxaLkGhjl62A0fo2UcVsOLsupyTJ+y8qTjCVznZJMaOSgr0warbLKKov8XQAfh6QGQBXJznUmAHLu0yz9Lk9oNL6uFIyUqguyI55TEWXCI5MaGRTkdRl4ZAc3O+hZbZA30PPGeFYLZFnyyy+/XAQaOf9redIhkxofdeRNdtZTLmidnfZsR96oL5WJ535WG2Q5e96gz05yUwuSl4KLLMdfHOWjjDKAymqMLHkvBXH5mWWVRn4mpSRGlnZnQJnBZI58K5cJmQzSso077bRT/fGcEzc/4/JFz0vrcTSWUwdkdUUGjeXBZAYmuWBg+Xol+e+5sNfJ4CPnBS4FUqVpBrIdOVot52JuKrgCAKDlyRvvOSq/fBR+9tf/3//7f8XN8ezXZj+v1O/PPn9W/eaadyXZt8zBSfkol9NAZXKiXGmNhpT9//IBPdlfz7559rnzpnz2jRsnNXLNj0w45LSp5bKfm78rY4zG07qm7P9nPzarBrJaPCs6Mg4pl3FH+iixR075lI8chJRVzA8//HCxnkRO7VSSfe6cHipjpYybsn/fWCnBkzHBh8UeOTisNIgsZaIkK7v/53/+p0HSJj+X/H1ZqZJywe3zzjuv+DdvPOgp47Wscs9/l+9+97v1x3NwV1aE3HzzzQtUajSufMnXzYXF8++nvH1ZKZ7/nuUDqUp/U40rVlImNDL+KQ3GKh+QVVofJJM0AMuSpAZAFcnAJNdayLlOc4RQ3vguv1FfCmoyoCgfXVS6OV6+CF9OeZSjnkpl1KWb/Lm4X5Y/Z4c6587NTn8mNBqvrZHl5xmENLWWR2PZaW486ioTM1lBkOXpWeKcN97PP//8ohQ7O+wZcGRQtqgO8ZKuEVGeNMjAIEcX5YiifH85WiqTOlnNktM95fvKMvOcazZHneVooyzJLiVeUrY/K1i+9rWvNQgMctRVBl0ftrhg6T0s7vtY1LVZKZLTD+TC4LneSmme5HxvGaRk0JTB2KLmyAUAoPJypHsOjsm+eFY4lOSUSvnIPl3jPmHeyG68nkXGA4u7pkZWKpdkPzYHy+TvyZ8t3dBPOdgpkyvlMUBWGOSN7uwbN44XsgIjEyZZMfzCCy80WT2SlRF5wz8fOUVttq28kiJfJ6u28/0sTuyRcUd53zwHSuVgrbxBn33hrCbIqpesfsnpvbKfnImPxomaxspf88OUX5vTzea0U1/+8peLzzorr3P63azSyEfGJVn9kHFGVsxn/z2nAt5iiy0axHP5+eXPl8cYGfvl+12cuCM1ngp3Ue1fWIV3Tn2cMVA+8m8u/yYzUZbT3GY8koPoMpGT63UYVAUsK5IaAFUmFwrMR970T7k44MKmnyqN0ikfYZMjcDI4yYWtc+G/vDaTGDnqJ4OeHMGTC9jlqKoMGrp169ZkqXsGF9l5/bBF/VJ2eHN0UrmcDilHcuVInuyM5zZHCWXQk1NAZXBRPoKqKaXA7cMWDGxKBl0ZWGSAk59haSRbyoAmO/w5uinffyZWskIig52sYsnPKIOqnN84P9/GAU5+brlwXmMZsOT1Gbh9VPka+b4bj5oqrUWScw1nQJjvIbelcvFM0GQQlEFp/jsDANAyZb8zB9zkaPjsH5dk3y77ein7rqVKjdIj+9E5CKek8RRKiyv7maWqiuwTl5IaeXM+F7POpEh5UiUTDvfff3/xczm4p7SGX06Zm33pTCTkwKxFybUbci2/TOY0ro7OuCMTJ4sTd6RsS/lApHyeVSx5kz37xjkIKAd0ZXVDJo2ycnth6/SVZLySn2cOCvuw2CP73eUDujJ5kp9ZVopkFXhOy5TVGXldfi75+ebi5qXHwIEDi8qYX/3qV/XvI6/LCu/y95WmT5/eZGxRWhMjkwpLOhCs8Ws0ToRkjJQD5rKSvrReX36W5VOD5bTG+beQFTBLsrYLwOLyzQJQpRanc5rXNL65nvOi5o3xrFDIjnF2orNsOSskcp7brFTIACnXmcib4CmnY2q8MF55wLQoOZ3Ta6+9tsDx7AiXplbK0ua8KZ+BUJba58J3mXTJSpRFzcWanfi0sPUuGmu8JkcGDFmunUFh/p4sm89y+mxzJi/yM9lss82K4Crn+M2y9dLnnosV5sLbC/P44483mI+2XK7TUXrf+RlkINDUqLVcz6QpWT2SVSblVTePPfZYMYIry9YzUZM/m6+fn1EGTDkqL8vNc2RdBsmZ0AEAoOXJG9s5pU/jm8GZ5MjBRk2Nfs8b0I2nKc2+b/YHy6sbSjeqy4/l/uLIvm/2hxtrvJ5DDpjKm/jZr84b25nYyD5+xh1Z/Z2j+cuTCDmdUk7rmgmHpvrPmQAoTdG0KDmdbfbbs/q8XFYxlF43ExP5+eY1WcGRbcrPIvvQi1qvI6/Jny2velmUxouQ5++75pprisFUmQTKacTyfed0wlmlkcmS4447rvj3zUqITF7k51aSffecFmxhmlpTI+XaGxnzpYwPcvBY4wFZmazKdToyAdFYflblayjmtbm2R1byZDV7tis/8/ybzMRT/n3k9FxZyZ5xVr6fTCYtyfouAItDUgOgipQCkyUZ7ZKd7wxwcnR/aX7XTB5kCXQmGyZNmlTcqM8OfY7uKg9qcoRNJj9yBM5Hla+fc882loFLju75y1/+UnTqM8DKqa8yWMo1N3L6q1ysO+fUzam2MsmSFR+77LJL/WtMnTq1aHe28cOSPFmh0jjASfm7sook32uOOvrzn/9cfE4ZeOWIrvSnP/2pGGlWPg9wBl3Z7uzAl/97ZIVLltlnxz3fQ/mIstK/X0m2PUuz8zXKg4ssg88qmixNzzl9S+8t/y3z5xsHspmMyeAxZRIrEx45hVfpbyVHb+Ujkyf5OWbSJoOk0kKFAAC0LE3195ta36Ck1Mcvl/360hRH5fIG/pVXXrnQNTUWJm9kZ7tKlRs5TW1WOpQGQmV/Pvu7pZvxWa1QqljI/mwOnsob+I1vwOcAncYVCB8n7mh8Az0TEk8++WRR6Z59/oxDvvjFLxaVLzntVlYZ5M3/HOSUg57WWmutIhGS1RGlKoWMO1LjNe+akn3xTOY0lsmBTObk1FPZzoxhcuHuXGcwp49NOQgpF2JvnBQprZVSHgfkv28OPstEQiYRyqeZLSWvyuOf0lTE+bdS+ozys8/ESr7P/AzK47ZSDFkuY6TSQub5d5DvJacVzvebr5mxSMYd+XoZx+W6MBk35aArgKVJUgOgijz44IP1nchFjcpp6lh2zrMTn53N7DRnZz47sLmORKljm+tbZCCSwUp2THNUTZY+l5ccZ8c318DIDvXCRtzka+QN+EwC5PUbbrhhg/eQ0zhlUJM/nyN5cj2N7NCXFrPLcvBcIyIrSDKhkAFUdqizdLs8qZFBVL6vxalayXUyGssKhlwwPUvOU3b887UyECu9t1JA2TiwzOtKUz6V5HvOoDGn1coETI52yiCp8SJ9Jfk7SomT8s/uxhtvLBIOGXzllFjliwE2JaclyNFwWeGSgWMGE/nvkwu7Z4IlqzRK8j3mVF/ZPgAAqkfedM7+eVPTDWXffeeddy6qckumTZtW9LvLb3YvzpoaTcmEQK7pkDfPS0pTUy3ueg459VJen9XQjZMRmUxoLKsCXnnllSbXEGm8oHUOBsq4o7zKO+ONYcOG1S80nu3MZEYmAkp94YxXcurdHFSU0/Lm2iA54Km0bl5JaW2TnCbrwxx77LEL/ffLz76UOMh+eU5zmzFBKfZY2Bp6OTCpsayEyb+FnFY3qyVyPY4cDLYwTVXA55S/GY/kZ5NJnqwmWdS6Gzk9cFYRZbuziiOvzWRaDgDLeC0HVjV+/cVJmAEsKUkNgCoyZMiQYlqj7NjnTfbGHd7Ga2qUbrRnB7NUjp772bnMTm+WfmcnuDTfbk6LlAFOdpDHjh1bdLjzunKZBMiqgEUFF/l6eSM/25HJk/IRPxnEZJJjm222KTrFmczIm/9NlTuX5LnsOOe0SeW/IxMejUcxLYkcaZUJnUyWlObrzYURP+q8r7nQeY64yqm5MmjIBE4GQzlaK5MvHyaDshzFlIFdjsbKUXSZcMlS7qzcWNhCe/nZZIImE1RZEl9eql6S8y/n+8u5fDNpBABAdcm+YPZV8wZ8Yzlopbw6IAfKZAVADiD6uDJGyJvdO+2008da/DxjjJxGtXy9iZTJiKYqu3OqpoMPPrg+9lmYTA5k9XPjuCP7xLnYdg78yWRG9oPzhnw+FiYTHDmlUg7iKpdJn0xolNarW1JZ4ZAJk9IUvlmxkp9JJk+yUnxJ5WtlTJf9/5yKN6tAct28rHRf3Pgoq71zAFbGdjkoLheBz9glB4MtrHIm/x1yWuJMomQM1dQaiPnvllU8GWceffTRS/zeABaHpAZAFclOZOMg4MPkqJ/8uVIgkDfOjz/++KLKIMuR84Z7dmBTdmazc5rzrWYnO0cTNa7GyI7p4nZOM7DKUu3y4CJH8uSUSil/R+O5XhvLNuVN+8Zl3jmHa849u+eee8ZHkWtiZAc+kwalUv1M5OQaGguT61PkdFk5z22p7D5l0iEDyZzHN0v5SxUcGTBlpUSOBsvPNatsFhYg5AiyH/7wh0XAk/PP5r9NPs4555xiDuEMVPJzz0qVxsmN/Iy/8Y1vFP/GOaKtKfkzWQGS1R+ZJFoaJf4AADSfxmshNFbeb89pjPJmf05l9FHlIKK8+Z7rLWSfO/uqH0X2obNPnH3mrAJv3I/N801VEeQN86effnqxf0/GBptvvnmDYzkwqCT779mfLq9SaVz9nAOz1ltvvQbH//Of/xQV7wv7ucX5HDNWyPVENthgg+JYxg2Z0FhUnzwXCs8K8PKBXRnLZayQ09zm9LqltQVLA6ByeqtSJc7CKiQyBst4IAfEZeKhlATJNQUzMZIJoIMOOqgYANd4sFT+/vw3zJgoE1RNyYRaDs7KGDMHfTWuzAFYGhb9f0QAqkpWZJSqLhYmO5k5nVN2rLP8PDv+WU5dmi81p4LKNTSaqgRZUrkQXVrUYt8fRQY/OSop16tovID54jr11FOLwHCfffapr0C5//77ixL1Uoc9H6XgMRcSzMqT7MSXEgcZEOTPZal/Vo3kYuLlgVQGIDmqLZMZuXZHBiUZIOSouZKs7MggJJMVGXjm6K3yACQrWTJwyX+LHBGVyZ/8tyvJf7tMmOSosRxtVT71WP6bloLbDERzJFYGZfnvXiqhBwCg5cqplUr9++ybZj8y+3uNHzlIqDwOyBvieTO5cWVBXpOvUy77tuUDdsqnnMrFqbOfmlMTNU6qfFjckb8nkwE55W1OXZV95dJgp6z+yBvemWTIuOPjTlGUVenZ1qYqPpZE4wFd+R6zyjkTBvvuu+9Hes38t8kkU2lx9KxkyCm/Sq9XijtSfsb5WWVfPRfZzoRSqV+f1Rn5M7k2Rg5Sy+l6y9ud1+dnmmv8ZX8/B1ZljFI+iCqrO7LiJtfCyAr/rEopyb+jjBcyvspqjaxyydiltDh8TmeWcU0uFJ4V7znVbVNxx8Ybb1z8/eVAr0yMZOwJsLSp1ABoRTJh0Xgxt8ayU56VD/nIICfnQs1Ao3wB61wULwOD4447rqheaGre3kVVQGRnOduS0y/l6KPGa0+UlDrv2dldlNLUWSV58z5HjWVZ80eVnfycAisrX7K6IoODnAu2NA9tJhhySqc8/+lPf7oIGrKiIzv+OWIpq1Dyc8opnTKpkQmH/EwbywAo59XN3zdq1Kh4/vnn6+fEzQAykyv5WpnYyGuaGoGXyZv8d8jESAYQ5SXl+Zo5yisrbnJO2wy8LrjggiJ5kY/yyp4cdZY/n6PsmpqiCgCAliX76KU+cz7P6ady8eXGsi+bfdfSzetcvDn7mU31q0t96xwolP3YXHsib143loN1smI4pyRqqo+ar9M4QVIup83NAUFZCZ2V2uVryWV8cOutt0a/fv2KG/wfdVqnfN+5EHhWBmQlSPabFybbmnFKPhamcVVBxjMPPPBA8Tl06dLlI7UxByXluiZZef7Xv/61mFIr1zIsxUCluKP0mec0tPl5ZaLnc5/7XHE819jLREGuPZLTypavWVguKze23HLLYlBUDpYqH/CU03/luiZZkZL/3k3NAJBxSg7KynVFsnIjf1+u15dyiq9MqmUslv9uacyYMcUgrZyeKwdjlQ/uyhgrp9Zd2gPcAFKb+R+WWgegauQN7hxxk1MVLQ3ZuV7UQnELk9UeWUae5dXZaV/YehKZAMkRQB82/VQGOuUjgVKOxFqcdSoWRwZAWYnRuFz9w2QHPhMducDg4iqv/igFk5lY+rDpBEryf9vlI8jy9TKRVT66LsvIs20ZQGbpeWl6LQAAqkcmMXLATY6+L00ztDheffXV4oZ0Vl83nrY0pzrNvmfppnTeQM91N/Lm+ZJWaZ9yyilF1UEmJxYmF/pe2ACnpSEH9mTVwbrrrltUJ+QN/YXJCoX8HBc1/VTGHlnx0Pg95OfVuIrjo8i+fCYWtt9++yV6vRzQlRUYWfn9UeOOjO0ybliS6Ywbxx6ZgCmPLXIarazqyOqOnGKsPHEFsCxJagBQMdnRzjLmHPXUVMk7AADA0pCDrjLJ81EGbQHQskhqAAAAAAAAVcFC4QAAAAAAQFWQ1AAAAAAAAKqCpAYAAAAAAFAVJDUAAAAAAICqIKkBAAAAAABUhXaVbkBrMGXKzJg/v9KtAACAlqlNm4ju3btErRM3AADAx48bJDWWggxMBCcAAMCiiBsAAODjM/0UAAAAAABQFSQ1AAAAAACAqiCpAQAAAAAAVAVJDQAAAAAAoCpIagAAAAAAAFVBUgMAAAAAAKgKkhoAAAAAAEBVkNQAAAAAAACqgqQGAAAAAABQFSQ1AAAAAACAqiCpAQAAAAAAVAVJDQAAAAAAoCpIagAAAAAAAFVBUgMAAAAAAKgKkhoAAAAAAEBVaFfpBgCV9d57c+N3v7s7Jk58PXr3XjV23HGnaN++Q6WbBQAAAACwgDbz58+fv+BhlsSbb84MnyLVaPToa+Kuu34V8+bNqz9WV1cXu+yyR+y//8EVbRsA0Hq0aRPRo0eXSjej4sQNAADw8eMGlRpQwwmNO++8Lbp2XSn22edrsckmn43HH380fvnLXxTHk8QGAAAAANCSqNRYCoy4ohqnnPra14ZFly4rxuWXXxPt2v1ffvP999+Pww8/OGbOnBm/+MXNpqICAD42lRr/JW4AAICFU6kBLFSuoZFTTmWFRnlCI+X+V77y1bjiikuK63bZZfeKtRMAACph4sQ3YvbsWZVuBlWoc+cVonfvVSrdDGiVfDfzUflubn0kNaAG5aLgKaecasommwxqcB0AANSKGTOmxzHHHBbz5//funOwuHKNwiuvvC5WXLFrpZsCrYrvZj4O382tj6QG1KDevVcttrmGxg477LjA+ccff6zBdQAAUCvyhsdFF40yGnghXnttQlx44Xlx7LHDY/XV+1a6OS1yNLCbZrD0+W5eNN/Ni+a7ufWR1IAatOOOO8Xo0T8rFgXfZpvtF1hT46abro+6urbFdQAAUGtMUfHh8qbZ2muvW+lmADXEd/OH891MrairdAOA5peLf++yyx4xffq0YlHwe+75bUydOqXY5n4ez7U0LBIOAAAAALQkKjWgRu2//8HF9q67flUsCl6SFRq77bZX/XkAAAAAgJZCUgNqWCYu9tnnq/G7391dLAqea2jklFMqNAAAAACAlkhSA2rcf6ei2r3SzQAAAAAA+FDW1AAAAAAAAKqCpAYAAAAAAFAVaiapMWbMmNhll11i0003jeHDh8fUqVOL488991wMHTo0Bg0aFGeffXbMnz+/0k0FAAAAAABqNanx4IMPxsiRI2PEiBFx5513xqxZs+Loo4+OuXPnxuGHHx4DBgwokh4vvvhi3HbbbZVuLgAAAAAAUKtJjV/96lex1157xRZbbBGrrbZanHTSSfH444/Hn/70pyLBkcmOfv36FRUct956a6WbCwAAAAAANKFd1IC33nor1ltvvfr9urq6+qmnBg4cGJ06dSr2+/fvX1RrLKk2bZZiYwEAoJXRXwYAAJaWmkhqbLDBBkVVxte//vUioXH77bfHRhttVFRp9OnTp/66Nm3aFOenT58eXbt2XezX7969yzJqOQAAAAAAUFNJjUMOOSQee+yx2HPPPWO55ZaLp556qlgU/Pnnn48OHTo0uLZjx44xZ86cJUpqTJkyM6wvDgAAC6/UMBAIAABYGmoiqbHiiivGDTfcEK+88kpcffXVMWPGjNh1112L55nYKDd79uxo3779Er1+JjQkNQAAAAAAYNmqiYXCS3r16hX33HNPnHDCCdG2bdtiCqqs2iiZMGFCzJ07d4mqNAAAAAAAgOZRU0mN0aNHx9prrx077LBDsT9o0KBiXY0xY8YU+6NGjYohQ4YUCQ8AAAAAAKBlqYnpp1Iu/p3TTV155ZX1x9q1axcjR44sKjfOOeecYpHwTHwAAAAAAAAtT80kNXJKqUceeWSB49tvv30xJdXTTz8dAwcOjG7dulWkfQAAAAAAwKLVTFJjUXr27BnbbLNNpZsBFfHBBx/EuHHPxFtvTY1u3VaO9dffwBRsAAAAAECLJKkBNeyRRx6Ma6+9OiZPnlR/rGfPXnHggYfE4MFDKto2AAAAAICaXigcaJjQOPfcs6JfvzXjRz/6cVx33c3FNvfzeJ4HAAAAAGhJJDWgRqecygqNz3xmUJx00vdivfXWj06dOhXb3M/j1133s+I6AAAAAICWQlIDalCuoZFTTu2117Coq2v4NZD7e+45LCZNmlhcBwAAAADQUkhqQA3KRcFT375rNHm+X79+Da4DAAAAAGgJJDWgBnXrtnKxnTDhlSbPjx8/vsF1AAAAAAAtgaQG1KD1198gevbsFbfddkvMmzevwbncv/32W6JXr97FdQAAAAAALYWkBtSgtm3bxoEHHhJPPPFYnHPOj+LZZ8fFO++8XWxzP48fcMDXi+sAAAAAAFqKdpVuAFAZgwcPiRNOODmuvfbq+P73v11/PCs08nieBwAAAABoSSQ1oIZl4mLTTQfHuHHPFIuC5xoaOeWUCg0AAAAAoCWS1IAalwmMAQM2qnQzAAAAAAA+lDU1AAAAAACAqiCpAQAAAAAAVAVJDQAAAAAAoCpIagAAAAAAAFVBUgMAAAAAAKgKkhoAAAAAAEBVkNQAAAAAAACqQrtKNwCorA8++CDGjXsm3npranTrtnKsv/4G0bZt20o3CwAAAABgAZIaUMMeeeTBuPbaq2Py5En1x3r27BUHHnhIDB48pKJtAwAAAABozPRTUMMJjXPPPSv69VszfvSjH8d1191cbHM/j+d5AAAAAICWRFIDanTKqazQ+MxnBsVJJ30v1ltv/ejUqVOxzf08ft11PyuuAwAAAABoKSQ1oAblGho55dReew2LurqGXwO5v+eew2LSpInFdQAArc0tt9wSW2+9dQwcODD233//mDBhQnH8ueeei6FDh8agQYPi7LPPjvnz59f/zKOPPho77bRTDB48OK655poKth4AAGqbpAbUoFwUPPXtu0aT5/v169fgOgCA1mL8+PFxySWXxKWXXhp333139O3bN04++eSYO3duHH744TFgwIAYM2ZMvPjii3HbbbcVPzN16tQ44ogjYuedd46bbropxo4dGw8//HCl3woAANQkSQ2oQd26rVxsJ0x4ZaHBfvl1AACtxTPPPFNUaGTyYrXVVisqM1555ZW4//77Y9asWTFixIhigMfw4cPj1ltvLX7mzjvvjF69esVRRx0Va665Zhx55JH15wAAgObVrpl/H9ACrL/+BtGzZ6+47bZb4sQTT45nnx1XVGVkEqN///Xj9ttviV69ehfXAdD8ck2jnAKw9N2c38dt27atdLOgVVh33XWLKot//etf0adPn7jhhhtiiy22iHHjxhXJjlxnLPXv37+o1kjPPvtsMe1UmzZtiv2NN944zj333CX+3f/741DVSn/HufU3DdAy+G6mtVjcv19JDahBeWPswAMPiZ/85Mw48MB9iukWSjp06FDsn3jiCDfQACrgkUcejGuvvbpY+6gkE9H5vT148JCKtg1aS1Jjxx13jD322KPYz8RGrrFxxRVXFM9LMoGRa41Nnz69qOBYZ5116s+tsMIKMWnS//03uri6d++ylN4FVM7UqZ2L7UordY4ePfxNA7QEvpupNZIaAAAtKKFx7rlnxWc+MyiOO+7bxdpHOVVgVtbl8RNOOFliAz6mv//97/HHP/4xbr755lh77bXjqquuim9+85ux2WabFYM7ynXs2DHmzJlTDPQoP1c6vqSmTJkZZWuPQ1WaNm12/fbNN2dWujkA+G6mlVVqLM5AIEkNqNFpTXIU8CabfLa4Qfbcc/83/dR6661f3Di77rqfxaabDlatAdDM382Z0DjppO8VI8RTfi/n/jnn/Mh3MywFd911V7Hgd041lY477ri48cYbi+qN559/vsG1s2fPjvbt20fXrl2LxcIbH19SmdCQ1KDalf6G/T0DtBy+m6k1FgqHGpTztOe0JnvtNawIyAcM2Ci23HLrYpv7e+45LCZNmlhcB0DzfzeXEholue+7GZaOefPmxZQpUxokKN55551o165dPPXUU/XHJ0yYUEzJmQmNjTbaqMG5XGy8d+/ezd52AABAUgNqUlZlpJzWpCn9+vVrcB0Ay57vZmgem266adxzzz3x85//PMaOHRtHHnlk9OzZM/bff/9i7YwxY8YU140aNSqGDBlSVEZtt9128cQTT8SDDz4Y7733XjFl1ZZbblnptwIAADXJ9FNQg3KaqZTztK+zzieKUb+l6afWX3+DGD9+fIPrAGje7+accqox382wdOQ0Uy+++GJce+21MXny5PjEJz4RF198cVGtOnLkyDjhhBPinHPOKSqkRo8eXfzMyiuvHCNGjIhDDz00ll9++ejSpUucddZZlX4rAABQkyQ1oAZl4qJnz15x9dWjYubMGcV0JyV5vEuXFaNXr97FdQA073dzLgpevqZGabqc22+/xXczLAVt2rSJo446qng0tv322xdVHE8//XSx5ka3bt3qz+27775FdcZLL71UVHt07ty5mVsOAAAk009BDcppFDbfPIPyF4q5og877Oi44opri23u5/HNNtvCQrQAzSi/cw888JB44onHikXBn312XLzzztvFNvfz+AEHfN13MyxjORXVNtts0yChUdK3b9/YeuutJTQAAKCCVGpADfrggw/ioYceiHXWWTemT58eo0ZdXH8uRwnn8Ycf/kvst98Bbp4BNKPBg4fECSecHNdee3V8//vfrj+eFRp5PM8DAABALZPUgBqUa2jklFPHHfftJtfUeOGF54ubaXl8wICNKt1cgJqSiYtNNx28wHezJDMAAABIakBNyptkqW/fNYqbZI0TF/369WtwHQDNq6nvZgAAAEBSA2pSjvpNEya80mSlxvjx4xtcBwAAAADQEkhqQA3KxEWunfGzn40q1tR4883J9ed69OgZXbt2LeZvz+sAAAAAAFqKuko3AKjMtCabb75lvPjiC/Hee+/FYYcdFaNGXVtscz+Pb7bZFuZvBwAAAABaFJUaUIM++OCDeOihB2LttdeNmTNnxKhRl0REPqKo0MjjDz/8l9hvvwMkNgAAAACAFkNSA2pQrqExefKkOO64bze5psYLLzwf3//+t4vjFqoFAAAAAFoKSQ2oQZnASH37rlFUYjROXPTr16/BdQAAAAAALYGkBtSgrMhIEya8En379o2LLjo/3njjjVhllVXimGOOj/HjJzS4DgAAAACgJZDUgBqUU0z17NkrfvSjU+Ptt9+uP55JjgMO2CeWX375Ym2NvA4AAAAAoKWoq3QDgOaXU07V1bWtT2h86lOfiZEjzym2KY+3aVNnkXAAAAAAoEVRqQE16J13ZsfEia/XTzH11FNPFI+08srdY+rUKcX5vK5Tp84Vbi0AAAAAwH9JakANyjU00lZbbRtHHvmtGDfumWJR8Exw5JRTl1xyQfz5z38qrjvppO9XurkAAAAAAAVJDahBuSh42nXXPYsppgYM2KjB+V122b1IapSuAwAAAABoCaypATVolVVWKbZjx97e5Pm77rqjwXUAAAAAAC2BpAbUoGOOOb7YZjXG9OnT4qqrLoszzvhBsc39Bx64r8F1AAAAAAAtQc0kNW655ZbYeuutY+DAgbH//vvHhAkTiuPPPfdcDB06NAYNGhRnn312zJ8/v9JNhWUuF/9eZ51PFH/v3/jG/vG73/0m/v73p4pt7ufxPG+RcAAAAACgJamJpMb48ePjkksuiUsvvTTuvvvu6Nu3b5x88skxd+7cOPzww2PAgAExZsyYePHFF+O2226rdHOhWeSi4B/nPAAAAABAc6uJpMYzzzxTVGhk8mK11VYrKjNeeeWVuP/++2PWrFkxYsSI6NevXwwfPjxuvfXWSjcXlrl3330n/vrXR6Jdu3Zx9dWjY9CgwdG37xrFNvfzeJ7P6wAAAAAAWop2UQPWXXfdePjhh+Nf//pX9OnTJ2644YbYYostYty4cUWyo1OnTsV1/fv3L6o1llSbNsug0bAMjR7982K76657RNeuK8V3vvP9Bud33nn3uOOOMcV13/zmERVqJQDQWugvAwAAS0vNJDV23HHH2GOPPYr9TGzkGhtXXHFF8bykTZs2UVdXF9OnT4+uXbsu9ut3795lmbQblpUpUyYV26FD94gePRb8+/3yl/cokhp5XVPnAQAAAAAqoSaSGn//+9/jj3/8Y9x8882x9tprx1VXXRXf/OY3Y7PNNosOHTo0uLZjx44xZ86cJUpqTJkyM6wvTjXp3r1XsR0z5lex115fjgsvPD/eeOONWGWVVeLYY4+PW2/9Vf11b745s8KtBQBaQ6WGgUAAAMDSUBNJjbvuuit23nnnYqqpdNxxx8WNN95YVG88//zzDa6dPXt2tG/ffolePxMakhpUk/33Pyh+97vfxO2331o8SiZMeCX233+fBtf52wYAAAAAWoqaWCh83rx5MWXKlAaJi3feeadYDPmpp56qPz5hwoSYO3fuElVpQDXq2LFTLL/88vX7ffr0jRNP/H6xLcnzeR0AAAAAQEtRE0mNTTfdNO655574+c9/HmPHjo0jjzwyevbsGfvvv3/MmjUrxowZU1w3atSoGDJkSLRt27bSTYZl6p13Zsfbb79dv//qqxPiJz8ZWWxL8nxeBwAAAADQUtREUiOnmco1NK699toYMWJEzJw5My6++OJimqmRI0fGGWecEYMHD4577703TjzxxEo3F5a5iy46v9hutdW28Ytf3Bw77vil2HjjTxXb3P/c57ZpcB0AAAAAQEtQE2tqtGnTJo466qji0dj2229fVHE8/fTTxZob3bp1q0gboTnlouBp1133LKaY+sY3jmhwfpdddo8///lP9dcBAAAAALQENVGp8WFyKqptttlGQoOascoqqxTbsWNvj9mzZ8b3v39SHHbYwcU29++6644G1wEAAAAAtAQ1UakBNHTMMcfHAQfsE/ff/8fiUTJ16ptx0EH7NbgOAAAAAKClUKkBNahTp87Rrt3/5TRXWqlbHHHEscW2JM/ndQAAAAAALYVKDahBOcXU+++/X78/bdpbcdllFza4Js/ndZ07d6lACwEAAAAAFqRSA2rQmWeeUWw/9anPxHXX/TIGDRocffuuUWxzf+DATze4DgAAAACgJVCpATVo8uTJxfbLX963mGLqpJO+3+D80KF7x9/+9mT9dQAAAAAALYFKDahBPXv2LLa33npjk+fHjLm5wXUAAAAAAC2BpAbUoBEjflBsn3rqiXjllZdj//2Hxd5771Zscz+rNMqvAwAAAABoCSQ1oAbl4t+9e69aPD/xxGNjzpw5MX/+/GKb+ynPWyQcAAAAAGhJJDWgRk2ePPFjnQcAAAAAaG6SGlCDMmExb9684vm5514c/ft/MlZeuUexzf2U5yU2AAAAAICWpF2lGwA0v+HDjy62K6zQJfr1WyNGjjynwfmcdmr27JnFdaNH31KhVgIAAAAANKRSA2rQu+++W2z33feAJs9/5Sv7NrgOAAAAAKAlUKkBNahjx47FouA33nhdDBw4MI4//qh47733on379nH++ZfETTfdWH8dAAAAAEBLoVIDatB55/133YxZs2bG0UcfWiQ0Um5zP6eeKr8OAAAAAKAlkNSAGtSzZ+8Fjg0aNHixrgMAAAAAqBRJDahBEye+vsCxxx57ZLGuAwAAAACoFEkNqEG5hkbq1Gn5uPTSq2K55ZaLNm3aFNv/7i/f4DoAAAAAgJbAQuFQg0praHzlK18tppgaPfqWBueHDds7Ro/+ef11AAAAAAAtgaQG1KD27dsXCYubbro+Ntts8zjhhGPinXfmRKdOy8W5514Ut9xyc/11AAAAAAAthemnoAadf/4lxfadd96Oww//esyePTvmzfug2Ob+nDlvN7gOAAAAAKAlkNSAGtS796oLHNt88y0W6zoAAAAAgEqR1IAaNGXK5AWOPfTQXxbrOgAAAACASpHUgBqUa2iklVbqFhdffEX92hm5zf2uXbs1uA4AAAAAoCWwUDjUoFwUPH31qwcVU0zdcMNtDc7vu+9X4/LLL66/DgAAAACgJVCpATWoU6fliu311/+8yfM33nh9g+sAAAAAAFoCSQ2oQeeee1GxnTbtrXj88Udj2LBd6x+5P336Ww2uA6B5ffDBB/H00/+IBx64r9jmPgAAAGD6KahJ3bv3jHbt2sX7778fZ511RoNzpf08n9cB0LweeeTBuPbaq2Py5En1x3r27BUHHnhIDB48pKJtAwAAgEpTqQE1KhMaH+c8AMsmoXHuuWdFv35rxo9+9OO47rqbi23u5/E8DwAAALVMUgNq0IsvPl//fMSIU6Jz585RV9e22OZ+U9cBsGzlFFNZofGZzwyKk076Xqy33vrRqVOnYpv7efy6635mKioAAABqmqQG1KCTTx5e/zxvkv3857+Mm276VbHN/aauA2DZGjfumWLKqb32GhZ1dQ27aLm/557DYtKkicV1AAAAUKskNaCGff7zOzV5fLvtdmj2tgDUurfemlps+/Zdo8nz/fr1a3AdAAAA1CILhUMNu+eeu2PXXXeP448/qpjOpG3btnH++ZfEH/7w+0o3DaDmdOu2crGdMOGVWGedTxQVGZnAyOPrr79BjB8/vsF1AAAAUIskNaAGnXXWefVTSx177OH1xzOxUb6f1wHQPDJx0bNnr/jZz0bF9OnT4803J9ef69GjZ3Tt2jV69epdXAcAAAC1yvRTUINyBHBja6219mJdB8CykdVym2++Zbz44gvx3nvvxWGHHRWjRl1bbHM/j2+22RbFdQAAAFCrVGpADXr99dcWOPbyyy81ed2qq67eTK0CqG1ZLffQQw/E2muvGzNnzohRoy6JiHxEUaGRxx9++C+x334HSGwAAABQs1RqQA3KNTRSx44dF5hiKvc7dOjY4DoAlr1cQ2Py5ElxyCGHxUUXXRE//OH/xLe+dWKxvfDCUfH1rx8WkyZNLK4DAACAWqVSA2p0NHD68pf3LaaYuuWWsQ3O77XXl+OXv7y+/joAlr1cFDz17btGUYkxYMBGDc7369evwXUAAABQiyQ1oAblzbJMWNx6643Rv//6ccopJ9efO/30s+K2226tvw6A5tGt28rFdsKEV4qEc1ZkZAIjj+fi4OPHj29wHQAAANQiSQ2oQeeff0kce+zh8e677zZIaKTy/bwOgOaRiYuePXvF1VePKtbUyKmoSvJ4ly4rFmtr5HUAH1d+x+R3DSyp116b0GALSyL7M9mvAYCPQ1IDalBTi3/36dM3Xn21YWBikXCA5pPVcZtvvmXceedt0bXrSnHYYUfHJpsMiscffyx++ctfxEsvvRC77baXKjpgqSQ0vnXcEfHe3LmVbgpV7MILG67NB4ujfYcO8dMLLpPYAOBjkdSAGvSvfz29wLHGCY3SdZ/85IBmahVAbctpAR966IFYZ511Y/r06TFq1MX15zLwz+MPP/yX2G+/AyQ2gI8lKzQyofHO2lvHvOW6Vro5QI2omzM94qX7iu8gSY2mqaLjo1JFR61V0UlqQA0qn2Lqwgsvj+OPP6q4mZY3yUpTU5Wua7yIOADLRq6hkYHsccd9u8k1NV544fn4/ve/XRxvvIg4wEeRCY15nXtUuhkAqKJjKVFFR61U0UlqQA0bMuRzxRRTv/zlrxocHzx4s3jkkYcr1i6AWpQJjNS37xpFkrlx4qJfv34NrgMAoPVQRQdUQl2VVtFJakANe/DBP8fxx5+0wHEJDYDmlxUZacKEV2K99dZf4Pz48eMbXAcAQOujig7gw9UtxjVAK3P66WfVP//tb38dw4btWv/I/aauA2DZyimmcmTMbbfdEm+9NSWOPPKQ+NrXhhXb3L/99luiV6/exXUAAABQq1RqQA0qX/z76qsvb3CufN8i4QDNJ6ecOvDAQ+InPzkzDj300frjkyfPiUMPPah4fuKJIywSDgAAQE1TqQEA0EJceulPP9Z5AAAAaO1UakAN+te/nq5//o1vHB5XXXV5k/t5nWoNgOYxffpb8fbbbxfPr7xydLz22oRiUfBcQ2P11fvGN7+5f3E+r+vatVulmwsAAAAVIakBNeiUU06uf77jjjsXj3KlpEZed8stY5u9fQC1aMSIE4ttnz79YqWVVioe5TKxkYmOvO7SS6+uUCsBAACgskw/BTVsyJDPNXl88ODNmr0tALVuxowZxfarXz2wyfP77vu1BtcBAABALZLUgBr24IN/jgkTXom9994thg3btdjm/iOPPFzppgHUnBVXXLHYXn/9tU2ev/HGXzS4DgAAAGqRpAbUoNNPP6v++fDhR8f8+fOL57nN/aauA2DZOvPMnxTbV18dv0A1Ru7n1FPl1wEAAEAtqok1NW677bYYMWLEAsfPPPPM6NOnT5x66qkxderUOPzww+Pggw+uSBuhOTW1+HfPnr1j8uSJH3odAMtGLv69/PLLF4uBH3LIV4s1NHLKqazQKCU08rxFwgEAAKhlNVGpscsuu8Rjjz1W/7jvvvuiW7dusc4668QRRxwRO++8c9x0000xduzYePhh0+7Q+uUUU401Tmgs7DoAlp1rr72pSFykTGT85CdnNkho5Hng4w946t+//wKPPP7oo4/GTjvtFIMHD45rrrmmwc/99re/jW233Ta23HLLuOuuuyrWfgAAqHU1UanRoUOH4lFyww03xOc///l48skno1evXnHUUUdFmzZt4sgjj4xbb701NttsyRZJbtNmGTQalqETTjim2LZt2zZ++MMfxQ9+cHL9uTPOOCt++MPvxQcffFBcd8std1awpUAtmDjxjZg9e1alm9Fi5PdyTjd1ySUXxKxZs2OFFTrHUUcdV6yl8fLLL1S6eS1K584rRO/eq1S6GVRZfzkHPO2www71+1kdtcceexQDnr7+9a8Xldt5zfDhw+OTn/xkERs899xzceKJJxYV3htvvHEcc8wxscEGG8Taa69d0fcCAAC1qCaSGuXefffduO666+Lmm2+OSy65pBiFlQmNlAHKueeeu8Sv2b17l2XQUlh2Smto7LfffvG5z20Wf/rTnxqcHzZsWPzyl78sruvRw983sOxMmzYtjjnmsJg3b16lm9JivfXW3Bg58tRKN6NFqqurK0bXr7TSSpVuCq18wNMtt9xSxA3ZR0pf/epX44477ojjjz++KpM7LaUdQG3K7yDfQwvymQCV1KaFfDcvbhtqLqmRU0xl8iLX0pg1a1YxIqtkhRVWiEmTJi3xa06ZMjP+9x4xVIUM1DNhkUH8eusNWKBSIwP30nVvvjmzgi0FWr+2cdFFo1RqNOHVVyfEhReeF8ceOzz69Olb6ea02EqN999v6/9VVSCDk5Y4EGhxBzyNGzcuttpqq/qfy3N5/ZJqKZ/B1KmdK90EoIattFJng+ea4LsZqKSVquy7ueaSGjn6PMvFS1PvlI/S6tixY8yZM2eJXzMTGpIaVJNzz70ohg8/uphiqjyhkcr38zp/28Cy1quX6YOaUvr+zQXD11pr3Uo3p8Xy/ymaY8DT7Nmzi2uaOleNg6GmTZtd6SYANSy/gwxIWJDvZqCSprWQ7+bFHQxVU0mNV155JcaPHx9Dhgwp9rt27RpTp06tP5/BSvv27SvYQmgeffuuscCx3r1XjYkTX//Q6wAAam3AU2sbDNUS2gDUrpbyXdjS+EyASppfZd/NdVFD7r777thmm23qExcbbbRRPPXUU/Xnn3nmmejdu3cFWwjN41//enqBY40TGgu7DgCg1gY8GQwFAAAtR00lNf785z/HZz/72fr97bbbLp544ol48MEH47333ourrroqttxyy4q2EZrDKaf83xRT5513cf3c0bnN/aauAwCo1QFPBkMBAEDLUTNJjSwP/9vf/haf/vSn64+tvPLKMWLEiDj00ENjiy22iJdffjmOOOKIirYTmtOQIZ8rppi6+eY745Zbxhbb3B88eLNKNw0AoMUMeNpxxx3jN7/5TTz77LNFlcbo0aMNhgIAgAqpmTU1lltuufjnP/+5wPF99923CEheeuml2HTTTaNz584VaR9UwoMP/jmOP/6kBY4/8sjDFWkPAEBzDng6/fTTmxzwtPzyy0eXLl3irLPOKs6tv/76ccABB8TQoUOL9TTWWGON2G+//Sr4DgAAoHbVTFJjUfr27Vs8oFacfvpZ9VNLPfDA/XHRRefGvHnzoq6uLo455oQG1wEAtDYfZcDT8ccfH7vuumtMnDgxBg0a1GDhcAAAoPlIakAN+uQnB9Q//+lPf1z/PBMb5fvl1wEA1PqAp3XXXbd4AAAAlVMza2oAAAAAAADVTVIDatBrr02of37MMcc3OFe+X34dAAAAAEClmX4KatDw4UcX2/bt28dWW21XPMpddtnF8f777xXX3XTTHRVqJQAAAABAQyo1oAbl2hlpzz33bvL87rvv2eA6AAAAAICWQKUG1KC6uroiYXH77TdHjx4949JLL6g/d+SRx8Udd9xefx0AAAAAQEvhjiXUoPPOu7jYvvfeew0SGin3c+qp8usAAAAAAFoCSQ2oQauv3neBY8svv8JiXQcAAAAAUCmSGlCD/vjHexc49vbbsxbrOgAAAACASpHUgBpUPuXUBRdcWr92Rm5zv6nrAAAAAAAqzULhUMP69OlXTDF10013NDi+6qqrxeuv/6di7QIAAAAAaIqkBtSwV18dHw899Jc477yz6o8NH36yhAYAAAAA0CKZfgpq0JFHHlf/vDyh0Xi//DoAAAAAgEqT1IAatO222y/V6wAAAAAAmoOkBtSgnHJqaV4HAAAAANAcJDWgBi1qiqlFTU0FAAAAAFBJFgqHGrbuuusVU0w1nmbq7rvvjJdffqli7QIAAAAAaIpKDahhL7zwXJPHJTQAAAAAgJZIUgNq0PDhJ9c//9Of7on99x8We++9W7HN/aauAwAAAACoNNNPQQ3afPMt6p9fcsmF9c/nzJnTYL/8OgAAAACASlOpATWqrq7uY50HAAAAAGhu7lpCDZo8eWLMmzeveP71rx/R4FxpP8/ndQAAAAAALYXpp6AGDR9+dLFdYYUusdNOXyoe5W666Rcxe/bM4rrRo2+pUCsBAAAAABpSqQE16N133y22++57QJPnv/KVfRtcBwAAAADQEqjUgBrUsWPHYlHwG2+8Lrp37x5nnXV6/bmTTz4lbrrpxvrrAAAAAABaCpUaUIPOO+/iYjtr1swGCY2U+zn1VPl1AAAAAAAtgaQG1KCePXsvcKxXr1UW6zoAAAAAgEqR1IAa9Pjjjy1wbNKkNxbrOgAAAACASpHUgBpUPuXUpZdeFcstt1y0adOm2OZ+U9cBAAAAAFSahcKhhg0YsHExxdTo0bc0OL7++p+MceP+VbF2AQAAAAA0RVIDatjTT/89/vGPv8Xpp3+//tgpp4yU0AAAAAAAWiTTT0ENOvnkU+qflyc0Gu+XXwcAAAAAUGmSGlCDNtlk0ALHOnZcbrGuAwAAAACoFEkNqEE55VRj7747Z7GuAwAAAACoFEkNqEGLmmJqUVNTAQAAAABUkoXCoYZtuungYoqpW24Z2+D4pz/9mXjyyScq1i4AAJa9unemVboJQA3xnQPA0iKpATXsr399pMnjEhoAAK1fp5fvr3QTAABgiUlqQA065ZSR9VNL/eIXP4877hhTf2733Yc2uA4AgNbpnbW2inmdVqp0M4AaqtSQTAVgaZDUgBq00UYD65+XJzQa75dfBwBA65IJjXmde1S6GQAAsEQsFA4AAAAAAFQFSQ2oQffd98f651/+8lcanCvfL78OAAAAAKDSTD8FNejii8+rf/6Vr3yteJS79dab6q/beuttm719AAAAAABNkdSAGtav35pNHl999T7x2muvNnt7AAAAoNYXVAdoLnVV+p0jqQE1bPz4f8cf/3hvXHrpBfXHjjzyOAkNAAAAqIBOL99f6SYAtHiSGlCDjj56eP0UVOUJjcb7eR0AAADQPN5Za6uY12mlSjcDqKFKjU5VmEyV1IAalOtklK+rsajrAAAAgOaRCY15nXtUuhkALVpdpRsANL+ccmppXgcAAAAA0BwkNaAGLWqKqfL9xlNTAQAAAABUkumnoIb16dOvmGKq8TRTY8b8Ml5//T8VaxcAAAAAQFMkNaCGvfrq+Pjzn++LCy/8Sf2xY489UUIDAAAAAGiRTD8FNejII4+rf16e0Gi8X34dAAAAAECl1VxS48c//nEcfvjh9fvPPfdcDB06NAYNGhRnn312zJ8/v6Ltg+aw7bbbL9XrAAAAAACaQ00lNcaNGxc33HBDfO973yv2586dWyQ4BgwYEGPGjIkXX3wxbrvttko3E5a5nHJqaV4HAAAAANAcamZNjXnz5sUpp5wSBx10UPTt27c4dv/998esWbNixIgR0alTpxg+fHicdtppReXGkmjTZhk1GpaR8immjjrquLjkkgua3M/rttpq64q0EaDWlfoXudXXoNr5GwYAAJaWmklq3HjjjcVUU3vvvXfce++98bnPfa6o3Bg4cGCR0Ej9+/cvqjWWVPfuXZZBi2HZW2+99WLYsD2KR7nf/OaOePnll4vnPXr4+waohKlTOxfblVbq7LsYAAAAaimpMXv27LjooouKCo3//Oc/cccdd8Rll10Wm266afTp06f+ujZt2kRdXV1Mnz49unbtutivP2XKzLAUB9UoE31vvjlzgeOlhEZq6jwAy960abPrt76LaQ2VGgYCAQAAS0NNJDXuueeeeOedd+Laa6+NlVdeOd5///3Yddddi3U09tprrwbXduzYMebMmbNESY1MaEhqUE2OPfbE+imorr768rj77l/Xn9tpp50bXOdvG6AySt+/+hkAAABQYwuFv/HGG8U0U5nQSO3atSummpoxY0ZMnTp1gaqO9u3bV6il0Dw+97n/WyejPKHReL/8OgAAAACASquJpMYqq6wS7777boNjOQ3Vd7/73Xjqqafqj02YMCHmzp27RFUaAAAAAABA86iJpMbWW28dL7zwQrFYeFZtXHfddcUi4V/4whdi1qxZxTRUadSoUTFkyJBo27ZtpZsMy9Qf/3hv/fMvfnG3BufK98uvAwAAAACotJpYU6Nbt25xxRVXxDnnnBNnnXVW9OzZMy644IJYddVVY+TIkXHCCScU53KR8NGjR1e6ubDMXXrpBfXPDznkm8Wj3G9/e2f9ddtuu32ztw8AAAAAoGaTGmmTTTaJm266aYHj22+/fbGQ+NNPP12su5EJEKgVffr0a/L4qquuFq+//p9mbw8AAAAAwKLUTFJjUbJyY5tttql0M6DZvfrq+Bg79o647rqr6o8dcMA3JDQAgBbr2muvjbfffjuOOOKISjcFAACogJpYUwNo6Mgjj6t/Xp7QaLxffh0AQKW9+uqrxTSy7777bqWbAgAAVIikBtSgxV0nw3oaAEBLMXXq1Dj00EOLaWVXWGGF+M1vfhNPPvlkvP766/HBBx9UunkAAEAzMf0U1KCccmpxr9t1192XeXsAABZl3Lhxcdxxx8Wqq65aVGpsuumm8alPfSomTZoUkydPjnnz5kW/fv3ihz/8YQwePLjSzQUAAJYhlRpQgxY1xdSipqYCAGhOb731VowcOTL222+/+NKXvhRXX311dOrUqTh3ww03xB/+8If4xz/+Effff3988YtfjHPPPbfSTQYAAJYxlRpQw1ZeuXsxxVTjaaZuuOHnMW3atIq1CwAgzZ8/P+bMmRN33XVXrLbaasWxnGqqTZs2Da7r3r177LDDDvHwww9XqKUAAEBzkdSAGjZ16pT49a/Hxs9/fkX9sYMOOlRCAwBoEVZeeeWiUqMpjRMb/fv3jxtvvLGZWgYAAFSK6aegBh1wwDfqn5cnNBrvl18HAFAJf/3rX+O9995boILjs5/9bHz1q1+Nyy+/vFhEvF0747UAAKAWSGpADVrcxb8tEg4AVFIuAH7KKafEjjvuGHfeeWd9QiOrNK699toYNmxYPPbYY7HTTjvFE088UenmAgAAzUBSA2pQTjm1NK8DAFgW6urqimTGMcccE+eff35RmfHvf/+7SGyss846scceexSLh59wwglx2GGHxbhx4yrdZAAAYBmT1IAatKgpphY1NRUAQHPLaaX23HPPGDt2bKyxxhqx3377FetsdOzYsf6avffeOw444IAYMWJEUd0BAAC0XiaehRrWvXuPYoqpxtNMjR07Jt56662KtQsAoLEVVlgh/ud//ic++clPxhlnnBGbb755rL766vXnDz300HjyySdj1qxZseKKK1a0rQAAwLIjqQE1bMqUN5s8LqEBALRU+++/f2y88cYNEhopKzdyKqpcbwMAAGi9qmL6qZkzZ1a6CdCqHHTQofXP//KX+xqcK98vvw4AoKUYOHBgTJgwYYHjEhoAAND6tfikxt///vf48pe/HO+++25MmjSp0s2BVmHnnXetf37BBT+JYcN2jUMPPaDY5n5T1wEANLe5c+fGeeedVzz/05/+FE888UTxPBcK33nnnSvcOgAAoBJadFIjkxjf/va3i0eWk48ePTq+9rWvxb/+9a/6a3LOXGDJ3XLL2EVOOdX4PABAJdxwww3F9pFHHomDDjqoWBD8n//8Z7Rv377STQMAACqgxa6p8de//jV++tOfxplnnhkbbbRRPP7448Xx9ddfPw4//PDYeuutY5tttomzzjor7rrrrujQoUOlm0wVmDjxjZg9WyKs5Oyzz4/77vtD/OY3/5fA+NKXdo2tt94uXnrphYq2raXp3HmF6N17lUo3AwBqSvbx27ZtWzz/zne+E0cddVTccccdMWXKlPrjAABAbWlxSY05c+YUpeSZyMiF/nL/S1/6UpHMWHPNNYvnw4cPjxEjRhSPK664QkKDxTJjxvQ45pjDYv78eZVuSouWCY7yJAf/VVdXF1deeV2suGLXSjcFAGrKe++9F7/61a/q9zt37lxUmDY+nlNS5ZS1++yzT4VaCgAA1GRSIxf3O/HEE+P+++8vApJ99923SFystdZace655xbXvP766/GPf/yjSHpsuOGGlW4yVSJvRl900SiVGk147bUJceGF58Wxxw6P1VfvW+nmtNhKDQkNAKjMuhq//e1vFzieSY3y4x988IGkBgAA1IAWl9TItTN22mmn4jFx4sRi+qnrr78+fvKTnxSjr+bNmxfLLbdcnHPOORIaLDHTBy1aJjTWXnvdSjcDAKBBZcbll1/eIMmRCY1tt922wXEAAKA2tNiFwh9++OHo3bt3McXUoEGDijU2tttuu+jSpUu88847sdJKK8Wzzz5bVGwAAACtTw5oKsl1NM4777zYaqut4je/+U0x4Onj+vGPf1ys11fy3HPPxdChQ4v44+yzz27wOx599NFi4NXgwYPjmmuu+di/GwAAaGVJjdNPP73YZjJj+eWXjzXWWCMuvfTSuOCCC4pzGdAcfPDBcckll1S6qQAAwDKQ00m9//77xfObb745Xn311WJq2kw85ECnNHny5I/02uPGjYsbbrghvve979VXgGSCY8CAATFmzJh48cUX47bbbivOTZ06NY444ohi7b+bbropxo4dWwzCAgAAml+LTGrkVFMzZ86Miy++OH7/+99Hu3b/nSXrRz/6UbGAeC4MmOtu5Aiq0047rdLNBQAAloFOnTrF448/XjzPpEIObNp4442Ldfiyojunq919992LgVBLWgFyyimnxEEHHRR9+/53PbFc02/WrFnF6/br1y+GDx8et956a3HuzjvvjF69esVRRx0Va665Zhx55JH15wAAgObVIpMaGUSccMIJRYDRvXv34tjtt99eLBr+85//PA444IDo06dPrLrqqkUZOgAA0Do988wzRWXGf/7zn2L/3nvvjT//+c9FJffzzz8fX/nKV4pkwwsvvLDYr3njjTcWU02tvvrqxetllUZWbgwcOLBIpKT+/fsX1Ropp73NaacymZIysfL0008v8XvJH28pD4BKqfT3X0t+AFRKmyr7LmxxC4WnvffeO377299G+/bti0XBM3jIhcFTBh+/+MUv4sknnywCjRydtcEGG1S6yQAAwFKWUzxlVURWaay22mrFsUxgfPazn22w3kW3bt3i29/+djEQ6sPMnj07LrroomIAVSZK7rjjjrjsssti0003LQZOlWQMUldXF9OnTy8qONZZZ536cyussEJMmjRpid9P9+5doiWYOrVzpZsA1LCVVuocPXq0jO/DlsR3M1BJK1XZd3OLTGpkgPLggw9Ghw4ditFROWLqqquuiquvvrpYX6Nt27bF3Lp/+tOf4thjj610cwEAgGVUwX3++efH1ltvXX8sEw3XXXddg+vee++9mDZt2mK95j333FOsx3HttdfGyiuvXMQVu+66a7GOxl577dXg2o4dO8acOXOK+CNjk8bHl9SUKTNjKaxv/rFNmza70k0Aalh+B7355sxKN6PF8d0MVNK0FvLdnJUaizMQqEUmNXJUVGmh8KzImDFjRhHIfOMb3ygSHv/4xz+Kkm8AAKD1yuqMUoVGymmiPv3pTy9wXVZ49+zZc7Fe84033igGTWVCI+X6fVkB/tJLLxULgjeu6sjX7tq1a4NzpeNLKhMaLSGp0RLaANSulvJd2NL4TIBKml9l380tck2NdM011xTbDFrKR2blKKzvfOc7FWwZAABQCVktcf3113+s11hllVXi3XffbXAsp6H67ne/G0899VT9sQkTJhRJlExobLTRRg3O5TofvXv3/ljtAAAAWllSY/To0fXPM5jYb7/9iuc5IirLvwEAAJZUDpjKRcVzsfCs2siprHKR8C984QvF2hk5DVUaNWpUDBkypIg9tttuu3jiiSeKKXJzkFVOjbvllltW+q0AAEBNapHTT5XmqS3JQKJU7p1TU+UDAABo/YYNG1ZUaCxqYFPGB2uvvXZR0b3ccsst8vVyUfErrrgizjnnnDjrrLOKaasuuOCCWHXVVWPkyJFxwgknFOdy7Y7SQKucqmrEiBFx6KGHFmv8denSpfhZAACg+bXYpEbObVuSAUz5PgAAUBtee+21OPXUU4u19c4+++w4+eSTi+e5Bl8eTx988EFcfPHF8bvf/S523333D33NTTbZJG666aYFjm+//fbFQuJPP/10se5GJkBK9t1336I6I9fe2HTTTaNz585L+Z0CAACLo8VmCjIwefjhh4uAJb3zzjv1+6XneU2Wf2+zzTaVbi4AALAMZOXFjjvuWDy/5JJL6p9nNUXpecrK7qy2+LiycmNh8UXfvn2LBwAAUDktNqmRyYryku4333yzfn/y5MnF80xq5HobkhoAANA6LWzq2cbHv/a1rzVTiwAAgEpq15JHZP3qV7+q3991113r98ufAwAArdfs2bOLxbuzYnvGjBlx6623Fsfffvvt+ud5LgdFfe5zn1NJAQAArVyLS2rcd999xRy3AAAAc+bMKda5SLNmzYrf//73xfOckrb0PJMa77//fqy33nqSGgAA0Mq1uKTGpZdeWiwG2Ni7774bzzzzTMybN68i7QIAAJrfyiuvHJdffnl9xXbp+XbbbVf/HAAAqB110cJcf/31MWLEiOjWrVvstdde8eCDD9af+853vhPf+973Kto+AACg5a2pAQAA1IYWV6nRrl272HnnneNLX/pS/OIXv4hvfetbxXRUP/vZz6JPnz5FpUaeBwAAWr+5c+fWV2yXP881NP7zn//EaqutVukmVq26OdMr3QSghvjOAaDVJjXKR17tv//+scMOOxSJjfHjxxdJjZwrNx8AAEDrt9xyy8VJJ51UxAedOnUqqrozqZHHd9999+KazTffvHieU1Kp4PhwXbqsGO07dIh46b5KNwWoMfndk99BANAqkxolq666ajElVfv27Yv9tm3bxre//e1KNwsAAGgGpUXCF+aNN94orjnnnHPiiSeeECsshp49e8VPL7gsZs6cUemmUIVee21CXHjheXHsscNj9dX7Vro5VJlMaOR3EAC06qRGKiU0SkmNL3zhCxVtDwAA0Hyeeuqp+NSnPtXkuVVWWaWo8N5nn31ixgw36RdX3lR0Y5GPIxMaa6+9bqWbAQDUoBa3UDgAAEC5Y445ptjecsstxfbGG28stn/961/jwQcfjIceeigee+yx+Pvf/17RdgIAAMteVVRqAAAAtSurtdOVV14Zw4YNi8suuyz23XffOPPMM6Nnz57FuYcffjg222yz2HbbbSvcWgAAYFmS1AAAAFq0urr/Fph37NhxgelpL7/88mK79dZb1z8HgGpVN2d6pZsA1JC6Kv3OkdQAAABapPfee69YT6OkXbt2Dbbl2rRp06xtA4ClvYh6+w4dIl66r9JNAWpM+w4diu+gaiKpAQAAtEjf+973Yu7cufUJi/nz5zfYAkBr0bNnr/jpBZfFzJkzKt0UqtBrr02ICy88L449dnisvnrfSjeHKtOly4rFd1A1kdQAAABapJEjR0aHDh1iu+22K/Y/+OCDYiupAUBrlDcVq+3GIi1LJjTWXnvdSjcDlrn/Tk4LAADQwmRCozyJ8e677zbYphEjRhSP6dOnF1sAAKB1U6kBAMvY5MmTlJHzkUrIy7dQC2Xki1pbI+28887Fds899yy2hxxySMycObNYSPxTn/pUMVUVAADQuklqAMAyTmh867gj4j032viIcm5c+KgL/uXc3NWc2HjkkUdixRVXLNbWSN/61reK7fHHH19sv/SlL1W0fQAAQPOT1ACAZSgrNDKh8c7aW8e85bpWujlAjaibMz3ipfuK76BqTmr85S9/iZ/97Gfx6U9/Oh588MFFXjtv3rx4//334+yzz2629gEAAM1PUgMAmkEmNOZ17lHpZgBUleHDh8c3vvGNuOuuu2L06NExfvz42G233WLw4MENFgvP5zn11LRp0yraXgAAYNmrmaTGyJEji0CopF+/fnHPPffEc889VywomAHSl7/85TjppJOiTZs2FW0rAADwXzn91H777Rf77rtv3HHHHXHuuecWx88888xKNw0AAKiAmklq/POf/4wrrriiKF1PuZhgjuY6/PDDY8stt4zzzz+/SHzcdtttMXTo0Eo3FwAAKJMDj/bYY4/YdtttY8qUKZVuDgAAUCE1kdTIuXWff/752HTTTaNz5871x3//+9/HrFmzikqNTp06FeXtp5122hInNRR2UO1Kf8O59fcMS5f/poBKain/b1+abejatWvxAAAAalNNJDVyiqlcODBHdk2cODEGDRoUZ5xxRowbNy4GDhxYJDRS//7948UXX1zi1+/evcsyaDU0n6lT/5vsW2mlztGjh79nWBb/fQFUgv+3AwAArU1NJDVeeOGFWGutteIHP/hBdOvWrZh/N59/4hOfiD59+jQoac9pqaZPn75Eo7+mTJkZZesUQtWZNm12/fbNN2dWujnQKv/7AqiElvL/9qzUMBAIAABYGmoiqbHbbrsVj5JTTz01tt9++1hnnXWiQ4cODa7t2LFjzJkzZ4mSGpnQkNSgmpX+fv0tw9Lnvymgkvy/HQAAaG3qogZ17969mI6qR48eMXXq1AbnZs+eHe3bt69Y2wAAAAAAgBpOapx99tkxduzY+v0nn3yymGYq19B46qmn6o9PmDAh5s6da+FBAAAAAABogWpi+qn1118/LrjggqIy44MPPigWCc9Fw7fYYouYNWtWjBkzJoYOHRqjRo2KIUOGRNu2bSvdZAAAAAAAoBaTGrvvvnuxWPgxxxxTJCx23XXXGD58eLRr1y5GjhwZJ5xwQpxzzjlF9cbo0aMr3VwAAAAAAKBWkxopExf5aCwXDL/nnnvi6aefjoEDB0a3bt0q0j4AAAAAAGDRaiapsSg9e/aMbbbZptLNAAAAAAAAan2hcAAAAAAAoPpJagAAAAAAAFVBUgMAAAAAAKgKkhoAAAAAAEBVkNQAAAAAAACqgqQGAAAAAABQFSQ1AAAAAACAqiCpAQAAAAAAVAVJDQAAAAAAoCpIagAAAAAAAFVBUgMAAAAAAKgKkhoAAAAAAEBVkNQAAAAAAACqgqQGAAAAAABQFSQ1AAAAAACAqiCpAQAAAAAAVAVJDQAAAAAAoCpIagAAAAAAAFVBUgMAAAAAAKgKkhoAAAAAAEBVkNQAAAAAAACqgqQGAAAAAABQFdpVugEAUAvq3plW6SYANcR3DgAA0FpJagBAM+j08v2VbgIAAABA1ZPUAIBm8M5aW8W8TitVuhlADVVqSKYCAACtkaQGADSDTGjM69yj0s0AAAAAqGoWCgcAAAAAAKqCpAYAAAAAAFAVJDUAAAAAAICqIKkBAAAAAABUBUkNAAAAAACgKkhqAAAAAAAAVUFSAwAAAAAAqAqSGgAAAAAAQFWQ1AAAAAAAAKqCpAYAAAAAAFAVJDUAAAAAAICqIKkBAAAAAABUBUkNAAAAAACgKkhqAAAAAAAAVUFSAwAAAAAAqAqSGgAAAAAAQFWQ1AAAAAAAAKqCpAYAAAAAAFAVJDUAAAAAAICqIKkBAAAAAABUBUkNAAAAAACgKrSrdANYuiZPnhQzZ86odDOoMq+9NqHBFpZUly4rRs+evSrdDAAAAABaOUmNVpbQ+NZxR8R7c+dWuilUqQsvPK/STaBKte/QIX56wWUSGwAAAAAsU5IarUhWaGRC4521t455y3WtdHOAGlE3Z3rES/cV30GSGgAAAAAsS5IarVAmNOZ17lHpZgAAQIs0cuTIGD16dP1+v3794p577onnnnsuRowYEePHj48vf/nLcdJJJ0WbNm2Kax599NE49dRTY+rUqXH44YfHwQcfXMF3AAAAtasmFwo/5JBD4rbbbqsPTnbaaacYPHhwXHPNNZVuGgAAsIz985//jCuuuCIee+yx4nH77bfH3Llzi2TFgAEDYsyYMfHiiy/WxwyZyDjiiCNi5513jptuuinGjh0bDz/8cKXfBgAA1KSaq9S4884744EHHigCklJwkqOsdtlllxg+fHh88pOfjM0226zSzQQAAJaB999/P55//vnYdNNNo3PnzvXHf//738esWbOKSo1OnToVscFpp50WQ4cOLWKIXr16xVFHHVVUbhx55JFx6623LnHc8L9FH1DVSn/HufU3DdAy+G6mtVjcv9+aSmpMmzYtzj777FhrrbWK/aUVnAAAANUhp5iaN29e7LHHHjFx4sQYNGhQnHHGGTFu3LgYOHBgkdBI/fv3L6o10rPPPltUdpemotp4443j3HPPXeLf3b17l6X8bqD5TZ3632TgSit1jh49/E0DtAS+m6k1NZXUyITGDjvsEO++++5SDU5aSga0pbQDqE1GhDTNZwJUUkv5bm4JbSh54YUXikFOP/jBD6Jbt25x5plnFs8/8YlPRJ8+feqvyxihrq4upk+fXlRwrLPOOvXnVlhhhZg0adIS/+4pU2bG/PlL7a1ARUybNrt+++abMyvdHAB8N9OKZNywOAOBaiapkXPePvTQQ3HXXXcVCwOmpRWctJQRV6WsLEAlGBHSNN/NQCX5bl7QbrvtVjxKcvHv7bffvogLOnTo0ODajh07xpw5c6Jt27YNzpWOL6lMaEhqUO1Kf8P+ngFaDt/N1JqaSGpkZUYGKz/84Q+LxEXJ0gpOWsqIq1JWFqASjAhpmu9moJJaynfz4o64qoTu3bsX01H16NGjWGuj3OzZs6N9+/bRtWvXYj2+xscBAIDmVxc14NJLL40NN9wwttlmmwbHl1ZwUsqCtoQHQKVU+vuvJT8AKqXS338t8bswp6QdO3Zs/f6TTz5ZTDOVa2g89dRT9ccnTJgQc+fOLWKGjTbaqMG5Z555Jnr37t3sbQcAAGqkUiODlrfeeis23XTTYj+rMe6+++7i+ac//en66wQnAADQuq2//vpxwQUXFJUZH3zwQbFIeC4avsUWWxTT044ZMyaGDh0ao0aNiiFDhhTV3dttt12cfvrp8eCDDxYLi1911VWx5ZZbVvqtAABATaqJpMYNN9wQ77//fv3+OeecEwMHDow999yzqN4QnAAAQG3Yfffdi8XCjznmmCJhseuuu8bw4cOjXbt2xdp7J5xwQhEvZPXG6NGji59ZeeWVY8SIEXHooYfG8ssvH126dImzzjqr0m8FAABqUk0kNVZZZZUG+xmIdOvWTXACAAA1KBMX+WgsFwy/55574umnny4GQWXMULLvvvsWA6BeeumlogK8c+fOzdxqAACgZpIajZUnLgQnAABASc+ePRdYi6+kb9++xQMAAKicmkxqNCY4AQAAAACAlq+u0g0AAAAAAABYHJIaAAAAAABAVZDUAAAAAAAAqoI1NQCgGdTNmV7pJgA1xHcOAADQWklqAMAy1KXLitG+Q4eIl+6rdFOAGpPfPfkdBAAA0JpIagDAMtSzZ6/46QWXxcyZMyrdFKrMa69NiAsvPC+OPXZ4rL5630o3hyqUCY38DgIAAGhNJDUAYBnLm4puLPJRZUJj7bXXrXQzAAAAoEWwUDgAAAAAAFAVJDUAAAAAAICqIKkBAAAAAABUBUkNAAAAAACgKkhqAAAAAAAAVUFSAwAAAAAAqAqSGgAAAAAAQFWQ1AAAAAAAAKqCpAYAAAAAAFAV2lW6ASx9de9Mq3QTgBriOwcAAACA5iKp0Qp1evn+SjcBAAAAAACWOkmNVuidtbaKeZ1WqnQzgBqq1JBMBQAAAKA5SGq0QpnQmNe5R6WbAQAAAAAAS5WFwgEAAAAAgKogqQEAAAAAAFQFSQ0AAAAAAKAqSGoAAAAAAABVQVIDAAAAAACoCpIaAAAAAABAVZDUAAAAAAAAqoKkBgAAAAAAUBUkNQAAAAAAgKogqQEAAAAAAFQFSQ0AAAAAAKAqSGoAAAAAAABVQVIDAAAAAACoCpIaAAAAAABAVZDUAAAAAAAAqoKkBgAAAAAAUBUkNQAAAAAAgKogqQEAAAAAAFQFSQ0AAAAAAKAqSGoAAAAAAABVQVIDAAAAAACoCpIaAAAAAABAVZDUAAAAAAAAqoKkBgAAAAAAUBUkNQAAAAAAgKogqQEAAAAAAFQFSQ0AAAAAAKAqtKt0A1j66uZMr3QTgBriOwcAAACA5iKp0Yp06bJitO/QIeKl+yrdFKDG5HdPfgcBAAAAwLIkqdGK9OzZK356wWUxc+aMSjeFKvPaaxPiwgvPi2OPHR6rr9630s2hCmVCI7+DAAAAAGBZqqmkxowZM+Lll1+ONddcM7p27RqtUd5UdGORjyoTGmuvvW6lmwEAAAAAUNsLhd99992x3Xbbxfe///3Yeuuti/303HPPxdChQ2PQoEFx9tlnx/z58yvdVAAAAAAAoFaTGjNnzozTTjstfvGLX8TYsWPjlFNOiR//+Mcxd+7cOPzww2PAgAExZsyYePHFF+O2226rdHMBAAAAAIBaTWrMmjUrvvvd78b6669f7G+wwQbx1ltvxf3331+cGzFiRPTr1y+GDx8et956a6WbCwAAAAAA1OqaGquuumrstttuxfP33nsvrr322vj85z8f48aNi4EDB0anTp2Kc/379y+qNZZUmzZLvcnQrEp/w7n19wzQMvhupjXxNwwAACwtNZHUKMkkxoEHHhjt27eP3/zmN3HppZdGnz596s+3adMm6urqYvr06Uu0kHj37l2WUYuheUyd2rnYrrRS5+jRw98zQEvguxkAAABqPKmRlRhXX311nHnmmcWC4X379o0OHTo0uKZjx44xZ86cJUpqTJkyM6wvTjWbNm12/fbNN2dWujkA+G6mFVZqGAgEAAAsDTWV1MhKjA033DDOOuusYvqpXEPj+eefb3DN7Nmzi0qOJZEJDUkNqlnp79ffMkDL4bsZAAAAanSh8EcffTTOPvvs+v2szsgExzrrrBNPPfVU/fEJEybE3Llzl6hKAwAAAAAAaB41kdRYc8014+abb46bbropXn/99TjvvPNiiy22iK233jpmzZoVY8aMKa4bNWpUDBkyJNq2bVvpJgMAAAAAALWY1OjVq1dceOGFcd1118XOO+8c77zzTpxzzjnRrl27GDlyZJxxxhkxePDguPfee+PEE0+sdHMBAAAAAIBaXlMjKzN+/etfL3B8++23j3vuuSeefvrpGDhwYHTr1q0i7QMAAAAAABatZpIai9KzZ8/YZpttKt0MAAAAAACg1qefAgAAAAAAqp+kBgAAAAAAUBUkNQAAAAAAgKogqQEAAAAAAFQFSQ0AAAAAAKAqSGoAAAAAAABVQVIDAAAAAACoCpIaAAAAAABAVZDUAAAAAAAAqoKkBgAAAAAAUBUkNQAAAAAAgKogqQEAAAAAAFQFSQ0AAAAAAKAqSGoAAAAAAABVQVIDAAAAAACoCu0q3QAAAABoSSZOfCNmz55V6Wa0SK+9NqHBloY6d14hevdepdLNAIBWTVIDAAAA/teMGdPjmGMOi/nz51W6KS3ahReeV+kmtEh1dXVx5ZXXxYordq10UwCg1ZLUAAAAatYhhxwSO++8c+y1117x6KOPxqmnnhpTp06Nww8/PA4++*********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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 5}, {"metadata": {}, "cell_type": "markdown", "source": ["# **结论：**\n", "# - 流失客户的年龄通常高于非流失客户，表明年龄较大的客户更容易流失\n", "# - 流失客户的信用评分略低于非流失客户\n", "# - 流失客户的账户余额通常高于非流失客户，这可能表明高余额客户对服务要求更高\n", "# - 客户存续时间与流失关系不明显，两组客户的存续时间分布相似，表明客户在银行的存续年限可能不是影响流失的主要因素\n"], "id": "9fea6a2a985507d7"}, {"metadata": {}, "cell_type": "markdown", "source": "# ### 1.4 变量相关性分析", "id": "5a1498334e8ffee4"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:44:46.771549Z", "start_time": "2025-06-20T08:44:46.462952Z"}}, "cell_type": "code", "source": ["# 选择数值型变量进行相关性分析\n", "numeric_columns = ['CreditScore', 'Age', 'Tenure', 'Balance', 'NumOfProducts', \n", "                   'HasCrCard', 'IsActiveMember', 'EstimatedSalary', 'Exited', \n", "                   'Co<PERSON><PERSON>', 'Satisfaction Score', 'Point Earned']\n", "\n", "# 计算相关系数矩阵\n", "corr_matrix = churn_data[numeric_columns].corr()\n", "\n", "# 绘制热力图\n", "plt.figure(figsize=(12, 10))\n", "sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', fmt='.2f')\n", "plt.title('变量相关性热力图')\n", "plt.show()\n", "\n", "# 各变量与流失的相关性\n", "print(\"\\n各变量与流失(Exited)的相关性：\")\n", "print(corr_matrix['Exited'].sort_values(ascending=False))\n"], "id": "d9d0a71478b9d5d3", "outputs": [{"data": {"text/plain": ["<Figure size 1200x1000 with 2 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "各变量与流失(Exited)的相关性：\n", "Exited                1.000000\n", "Complain              0.995693\n", "Age                   0.285296\n", "Balance               0.118577\n", "EstimatedSalary       0.012490\n", "Point Earned         -0.004628\n", "Satisfaction Score   -0.005849\n", "HasCrCard            -0.006976\n", "Tenure               -0.013656\n", "CreditScore          -0.026771\n", "NumOfProducts        -0.047611\n", "IsActiveMember       -0.156356\n", "Name: Exited, dtype: float64\n"]}], "execution_count": 6}, {"metadata": {}, "cell_type": "markdown", "source": ["# **结论：**\n", "# - 投诉(Co<PERSON>lain)与流失呈较强正相关，表明有投诉的客户更容易流失\n", "# - 年龄(Age)与流失呈正相关，表明年龄较大的客户更容易流失\n", "# - 满意度评分(Satisfaction Score)与流失呈负相关，表明满意度低的客户更容易流失\n", "# - 是否为活跃会员(IsActiveMember)与流失呈负相关，表明不活跃的客户更容易流失\n"], "id": "8649f9fa55bbd597"}, {"metadata": {}, "cell_type": "markdown", "source": "# ## 2. 数据预处理", "id": "a34264c41a9d1cbd"}, {"metadata": {}, "cell_type": "markdown", "source": "# ### 2.1 检查缺失值", "id": "497b0e669158596b"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:45:18.881071Z", "start_time": "2025-06-20T08:45:18.870197Z"}}, "cell_type": "code", "source": ["# 检查缺失值\n", "print(\"缺失值检查：\")\n", "print(churn_data.isnull().sum())\n"], "id": "ae564ddb5e401a81", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["缺失值检查：\n", "RowNumber             0\n", "CustomerId            0\n", "Surname               0\n", "CreditScore           0\n", "Geography             0\n", "Gender                0\n", "Age                   0\n", "Tenure                0\n", "Balance               0\n", "NumOfProducts         0\n", "HasCrCard             0\n", "IsActiveMember        0\n", "EstimatedSalary       0\n", "Exited                0\n", "Complain              0\n", "Satisfaction Score    0\n", "Card Type             0\n", "Point Earned          0\n", "dtype: int64\n"]}], "execution_count": 7}, {"metadata": {}, "cell_type": "markdown", "source": "# **结论：** 数据集中没有缺失值，不需要进行缺失值处理。\n", "id": "6df329a646c15359"}, {"metadata": {}, "cell_type": "markdown", "source": "# ### 2.2 处理分类变量", "id": "803047e932c446d8"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:45:25.815588Z", "start_time": "2025-06-20T08:45:25.755081Z"}}, "cell_type": "code", "source": ["# 查看分类变量的唯一值\n", "print(\"Geography唯一值：\", churn_data['Geography'].unique())\n", "print(\"Gender唯一值：\", churn_data['Gender'].unique())\n", "print(\"Card Type唯一值：\", churn_data['Card Type'].unique())\n", "\n", "# 创建副本以避免修改原始数据\n", "processed_data = churn_data.copy()\n", "\n", "# 对Geography进行独热编码\n", "geography_dummies = pd.get_dummies(processed_data['Geography'], prefix='Geography')\n", "processed_data = pd.concat([processed_data, geography_dummies], axis=1)\n", "\n", "# 对Gender进行映射（1=Male, 0=Female）\n", "processed_data['Gender'] = processed_data['Gender'].map({'Male': 1, 'Female': 0})\n", "\n", "# 对Card Type进行独热编码\n", "card_dummies = pd.get_dummies(processed_data['Card Type'], prefix='Card')\n", "processed_data = pd.concat([processed_data, card_dummies], axis=1)\n", "\n", "# 查看处理后的数据\n", "print(\"处理后的数据前5行：\")\n", "print(processed_data.head())\n"], "id": "a5a5c77db93e11f4", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Geography唯一值： ['France' 'Spain' 'Germany']\n", "Gender唯一值： ['Female' 'Male']\n", "Card Type唯一值： ['DIAMOND' 'GOLD' 'SILVER' 'PLATINUM']\n", "处理后的数据前5行：\n", "   RowNumber  CustomerId   Surname  CreditScore Geography  Gender  Age  \\\n", "0          1    15634602  Hargrave          619    France       0   42   \n", "1          2    15647311      Hill          608     Spain       0   41   \n", "2          3    15619304      Onio          502    France       0   42   \n", "3          4    15701354      Boni          699    France       0   39   \n", "4          5    15737888  Mitchell          850     Spain       0   43   \n", "\n", "   Tenure    Balance  NumOfProducts  ...  Satisfaction Score  Card Type  \\\n", "0       2       0.00              1  ...                   2    DIAMOND   \n", "1       1   83807.86              1  ...                   3    DIAMOND   \n", "2       8  159660.80              3  ...                   3    DIAMOND   \n", "3       1       0.00              2  ...                   5       GOLD   \n", "4       2  125510.82              1  ...                   5       GOLD   \n", "\n", "   Point Earned  Geography_France  Geography_Germany  Geography_Spain  \\\n", "0           464              True              False            False   \n", "1           456             False              False             True   \n", "2           377              True              False            False   \n", "3           350              True              False            False   \n", "4           425             False              False             True   \n", "\n", "  Card_DIAMOND  Card_GOLD  Card_PLATINUM  Card_SILVER  \n", "0         True      False          False        False  \n", "1         True      False          False        False  \n", "2         True      False          False        False  \n", "3        False       True          False        False  \n", "4        False       True          False        False  \n", "\n", "[5 rows x 25 columns]\n"]}], "execution_count": 8}, {"metadata": {}, "cell_type": "markdown", "source": ["# **结论：**\n", "# - 对地理位置变量(Geography)进行了独热编码，转换为Geography_France、Geography_Germany和Geography_Spain\n", "# - 对性别变量(Gender)进行了二元编码，Male=1，Female=0\n", "# - 对卡类型变量(Card Type)进行了独热编码，转换为Card_DIAMOND、Card_GOLD、Card_PLATINUM和Card_SILVER\n", "# - 这些转换使得分类变量可以被机器学习模型使用\n"], "id": "8c6005cab839b012"}, {"metadata": {}, "cell_type": "markdown", "source": "# ### 2.3 特征工程", "id": "93b3bc72c20873ca"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:45:48.352039Z", "start_time": "2025-06-20T08:45:48.323862Z"}}, "cell_type": "code", "source": ["# 创建新特征：余额收入比\n", "processed_data['余额收入比'] = processed_data['Balance'] / processed_data['EstimatedSalary']\n", "# 处理可能的无穷大或NaN值\n", "processed_data['余额收入比'] = processed_data['余额收入比'].replace([np.inf, -np.inf], 0).fillna(0)\n", "\n", "# 创建新特征：积分收入比\n", "processed_data['积分收入比'] = processed_data['Point Earned'] / processed_data['EstimatedSalary']\n", "processed_data['积分收入比'] = processed_data['积分收入比'].replace([np.inf, -np.inf], 0).fillna(0)\n", "\n", "# 创建新特征：投诉满意度比\n", "processed_data['投诉满意度比'] = processed_data['Complain'] / processed_data['Satisfaction Score']\n", "processed_data['投诉满意度比'] = processed_data['投诉满意度比'].replace([np.inf, -np.inf], 0).fillna(0)\n", "\n", "# 创建新特征：年龄与存续时间的交互项\n", "processed_data['年龄存续交互'] = processed_data['Age'] * processed_data['Tenure']\n", "\n", "# 显示新特征的统计信息\n", "print(\"\\n新特征的统计信息：\")\n", "new_features = ['余额收入比', '积分收入比', '投诉满意度比', '年龄存续交互']\n", "print(processed_data[new_features].describe())\n"], "id": "e3b68302efb6209e", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "新特征的统计信息：\n", "              余额收入比         积分收入比        投诉满意度比        年龄存续交互\n", "count  10000.000000  10000.000000  10000.000000  10000.000000\n", "mean       3.878703      0.028107      0.092458    194.804000\n", "std      108.337260      0.292558      0.223156    127.939996\n", "min        0.000000      0.001128      0.000000      0.000000\n", "25%        0.000000      0.003799      0.000000     90.000000\n", "50%        0.747002      0.006044      0.000000    180.000000\n", "75%        1.514022      0.012041      0.000000    280.000000\n", "max    10614.655440     21.934370      1.000000    880.000000\n"]}], "execution_count": 9}, {"metadata": {}, "cell_type": "markdown", "source": ["# **结论：**\n", "# - 创建了四个新特征：余额收入比、积分收入比、投诉满意度比和年龄存续交互\n", "# - 这些新特征可能会提供额外的信息，帮助提高模型的预测能力\n", "# - 特别是余额收入比和投诉满意度比，可能与客户流失有更强的相关性\n"], "id": "68c52297fc4acc4b"}, {"metadata": {}, "cell_type": "markdown", "source": "# ### 2.4 特征选择", "id": "1420297b4cef5132"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:45:59.030883Z", "start_time": "2025-06-20T08:45:59.022892Z"}}, "cell_type": "code", "source": ["# 删除不需要的列（ID、行号、姓氏等）\n", "columns_to_drop = ['RowNumber', 'CustomerId', 'Surname', 'Geography', 'Card Type']\n", "processed_data = processed_data.drop(columns=columns_to_drop)\n", "\n", "# 查看处理后的数据集\n", "print(\"特征选择后的数据集形状：\", processed_data.shape)\n", "print(\"特征选择后的列名：\", processed_data.columns.tolist())\n"], "id": "cc4ba3a27bf9a615", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["特征选择后的数据集形状： (10000, 24)\n", "特征选择后的列名： ['CreditScore', 'Gender', 'Age', 'Tenure', 'Balance', 'NumOfProducts', 'HasCrCard', 'IsActiveMember', 'EstimatedSalary', 'Exited', 'Complain', 'Satisfaction Score', 'Point Earned', 'Geography_France', 'Geography_Germany', 'Geography_Spain', 'Card_DIAMOND', 'Card_GOLD', 'Card_PLATINUM', 'Card_SILVER', '余额收入比', '积分收入比', '投诉满意度比', '年龄存续交互']\n"]}], "execution_count": 10}, {"metadata": {}, "cell_type": "markdown", "source": ["# **结论：**\n", "# - 删除了对模型无用的特征，如行号(RowNumber)、客户ID(CustomerId)和姓氏(Surname)\n", "# - 删除了已经转换为独热编码的原始分类变量，如Geography和Card Type\n", "# - 最终得到了更加精简且信息丰富的特征集 "], "id": "5845932703b5903b"}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "## 3. 模型建立与评估", "id": "e776aa8cd21d1a81"}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:51:31.195352Z", "start_time": "2025-06-20T08:51:31.190200Z"}}, "cell_type": "code", "source": " ### 3.1 数据集划分", "id": "3396d4414cb96a30", "outputs": [], "execution_count": 11}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:51:45.311439Z", "start_time": "2025-06-20T08:51:44.893669Z"}}, "cell_type": "code", "source": ["# 导入必要的库\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.linear_model import LogisticRegression\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc, accuracy_score, precision_score, recall_score, f1_score\n", "from sklearn.model_selection import cross_val_score\n", "\n", "# 准备特征和目标变量\n", "X = processed_data.drop('Exited', axis=1)\n", "y = processed_data['Exited']\n", "\n", "# 划分训练集和测试集\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.3, random_state=42)\n", "\n", "print(\"训练集大小:\", X_train.shape)\n", "print(\"测试集大小:\", X_test.shape)"], "id": "127c61fb3b667a23", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["训练集大小: (7000, 23)\n", "测试集大小: (3000, 23)\n"]}], "execution_count": 12}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:51:54.933129Z", "start_time": "2025-06-20T08:51:54.920108Z"}}, "cell_type": "code", "source": "### 3.2 特征标准化", "id": "cdbf0c9852feaf22", "outputs": [], "execution_count": 13}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:52:03.701612Z", "start_time": "2025-06-20T08:52:03.668294Z"}}, "cell_type": "code", "source": ["# 标准化特征\n", "scaler = StandardScaler()\n", "X_train_scaled = scaler.fit_transform(X_train)\n", "X_test_scaled = scaler.transform(X_test)\n", "\n", "print(\"标准化前第一个样本:\", X_train.iloc[0].values[:5])\n", "print(\"标准化后第一个样本:\", X_train_scaled[0][:5])"], "id": "6f7de863a597882", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["标准化前第一个样本: [np.int64(619) np.int64(0) np.int64(32) np.int64(4) np.float64(175406.13)]\n", "标准化后第一个样本: [-0.34459497 -1.09823226 -0.65674999 -0.34217046  1.58372514]\n"]}], "execution_count": 14}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:52:31.542329Z", "start_time": "2025-06-20T08:52:31.537585Z"}}, "cell_type": "code", "source": ["# **结论：**\n", "# - 数据已成功划分为训练集和测试集，比例为7:3\n", "# - 特征已进行标准化处理，使所有特征的均值为0，标准差为1\n", "# - 标准化有助于提高模型的训练效果，特别是对于基于距离的算法"], "id": "a391e3b96584d90c", "outputs": [], "execution_count": 15}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:53:13.909164Z", "start_time": "2025-06-20T08:53:13.905144Z"}}, "cell_type": "code", "source": "### 3.3 建立逻辑回归模型", "id": "13f4fd18442f8056", "outputs": [], "execution_count": 16}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:56:37.308178Z", "start_time": "2025-06-20T08:56:36.893710Z"}}, "cell_type": "code", "source": ["# 建立逻辑回归模型\n", "lr_model = LogisticRegression(random_state=42, max_iter=1000)\n", "lr_model.fit(X_train_scaled, y_train)\n", "\n", "# 在测试集上进行预测\n", "y_pred_lr = lr_model.predict(X_test_scaled)\n", "y_prob_lr = lr_model.predict_proba(X_test_scaled)[:, 1]\n", "\n", "# 评估模型性能\n", "print(\"逻辑回归模型评估报告：\")\n", "print(classification_report(y_test, y_pred_lr))\n", "\n", "# 计算并打印主要评估指标\n", "lr_accuracy = accuracy_score(y_test, y_pred_lr)\n", "lr_precision = precision_score(y_test, y_pred_lr)\n", "lr_recall = recall_score(y_test, y_pred_lr)\n", "lr_f1 = f1_score(y_test, y_pred_lr)\n", "\n", "print(\"\\n逻辑回归主要评估指标:\")\n", "print(f\"准确率: {lr_accuracy:.4f} ({lr_accuracy*100:.2f}%)\")\n", "print(f\"精确率: {lr_precision:.4f} ({lr_precision*100:.2f}%)\")\n", "print(f\"召回率: {lr_recall:.4f} ({lr_recall*100:.2f}%)\")\n", "print(f\"F1分数: {lr_f1:.4f}\")\n", "\n", "# 计算混淆矩阵\n", "conf_matrix_lr = confusion_matrix(y_test, y_pred_lr)\n", "print(\"\\n混淆矩阵：\")\n", "print(conf_matrix_lr)\n", "\n", "# 计算ROC曲线和AUC\n", "fpr_lr, tpr_lr, _ = roc_curve(y_test, y_prob_lr)\n", "roc_auc_lr = auc(fpr_lr, tpr_lr)\n", "print(f\"\\n逻辑回归AUC: {roc_auc_lr:.4f}\")\n", "\n", "# 可视化混淆矩阵\n", "plt.figure(figsize=(8, 6))\n", "sns.heatmap(conf_matrix_lr, annot=True, fmt='d', cmap='Blues', \n", "            xticklabels=['未流失', '已流失'], \n", "            yticklabels=['未流失', '已流失'])\n", "plt.title('逻辑回归模型混淆矩阵')\n", "plt.xlabel('预测标签')\n", "plt.ylabel('真实标签')\n", "plt.show()\n", "\n", "# 查看特征重要性\n", "feature_importance_lr = pd.DataFrame({\n", "    '特征': X_train.columns,\n", "    '重要性': np.abs(lr_model.coef_[0])\n", "})\n", "feature_importance_lr = feature_importance_lr.sort_values('重要性', ascending=False)\n", "\n", "plt.figure(figsize=(12, 8))\n", "sns.barplot(x='重要性', y='特征', data=feature_importance_lr.head(15))\n", "plt.title('逻辑回归模型 - 前15个重要特征')\n", "plt.tight_layout()\n", "plt.show()\n"], "id": "ce508010e537edc6", "outputs": [{"name": "stdout", "output_type": "stream", "text": ["逻辑回归模型评估报告：\n", "              precision    recall  f1-score   support\n", "\n", "           0       1.00      1.00      1.00      2416\n", "           1       0.99      1.00      1.00       584\n", "\n", "    accuracy                           1.00      3000\n", "   macro avg       1.00      1.00      1.00      3000\n", "weighted avg       1.00      1.00      1.00      3000\n", "\n", "\n", "逻辑回归主要评估指标:\n", "准确率: 0.9987 (99.87%)\n", "精确率: 0.9949 (99.49%)\n", "召回率: 0.9983 (99.83%)\n", "F1分数: 0.9966\n", "\n", "混淆矩阵：\n", "[[2413    3]\n", " [   1  583]]\n", "\n", "逻辑回归AUC: 0.9991\n"]}, {"data": {"text/plain": ["<Figure size 800x600 with 2 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["<Figure size 1200x800 with 1 Axes>"], "image/png": "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"}, "metadata": {}, "output_type": "display_data"}], "execution_count": 18}, {"metadata": {"ExecuteTime": {"end_time": "2025-06-20T08:56:57.401946Z", "start_time": "2025-06-20T08:56:57.394531Z"}}, "cell_type": "code", "source": ["# **结论：**\n", "# - 逻辑回归模型在测试集上的准确率为{lr_accuracy*100:.2f}%\n", "# - 模型对流失客户的识别能力（召回率）为{lr_recall*100:.2f}%，说明模型能够捕捉到大部分真实流失客户\n", "# - 最重要的特征包括：{', '.join(feature_importance_lr['特征'].head(3).tolist())}\n", "# - 模型的AUC值为{roc_auc_lr:.4f}，表明模型具有良好的区分能力"], "id": "aa31a01f90830f", "outputs": [], "execution_count": 19}, {"metadata": {}, "cell_type": "code", "outputs": [], "execution_count": null, "source": "", "id": "e29ab9495e7cee41"}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 2}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython2", "version": "2.7.6"}}, "nbformat": 4, "nbformat_minor": 5}